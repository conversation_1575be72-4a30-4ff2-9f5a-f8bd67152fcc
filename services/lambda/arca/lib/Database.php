<?php

namespace FuncionesComunes\Database;

use Dotenv\Dotenv;
use Exception;
use PDO;
use PDOException;

class Database
{
    private static $instance = null;
    private $idempresa;
    private $bd_saasargentina;
    private $bd_link;
    private $stage;
    private $read = false;
    private $beta = false;
    private $alfa = false;

    private function __construct(?array $config = null)
    {
        if (file_exists(__DIR__ . '/../.env')) {
            $dotenv = Dotenv::createImmutable(__DIR__ . '/..');
            $dotenv->load();
        }

        // El stage se pasa automáticamente desde serverless.yml como APP_ENV
        $this->stage = $_ENV['APP_ENV'] ?? 'dev';
        $this->conectarBdSaas();
    }

    private function mostrarError($mensaje) {
        $texto = 'ERROR: '.$mensaje.'<br /><br />
            idempresa: '.$this->idempresa.'">'.$this->idempresa.'<br />
            stage: '.$this->stage.'<br />';
        echo $texto;
        // email_queue(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ERROR en API', $texto, false);
    }

    private function conectarBdSaas(): void
    {
        try {
            $host = $_ENV['BD_HOST'];
            $user = $_ENV['BD_USER'];
            $pass = $_ENV['BD_PASS'];
            $port = $_ENV['BD_PORT'];

            $dsn = "mysql:host={$host};dbname=saasargentina;charset=utf8mb4;port={$port}";
            $this->bd_saasargentina = new PDO($dsn, $user, $pass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
        } catch (PDOException $e) {
            $this->mostrarError("Error conectando a la base de datos principal: " . $e->getMessage());
        }
    }

    public function conectarById(int $idempresa, bool $read = false): bool
    {
        try {

            if (!is_numeric($idempresa)) {
                throw new Exception("idempresa no es un número");
                return false;
            }

            $query = "SELECT empresas.idempresa,
                servidores.BD_HOST, servidores.BD_HOST_RO, servidores.BD_USER,
                servidores.BD_PASS, servidores.BD_BD, servidores.BD_PORT,
                servidores.BD_SOCKET, servidores.version
            FROM empresas
                LEFT JOIN servidores ON empresas.idservidor = servidores.idservidor
            WHERE idempresa = :idempresa
                AND estado IN ('prueba', 'activada', 'demo')
                AND idsistema != 4
            LIMIT 1";

            $stmt = $this->bd_saasargentina->prepare($query);
            $stmt->execute(['idempresa' => $idempresa]);
            $datos_servidor = $stmt->fetch();

            if ($datos_servidor && $datos_servidor['idempresa'] == $idempresa) {
                $this->idempresa = $datos_servidor['idempresa'];
                $this->read = $read;

                $this->conectarBd($datos_servidor);
                return true;

            } else {
                throw new Exception("No se encontró la empresa");
                return false;
            }
        } catch (PDOException $e) {
            $this->mostrarError("Error en la consulta: " . $e->getMessage());
            return false;
        }
    }

    private function conectarBd(array $datos_servidor): bool
    {
        try {
            $host = $this->read ? $datos_servidor['BD_HOST_RO'] : $datos_servidor['BD_HOST'];
            $database = $datos_servidor['BD_BD'] . $datos_servidor['idempresa'];
            $username = $datos_servidor['BD_USER'] . $datos_servidor['idempresa'];
            $password = md5($datos_servidor['BD_PASS'] . $datos_servidor['idempresa']);

            $dsn = "mysql:host={$host};dbname={$database};charset=utf8mb4";
            if (!empty($datos_servidor['BD_PORT'])) {
                $dsn .= ";port={$datos_servidor['BD_PORT']}";
            }
            if (!empty($datos_servidor['BD_SOCKET'])) {
                $dsn .= ";unix_socket={$datos_servidor['BD_SOCKET']}";
            }

            $this->bd_link = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_PERSISTENT => true
            ]);

            return true;

        } catch (PDOException $e) {
            $this->mostrarError("Error conectando a la base de datos de la empresa: " . $e->getMessage());
            return false;
        }
    }

    public function conectarBdByIUE($iue, $read = false)
    {
        if (!$iue)
            throw new Exception("No se recibió un IUE");

        $this->getIdByIUE($iue);
        $this->conectarById($this->idempresa, $read);
    }

    public function conectarBdByMl($user_id, $read = false)
    {
        if (!$user_id)
            throw new Exception("No se recibió un ML_user_id");

        $this->getIdByMl($user_id);
        $this->conectarById($this->idempresa, $read);
    }

    private function getIdByIUE($iue)
    {
        $query = "SELECT idempresa FROM empresas WHERE iue = :iue LIMIT 1";
        $stmt = $this->bd_saasargentina->prepare($query);
        $stmt->execute(['iue' => $iue]);
        $this->idempresa = $stmt->fetchColumn();
    }

    private function getIdByMl($user_id)
    {
        $query = "SELECT idempresa FROM tiendas WHERE ML_user_id = :user_id LIMIT 1";
        $stmt = $this->bd_saasargentina->prepare($query);
        $stmt->execute(['user_id' => $user_id]);
        $this->idempresa = $stmt->fetchColumn();
    }

    public function consultaSql(string $sql, array $params = []): array
    {
        try {
            $stmt = $this->bd_link->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            $this->mostrarError("Error en la consulta: " . $e->getMessage());
            // Relanzar la excepción para que sea manejada por el código que llama
            throw new Exception("Error en la consulta SQL: " . $e->getMessage(), 0, $e);
        }
    }

    public static function getInstance(): Database
    {
        if (self::$instance === null) {
            self::$instance = new Database();
        }
        return self::$instance;
    }

    public function getBdLink(): PDO
    {
        return $this->bd_Link;
    }

    public function setReadMode(bool $read): void
    {
        $this->read = $read;
    }

    public function getIdEmpresa(): ?int
    {
        return $this->idempresa;
    }

    public function isBeta(): bool
    {
        return $this->beta;
    }

    public function isAlfa(): bool
    {
        return $this->alfa;
    }

    public function getStage(): string
    {
        return $this->stage;
    }
}
