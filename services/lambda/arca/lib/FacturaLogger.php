<?php declare(strict_types=1);

namespace FuncionesComunes\FacturaLogger;

use Aws\CloudWatchLogs\CloudWatchLogsClient;

class FacturaLogger
{
    private ?CloudWatchLogsClient $cloudWatchClient = null;
    private string $stage;
    private string $logGroupName = 'afip-facturas-log';
    private string $logStreamName;
    private bool $isDev;
    private string $localLogFile;

    public function __construct(string $stage)
    {
        $this->stage = $stage;
        $this->isDev = $stage === 'dev';
        $this->logStreamName = 'afip-facturas-' . date('Y-m-d');
        $this->localLogFile = __DIR__ . '/../afip-facturas.log';

        if (!$this->isDev) {
            $this->cloudWatchClient = new CloudWatchLogsClient([
                'version' => 'latest',
                'region'  => $_ENV['AWS_REGION'] ?? 'sa-east-1',
            ]);
        }
    }

    /**
     * Registra un intento de facturación
     */
    public function logFactura(array $datos): void
    {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'stage' => $this->stage,
            'idempresa' => $datos['idempresa'] ?? null,
            'idventa' => $datos['idventa'] ?? null,
            'estado' => $datos['estado'] ?? 'ERROR',
            'error' => $datos['error'] ?? null,
            'idtipoventa' => $datos['idtipoventa'] ?? null,
            'numero' => $datos['numero'] ?? null,
            'fecha_venta' => $datos['fecha_venta'] ?? null,
            'total' => $datos['total'] ?? null,
            'cuit' => $datos['cuit'] ?? null,
            'dni' => $datos['dni'] ?? null,
            'cae' => $datos['cae'] ?? null,
            'obscae' => $datos['obscae'] ?? null,
        ];

        $logMessage = json_encode($logEntry, JSON_UNESCAPED_UNICODE);

        if ($this->isDev) {
            $this->logToFile($logMessage);
        } else {
            $this->logToCloudWatch($logMessage);
        }
    }

    /**
     * Guarda el log en archivo local (desarrollo)
     */
    private function logToFile(string $message): void
    {
        $logLine = date('Y-m-d H:i:s') . " | " . $message . "\n";
        file_put_contents($this->localLogFile, $logLine, FILE_APPEND | LOCK_EX);
    }

    /**
     * Guarda el log en CloudWatch (alfa, beta, prod)
     */
    private function logToCloudWatch(string $message): void
    {
        try {
            // Verificar si el grupo de logs existe, si no, crearlo
            $this->ensureLogGroupExists();

            // Verificar si el stream de logs existe, si no, crearlo
            $this->ensureLogStreamExists();

            // Obtener el sequence token si existe
            $sequenceToken = $this->getSequenceToken();

            // Preparar el evento de log
            $logEvent = [
                'timestamp' => round(microtime(true) * 1000), // timestamp en milisegundos
                'message' => $message,
            ];

            // Preparar los parámetros para putLogEvents
            $params = [
                'logGroupName' => $this->logGroupName,
                'logStreamName' => $this->logStreamName,
                'logEvents' => [$logEvent],
            ];

            if ($sequenceToken) {
                $params['sequenceToken'] = $sequenceToken;
            }

            // Enviar el evento de log
            $this->cloudWatchClient->putLogEvents($params);

        } catch (\Exception $e) {
            // Si falla CloudWatch, al menos registrar en error_log
            error_log("Error enviando log a CloudWatch: " . $e->getMessage());
            error_log("Mensaje original: " . $message);
        }
    }

    /**
     * Asegura que el grupo de logs exista
     */
    private function ensureLogGroupExists(): void
    {
        try {
            $this->cloudWatchClient->describeLogGroups([
                'logGroupNamePrefix' => $this->logGroupName,
            ]);
        } catch (\Exception $e) {
            // Si no existe, crearlo
            try {
                $this->cloudWatchClient->createLogGroup([
                    'logGroupName' => $this->logGroupName,
                ]);
            } catch (\Exception $createException) {
                // Podría fallar si ya existe, ignorar
            }
        }
    }

    /**
     * Asegura que el stream de logs exista
     */
    private function ensureLogStreamExists(): void
    {
        try {
            $this->cloudWatchClient->describeLogStreams([
                'logGroupName' => $this->logGroupName,
                'logStreamNamePrefix' => $this->logStreamName,
            ]);
        } catch (\Exception $e) {
            // Si no existe, crearlo
            try {
                $this->cloudWatchClient->createLogStream([
                    'logGroupName' => $this->logGroupName,
                    'logStreamName' => $this->logStreamName,
                ]);
            } catch (\Exception $createException) {
                // Podría fallar si ya existe, ignorar
            }
        }
    }

    /**
     * Obtiene el sequence token del último evento
     */
    private function getSequenceToken(): ?string
    {
        try {
            $result = $this->cloudWatchClient->describeLogStreams([
                'logGroupName' => $this->logGroupName,
                'logStreamNamePrefix' => $this->logStreamName,
            ]);

            if (!empty($result['logStreams'])) {
                return $result['logStreams'][0]['uploadSequenceToken'] ?? null;
            }
        } catch (\Exception $e) {
            // Ignorar errores al obtener sequence token
        }

        return null;
    }
}
