<?php

namespace FuncionesComunes\ErrorHandler;

use Aws\Sqs\SqsClient;
use Exception;

class ErrorHandler
{
    private static $instance = null;
    private SqsClient $sqsClient;
    private string $emailQueueUrl;
    private string $mailServidor;
    private string $mailDesarrollo;
    private string $stage;
    private bool $isDevelopment;

    private function __construct()
    {
        // El stage se pasa automáticamente desde serverless.yml como APP_ENV
        $this->stage = $_ENV['APP_ENV'] ?? 'dev';
        $this->isDevelopment = $this->stage === 'dev' || $this->stage === 'desarrollo';

        // Obtener configuración de variables de entorno
        $this->emailQueueUrl = $_ENV['AWS_SQS_EMAIL_QUEUE'] ?? 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue';
        $this->mailServidor = $_ENV['MAIL_SERVIDOR'] ?? '<EMAIL>';
        $this->mailDesarrollo = $_ENV['MAIL_DESARROLLO'] ?? '<EMAIL>';

        // Inicializar cliente SQS
        $this->sqsClient = new SqsClient([
            'version' => 'latest',
            'region'  => $_ENV['AWS_REGION'] ?? 'sa-east-1',
            'credentials' => [
                'key'    => $_ENV['AWS_SQS_QUEUER_KEY'] ?? '',
                'secret' => $_ENV['AWS_SQS_QUEUER_SECRET'] ?? '',
            ],
        ]);
    }

    /**
     * Método principal para manejar todos los errores
     *
     * @param string $mensaje Mensaje de error
     * @param array $contexto Contexto adicional (idempresa, idventa, función, exception, etc.)
     * @param bool $continuar Si debe continuar la ejecución después del error
     */
    public function error(string $mensaje, array $contexto = [], bool $continuar = false): void
    {
        $textoCompleto = $this->construirMensajeError($mensaje, $contexto);

        if ($this->isDevelopment) {
            echo "\n\nERROR: " . strip_tags($textoCompleto) . "\n\n";
        } else {
            $this->enviarEmailError($textoCompleto);
        }

        if (!$continuar) {
            exit(1);
        }
    }

    /**
     * Construye el mensaje de error con toda la información de contexto
     */
    private function construirMensajeError(string $mensaje, array $contexto = []): string
    {
        $texto = 'ERROR: ' . $mensaje . '<br /><br />';
        $texto .= 'stage: ' . $this->stage . '<br />';
        $texto .= 'función: ' . ($contexto['funcion'] ?? 'Lambda Function') . '<br />';
        $texto .= 'fecha: ' . date('Y-m-d H:i:s') . '<br />';

        // Agregar contexto dinámico
        foreach ($contexto as $clave => $valor) {
            if ($clave === 'exception' && $valor instanceof Exception) {
                $texto .= '<br />Exception: ' . $valor->getMessage() . '<br />';
                $texto .= 'Stack trace:<br />' . nl2br($valor->getTraceAsString()) . '<br />';
            } elseif ($clave !== 'funcion') {
                $texto .= $clave . ': ' . $valor . '<br />';
            }
        }

        return $texto;
    }

    /**
     * Envía el email de error a través de SQS
     */
    private function enviarEmailError(string $mensaje): bool
    {
        try {
            if (strlen($mensaje) > 260000) {
                $mensaje = substr($mensaje, 0, 260000) . '<br />... (mensaje truncado)';
            }

            $params = [
                'QueueUrl' => $this->emailQueueUrl,
                'DelaySeconds' => 0,
                'MessageBody' => $mensaje . '<br><br><hr>Enviado desde Lambda Function',
                'MessageAttributes' => [
                    "Subject" => [
                        'DataType' => "String",
                        'StringValue' => "ERROR en Lambda Function - Stage: {$this->stage}"
                    ],
                    "To" => [
                        'DataType' => "String",
                        'StringValue' => $this->mailDesarrollo
                    ],
                    "From" => [
                        'DataType' => "String",
                        'StringValue' => $this->mailServidor
                    ],
                    "FromName" => [
                        'DataType' => "String",
                        'StringValue' => 'Lambda Function Error Handler'
                    ],
                ],
            ];

            $this->sqsClient->sendMessage($params);
            return true;

        } catch (Exception $e) {
            error_log("Error al enviar email: " . $e->getMessage());
            error_log("Mensaje original: " . strip_tags($mensaje));
            return false;
        }
    }

    /**
     * Obtener la instancia singleton
     */
    public static function getInstance(): ErrorHandler
    {
        if (self::$instance === null) {
            self::$instance = new ErrorHandler();
        }
        return self::$instance;
    }

    /**
     * Verificar si estamos en modo desarrollo
     */
    public function isDevelopment(): bool
    {
        return $this->isDevelopment;
    }

    /**
     * Obtener el stage actual
     */
    public function getStage(): string
    {
        return $this->stage;
    }
}
