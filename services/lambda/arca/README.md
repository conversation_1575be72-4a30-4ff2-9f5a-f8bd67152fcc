# AFIP SDK Lambda Function

Esta función Lambda procesa mensajes de la cola SQS para generar facturas electrónicas usando la API de AFIPSDK.

## Descripción General

El sistema utiliza AWS Lambda para procesar facturas electrónicas a través de la API de AFIPSDK. El flujo de trabajo es el siguiente:

- La aplicación principal envía un mensaje a una cola SQS con el formato `idempresa|idventa`
- La función Lambda afipsdk recibe el mensaje y procesa la factura
- La función Lambda obtiene los datos de la factura de la base de datos
- La función Lambda envía los datos a AFIP y recibe la respuesta
- La función Lambda actualiza el estado de la factura en la base de datos

## Configuración de Variables de Entorno

### Variables Requeridas en AWS Lambda

Estas variables deben configurarse en la consola de AWS Lambda para cada ambiente:

#### Base de Datos
```bash
BD_HOST=127.0.0.1
BD_USER=root
BD_PASS=pass
BD_BD=saasargentina
BD_PORT=3306
```

#### AWS SQS - Cola para emails
```bash
AWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue
AWS_SQS_QUEUER_KEY=YOUR_AWS_SQS_QUEUER_KEY
AWS_SQS_QUEUER_SECRET=YOUR_AWS_SQS_QUEUER_SECRET
```

#### AWS S3 - Credenciales
```bash
AWS_S3_KEY=YOUR_S3_ACCESS_KEY
AWS_S3_SECRET=YOUR_S3_SECRET_KEY
```

#### AFIP - Configuración de ambiente
```bash
AFIP_PRODUCTION=false  # true para producción, false para homologación
```

#### Email - Configuración de errores
```bash
MAIL_SERVIDOR=<EMAIL>
MAIL_DESARROLLO=<EMAIL>
```

#### Ambiente de aplicación
```bash
APP_ENV=dev  # Valores: dev, alfa, beta, prod y se auto completa según el deploy de serverless
```

## Manejo de Ambientes

### Configuración por Ambiente

El sistema maneja 4 ambientes principales: dev, alfa, beta, prod

El sistema determina el ambiente basándose en:

1. **Variable de entorno `APP_ENV`** (prioritaria)
2. **Detección automática por stage de Serverless**

### Enrutamiento de Mensajes

La función `afipsdk_lambda()` en `funciones_aws.php` determina automáticamente la cola correcta basándose en la variable de sesión `$_SESSION['servidor_version']`:

- `$_SESSION['servidor_version'] == 'ALFA'` → Cola ALFA
- `$_SESSION['servidor_version'] == 'BETA'` → Cola BETA
- `$_SESSION['servidor_version'] == ''` o no definida → Cola PROD

### Configuración AFIP (Homologación vs Producción)

La configuración de AFIP se determina por:

1. **Variable `AFIP_PRODUCTION`**: Controla si se usa el ambiente de producción de AFIP. La idea es poder cambiarlo desde la variable de entorno de Lambda
2. **Ambiente de aplicación**: Por defecto, solo `prod` usa producción de AFIP

## Sistema de Logging

### Configuración de CloudWatch

El sistema utiliza un log group separado en CloudWatch llamado `afip-facturas-log` para todas las etapas (alfa, beta, prod). Para desarrollo local, los logs se guardan en el archivo `afip-facturas.log`.

#### Crear el Log Group de CloudWatch

```bash
# Crear el log group
aws logs create-log-group --log-group-name afip-facturas-log --region sa-east-1

# Configurar retención de logs (opcional - 30 días)
aws logs put-retention-policy --log-group-name afip-facturas-log --retention-in-days 30 --region sa-east-1
```

### Scripts de Ayuda

#### Script de configuración automática
```bash
# Configura CloudWatch automáticamente
./setup-cloudwatch.sh
```

#### Script de consulta de logs
```bash
# Ver ayuda del script
./query-logs.sh help

# Ejemplos de uso:
./query-logs.sh recent 50         # Últimos 50 logs
./query-logs.sh today             # Logs de hoy
./query-logs.sh stage prod        # Logs de producción
./query-logs.sh empresa 161       # Logs de empresa 161
./query-logs.sh errors            # Solo errores
./query-logs.sh follow            # Seguir en tiempo real
```

#### Verificar permisos del Lambda

Los permisos ya están configurados en `serverless.yml`, pero si necesitas verificarlos:

```bash
# Verificar política del role del Lambda
aws iam get-role-policy --role-name arca-dev-sa-east-1-lambdaRole --policy-name arca-dev-lambda --region sa-east-1
```

### Comandos para Consultar Logs

#### Ver últimos logs

```bash
# Últimos 50 eventos en todas las streams
aws logs filter-log-events --log-group-name afip-facturas-log --limit 50 --region sa-east-1

# Últimos logs con formato más legible
aws logs filter-log-events --log-group-name afip-facturas-log --limit 20 --region sa-east-1 --query 'events[*].[timestamp,message]' --output table
```

#### Filtrar logs por fecha

```bash
# Logs de las últimas 24 horas
aws logs filter-log-events --log-group-name afip-facturas-log --start-time $(date -d '1 day ago' +%s)000 --region sa-east-1

# Logs de una fecha específica (formato: YYYY-MM-DD)
aws logs filter-log-events --log-group-name afip-facturas-log \
  --start-time $(date -d '2024-01-15' +%s)000 \
  --end-time $(date -d '2024-01-16' +%s)000 \
  --region sa-east-1

# Logs de la última hora
aws logs filter-log-events --log-group-name afip-facturas-log --start-time $(date -d '1 hour ago' +%s)000 --region sa-east-1
```

#### Filtrar logs por stage

```bash
# Logs solo de producción
aws logs filter-log-events --log-group-name afip-facturas-log --filter-pattern '"stage":"prod"' --region sa-east-1

# Logs solo de alfa
aws logs filter-log-events --log-group-name afip-facturas-log --filter-pattern '"stage":"alfa"' --region sa-east-1

# Logs solo de beta
aws logs filter-log-events --log-group-name afip-facturas-log --filter-pattern '"stage":"beta"' --region sa-east-1
```

#### Filtrar logs por empresa

```bash
# Logs de una empresa específica
aws logs filter-log-events --log-group-name afip-facturas-log --filter-pattern '"idempresa":161' --region sa-east-1

# Logs de múltiples empresas
aws logs filter-log-events --log-group-name afip-facturas-log --filter-pattern '{ $.idempresa = 161 || $.idempresa = 162 }' --region sa-east-1
```

#### Filtrar logs por estado

```bash
# Solo facturas aprobadas
aws logs filter-log-events --log-group-name afip-facturas-log --filter-pattern '"estado":"APROBADO"' --region sa-east-1

# Solo errores
aws logs filter-log-events --log-group-name afip-facturas-log --filter-pattern '"estado":"ERROR"' --region sa-east-1

# Solo facturas pausadas
aws logs filter-log-events --log-group-name afip-facturas-log --filter-pattern '"estado":"PAUSADO"' --region sa-east-1
```

#### Consultas combinadas

```bash
# Errores de una empresa específica en las últimas 24 horas
aws logs filter-log-events --log-group-name afip-facturas-log \
  --filter-pattern '{ $.estado = "ERROR" && $.idempresa = 161 }' \
  --start-time $(date -d '1 day ago' +%s)000 \
  --region sa-east-1

# Facturas aprobadas de producción en un rango de fechas
aws logs filter-log-events --log-group-name afip-facturas-log \
  --filter-pattern '{ $.estado = "APROBADO" && $.stage = "prod" }' \
  --start-time $(date -d '2024-01-15' +%s)000 \
  --end-time $(date -d '2024-01-16' +%s)000 \
  --region sa-east-1
```

#### Ver logs locales (desarrollo)

```bash
# Ver todo el archivo de log local
cat afip-facturas.log

# Ver las últimas 50 líneas
tail -50 afip-facturas.log

# Seguir logs en tiempo real
tail -f afip-facturas.log

# Filtrar por estado en logs locales
grep '"estado":"ERROR"' afip-facturas.log

# Filtrar por empresa en logs locales
grep '"idempresa":161' afip-facturas.log

# Probar el sistema de logging
php test-logging.php [idempresa] [idventa]
```

### Estructura de Archivos

```
arca/
├── lib/
│   ├── Database.php
│   ├── ErrorHandler.php
│   └── FacturaLogger.php          # 🆕 Nueva clase de logging
├── public/
│   └── afipsdk.php                # Modificado con logging
├── afip-facturas.log              # 🆕 Log local (solo dev)
├── setup-cloudwatch.sh           # 🆕 Script de configuración
├── query-logs.sh                  # 🆕 Script de consulta
├── test-logging.php               # 🆕 Script de prueba
├── serverless.yml                 # Modificado con permisos
└── README.md                      # Actualizado
```

### Formato del Log

Cada entrada de log contiene la siguiente información en formato JSON:

```json
{
  "timestamp": "2024-01-15 14:30:25",
  "stage": "prod",
  "idempresa": 161,
  "idventa": 3567,
  "estado": "APROBADO",
  "error": null,
  "idtipoventa": 1,
  "numero": "00001-00000123",
  "fecha_venta": "2024-01-15",
  "total": 1210.00,
  "cuit": "20123456789",
  "dni": "12345678",
  "cae": "74123456789012",
  "obscae": "20240125"
}
```

**Estados posibles**:
- `PROCESANDO`: Factura en proceso
- `APROBADO`: Factura aprobada por AFIP
- `ERROR`: Error en el procesamiento
- `PAUSADO`: Empresa pausada temporalmente

### 1. Colas SQS por Ambiente

- **URL**: https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-${stage}

**Formato del mensaje**: idempresa|idventa (ambos deben ser números enteros positivos)

## Configuración en AWS

### 1. Configurar Variables de Entorno en Lambda

Para cada función Lambda (dev, alfa, beta, prod), configurar las variables de entorno correspondientes en la consola de AWS:

1. Ir a AWS Lambda → Funciones → [nombre-función]
2. Ir a Configuración → Variables de entorno
3. Agregar todas las variables listadas arriba
4. Ajustar los valores según el ambiente

## Comandos de Deploy

### Deploy por Ambiente

```bash
# Navegar al directorio del proyecto
cd services/lambda/arca

# Deploy en desarrollo
serverless deploy --stage=dev

# Deploy en alfa
serverless deploy --stage=alfa

# Deploy en beta
serverless deploy --stage=beta

# Deploy en producción
serverless deploy --stage=prod
```

### Deploy con Variables de Entorno

```bash
# Deploy con variables específicas
serverless deploy --stage=prod \
  --param="awsRegion=sa-east-1" \
  --param="afipProduction=true"
```

## Flujo de Procesamiento

1. **Recepción del mensaje**:
   - La función Lambda recibe un mensaje de la cola SQS
   - Valida que el mensaje tenga el formato correcto (idempresa|idventa)

2. **Obtención de datos**:
   - Conecta a la base de datos de la empresa
   - Obtiene los datos de la venta y sus detalles

3. **Preparación de datos para AFIP**:
   - Formatea los datos según los requerimientos de la API de AFIP
   - Genera el JSON para la solicitud

4. **Envío a AFIP**:
   - Envía los datos a la API de AFIP
   - Recibe la respuesta (CAE, resultado, etc.)

5. **Actualización de la base de datos**:
   - Actualiza el estado de la factura con el resultado de AFIP
   - Guarda el CAE y otros datos relevantes

### Pruebas locales

Para probar la función localmente sin necesidad de enviar mensajes a la cola SQS:

```bash
# Probar con un evento específico
serverless bref:local --function afipsdk --data '{"Records":[{"body":"161|3567"}]}'

# O usando el script de ayuda
php ./test.php 161 3567
```

### Pruebas en AWS

Para probar la función en AWS:

```bash
# Enviar un mensaje a la cola SQS
aws sqs send-message --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --message-body "161|3567"

# Ver los logs de la función
serverless logs -f afipsdk

# Ver los logs en tiempo real
serverless logs -f afipsdk -t

# Ver los logs con AWS CLI
aws logs filter-log-events --log-group-name /aws/lambda/arca-dev-afipsdk --limit 20
```

### Gestión de la Cola SQS

```bash
# Ver atributos de la cola
aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --attribute-names All

# Ver mensajes en la cola
aws sqs receive-message --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --max-number-of-messages 10

# Purgar la cola (eliminar todos los mensajes)
aws sqs purge-queue --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev
```

## Solución de Problemas

### El Lambda no procesa los mensajes

Verifica que la cola SQS tenga mensajes (ajusta la URL según el ambiente):

```bash
# Para desarrollo
aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --attribute-names ApproximateNumberOfMessages

# Para alfa
aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-alfa --attribute-names ApproximateNumberOfMessages

# Para beta
aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-beta --attribute-names ApproximateNumberOfMessages

# Para producción
aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-prod --attribute-names ApproximateNumberOfMessages
```

Verifica que el Lambda tenga los permisos correctos:

```bash
aws lambda get-policy --function-name arca-dev-afipsdk
```

Verifica que el mapeo de eventos esté configurado correctamente:

```bash
aws lambda list-event-source-mappings --function-name arca-dev-afipsdk
```

### Errores en el Lambda

- Revisa los logs para ver el error específico:

```bash
serverless logs -f afipsdk --stage=dev
serverless logs -f afipsdk --stage=alfa
serverless logs -f afipsdk --stage=beta
serverless logs -f afipsdk --stage=prod
```

## Gestión de Pausa por Empresa

### Variable de Entorno

Agregar a las variables de entorno de Lambda (opcional):

```bash
IDEMPRESAS_PAUSADAS=   # Vacío = sin pausas (comportamiento por defecto)
```

### Opciones de pausa

#### Pausar todas las empresas
```bash
IDEMPRESAS_PAUSADAS=*
```

#### Pausar empresas específicas
```bash
IDEMPRESAS_PAUSADAS=161,162,163
```

#### Pausar una sola empresa
```bash
IDEMPRESAS_PAUSADAS=161
```

### Comandos AWS CLI

#### Pausar todas las empresas
```bash
aws lambda update-function-configuration \
  --function-name arca-dev-afipsdk \
  --environment Variables='{"IDEMPRESAS_PAUSADAS":"*","BD_HOST":"127.0.0.1",...}'
```

#### Pausar empresas específicas
```bash
aws lambda update-function-configuration \
  --function-name arca-dev-afipsdk \
  --environment Variables='{"IDEMPRESAS_PAUSADAS":"161,162","BD_HOST":"127.0.0.1",...}'
```

#### Quitar todas las pausas
```bash
aws lambda update-function-configuration \
  --function-name arca-dev-afipsdk \
  --environment Variables='{"IDEMPRESAS_PAUSADAS":"","BD_HOST":"127.0.0.1",...}'
```

### Comportamiento durante la pausa

- **Mensajes pausados**: Permanecen en SQS y se procesan cuando se quite la pausa
- **Sin modificación en BD**: Las ventas no se tocan durante la pausa
- **Log visible**: Se muestra qué empresa fue pausada en los logs de Lambda