service: arca

provider:
    name: aws
    region: sa-east-1
    stage: ${opt:stage, 'dev'}
    environment:
        APP_ENV: ${self:provider.stage}
    vpc:
        securityGroupIds:
            - sg-df2cdcb9  # Security Group que permite acceso a RDS
        subnetIds:
            - subnet-58a1d73c
            - subnet-1bb7b142
            - subnet-59a1d73d
            - subnet-804867d9
    iam:
        role:
            statements:
                - Effect: Allow
                  Action:
                    - sqs:ReceiveMessage
                    - sqs:DeleteMessage
                    - sqs:GetQueueAttributes
                    - sqs:ChangeMessageVisibility
                  Resource: arn:aws:sqs:sa-east-1:124561084955:afipsdk-queue-${self:provider.stage}
                - Effect: Allow
                  Action:
                    - ec2:CreateNetworkInterface
                    - ec2:DescribeNetworkInterfaces
                    - ec2:DeleteNetworkInterface
                    - ec2:AttachNetworkInterface
                    - ec2:DetachNetworkInterface
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                  Resource:
                    - "*"
                    - "arn:aws:logs:sa-east-1:124561084955:log-group:afip-facturas-log:*"

plugins:
    - ./vendor/bref/bref

functions:
    afipsdk:
        handler: public/afipsdk.php
        description: 'Procesador AFIP SDK (${self:provider.stage})'
        runtime: php-83
        timeout: 30  # Aumentar el timeout a 30 segundos
        events:
            - sqs:
                arn: arn:aws:sqs:sa-east-1:124561084955:afipsdk-queue-${self:provider.stage}
                batchSize: 1

# Exclude files from deployment
package:
    patterns:
        - '!tests/**'

custom:
    bref:
        phpFpm:
            version: 83
