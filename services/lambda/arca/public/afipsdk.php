<?php declare(strict_types=1);

require __DIR__ . '/../vendor/autoload.php';
require __DIR__ . '/../lib/Database.php';
require __DIR__ . '/../lib/ErrorHandler.php';
require __DIR__ . '/../lib/FacturaLogger.php';

use Bref\Context\Context;
use Bref\Event\Sqs\SqsHandler;
use Bref\Event\Sqs\SqsEvent;
use FuncionesComunes\Database\Database;
use FuncionesComunes\ErrorHandler\ErrorHandler;
use FuncionesComunes\FacturaLogger\FacturaLogger;
use Aws\S3\S3Client;
// use Afip;

class AfipSdk extends SqsHandler
{
    private Database $db;
    private ErrorHandler $errorHandler;
    private FacturaLogger $facturaLogger;
    private S3Client $s3Client;
    private string $stage;
    private bool $isProduction;

    public function __construct()
    {
        // Cargar variables de entorno
        if (file_exists(__DIR__ . '/../.env')) {
            $dotenv = \Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
            $dotenv->load();
        }

        // El stage se pasa automáticamente desde serverless.yml como APP_ENV
        $this->stage = $_ENV['APP_ENV'] ?? 'dev';
        $this->isProduction = ($_ENV['AFIP_PRODUCTION'] ?? 'false') === 'true';

        // Inicializar dependencias
        $this->db = Database::getInstance();
        $this->errorHandler = ErrorHandler::getInstance();
        $this->facturaLogger = new FacturaLogger($this->stage);

        $this->s3Client = new S3Client([
            'version' => 'latest',
            'region'  => $_ENV['AWS_REGION'] ?? 'sa-east-1',
            'credentials' => [
                'key'    => $_ENV['AWS_S3_KEY'],
                'secret' => $_ENV['AWS_S3_SECRET'],
            ],
        ]);
    }

    /**
     * Procesa mensajes SQS
     */
    public function handleSqs(SqsEvent $event, Context $context): void
    {
        echo "INICIO: Procesando " . count($event->getRecords()) . " mensajes\n";

        foreach ($event->getRecords() as $record) {
            $mensaje = $record->getBody();
            echo "Procesando: $mensaje\n";

            try {
                $this->procesarMensaje($mensaje);
            } catch (\Exception $e) {
                $this->errorHandler->error(
                    'Error procesando mensaje SQS',
                    ['mensaje' => $mensaje, 'error' => $e->getMessage()]
                );
            }
        }
    }

    /**
     * Procesa un mensaje individual
     */
    public function procesarMensaje(string $mensaje): void
    {
        // Validar formato
        if (!preg_match('/^\d+\|\d+$/', $mensaje)) {
            throw new \Exception("Formato de mensaje inválido: $mensaje");
        }

        list($idempresa, $idventa) = explode('|', $mensaje);
        $idempresa = (int)$idempresa;
        $idventa = (int)$idventa;

        // Inicializar datos del log
        $logData = [
            'idempresa' => $idempresa,
            'idventa' => $idventa,
            'estado' => 'PROCESANDO'
        ];

        try {
            // Verificar pausa antes de procesar
            if ($this->enPausa($idempresa)) {
                echo "PAUSADO: Empresa $idempresa pausada - mensaje devuelto a cola\n";
                $logData['estado'] = 'PAUSADO';
                $logData['error'] = 'Empresa en pausa';
                $this->facturaLogger->logFactura($logData);
                return; // El mensaje se devuelve automáticamente a SQS
            }

            // Conectar a la empresa
            if (!$this->db->conectarById($idempresa)) {
                throw new \Exception("No se pudo conectar a empresa $idempresa");
            }

            // Obtener datos de la venta
            $venta = $this->obtenerVenta($idventa);
            if (!$venta) {
                throw new \Exception("Venta $idventa no encontrada");
            }

            // Completar datos del log con información de la venta
            $logData = array_merge($logData, [
                'idtipoventa' => $venta['idtipoventa'] ?? null,
                'numero' => $venta['numero'] ?? null,
                'fecha_venta' => $venta['fecha'] ?? null,
                'total' => $venta['total'] ?? null,
                'dni' => $venta['numero_documento'] ?? null,
                'cuit' => $venta['cuit'] ?? null,
                'tipodoc' => $venta['tipodoc'] ?? null,
            ]);

            // Verificar si debe aprobar esta factura
            if (!$this->debeAprobar($venta, $logData)) {
                $this->facturaLogger->logFactura($logData);
                return; // El mensaje se devuelve automáticamente a SQS
            }

            // Validaciones previas antes de llamar a AFIP
            $this->validarAntes($venta);

            // Descargar certificados
            $certificados = $this->descargarCertificados($venta['configuracion_cuit']);

            // Obtener último número de AFIP
            $ultimonumero = $this->ultimoNumero($venta, $certificados);

            // Actualizar número si corresponde
            $this->actualizarNumero($venta, $ultimonumero);

            // Procesar con AFIP
            $resultado = $this->enviarFacturaAfip($idempresa, $idventa, $venta, $certificados);

            // Actualizar log con resultado exitoso
            $logData['estado'] = 'APROBADO';
            $logData['cae'] = $resultado['CAE'] ?? null;
            $logData['obscae'] = $resultado['CAEFchVto'] ?? null;

            echo "Factura $idventa procesada exitosamente\n";

        } catch (\Exception $e) {
            // Actualizo venta si corresponde
            if (isset($e->actualizar) || isset($e->estado)) {
                $this->actualizarVenta(
                    $idventa,
                    $e->estado ?? '',
                    $e->actualizar ?? []
                );
            }

            // Log del error
            $logData['estado'] = 'ERROR';
            $logData['error'] = $e->getMessage();

            echo "Error procesando factura $idventa: " . $e->getMessage() . "\n";
            throw $e; // Re-lanzar para que ErrorHandler lo maneje
        } finally {
            // Siempre registrar el log, sin importar el resultado
            $this->facturaLogger->logFactura($logData);
        }
    }

    /**
     * Obtiene datos de la venta
     */
    private function obtenerVenta(int $idventa): ?array
    {
        $sql = "SELECT idventa, fecha, estadocae, cae, tipodoc, cuit, dni, numero,
                total, nogravado, neto, iva, tributos, exento, descuento,
                concepto, vencimiento1, fechainicio, fechafin,
                cat.idtipoventa, cat.idcomportamiento, cat.discrimina, cat.tipofacturacion, cat.letra, cat.puntodeventa, cat.ultimo_idventa_aprobado,
                m.codigo, m.cotizacion,
                (SELECT cuit FROM configuraciones LIMIT 1) AS configuracion_cuit
                FROM ventas
                LEFT JOIN categorias_ventas cat ON ventas.idtipoventa = cat.idtipoventa
                LEFT JOIN monedas AS m ON ventas.idmoneda = m.idmoneda
                WHERE ventas.idventa = ? LIMIT 1";

        $resultado = $this->db->consultaSql($sql, [$idventa]);
        return $resultado[0] ?? null;
    }

    /**
     * Descarga certificados desde S3
     */
    private function descargarCertificados(string $cuit): array
    {
        $bucket = $_ENV['AWS_S3_WSFE_BUCKET'] ?? 'saasargentina-wsfe';
        $certificados = [];
        $tempDir = sys_get_temp_dir() . '/wsfe';

        // Crear directorio temporal si no existe
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        foreach (['crt', 'key'] as $ext) {
            $s3File = "$cuit/$cuit.$ext";
            $localFile = "$tempDir/$cuit.$ext";

            try {
                $this->s3Client->getObject([
                    'Bucket' => $bucket,
                    'Key'    => $s3File,
                    'SaveAs' => $localFile,
                ]);

                $certificados[$ext] = $localFile;
                echo "Certificado descargado: $s3File\n";

            } catch (\Exception $e) {
                throw new \Exception("Error descargando certificado $s3File: " . $e->getMessage());
            }
        }

        return $certificados;
    }

    /**
     * Envía factura a AFIP
     */
    private function enviarFacturaAfip(int $idempresa, int $idventa, array $venta, array $certificados): array
    {
        try {
            // Debug: ver qué datos tenemos en el array $venta
            echo "DEBUG - Contenido del array venta:\n";
            foreach ($venta as $key => $value) {
                echo "  $key: " . (is_string($value) ? $value : json_encode($value)) . "\n";
            }

            // Verificar si existe cuit_empresa o buscar alternativas
            $cuit = null;
            if (isset($venta['cuit_empresa'])) {
                $cuit = $venta['cuit_empresa'];
            } elseif (isset($venta['cuit'])) {
                $cuit = $venta['cuit'];
            } else {
                throw new \Exception("No se encontró CUIT en los datos de la venta");
            }

            echo "CUIT a usar: $cuit\n";

            // Configurar AFIPSDK
            $afip = new Afip([
                'CUIT' => $cuit,
                'production' => $this->isProduction,
                'cert' => file_get_contents($certificados['crt']),
                'key' => file_get_contents($certificados['key']),
            ]);

            // Preparar datos del comprobante
            $comprobante = $this->prepararComprobante($venta);

            // Enviar a AFIP
            $resultado = $afip->ElectronicBilling->CreateVoucher($comprobante);

            // Procesar resultado
            if ($resultado['CAE']) {
                $this->actualizarVenta($idventa, 'APROBADO', $resultado['actualizar'] ?? []);
                return $resultado;
            } else {
                throw new \Exception("AFIP rechazó la factura: " . json_encode($resultado));
            }

        } finally {
            // Limpiar archivos temporales
            foreach ($certificados as $archivo) {
                if (file_exists($archivo)) {
                    unlink($archivo);
                }
            }
        }
    }

    /**
     * Prepara datos del comprobante para AFIP
     */
    private function prepararComprobante(array $venta): array
    {
        return [
            'CantReg' => 1,
            'PtoVta' => $venta['punto_venta'],
            'CbteTipo' => $venta['tipo_comprobante'],
            'Concepto' => $venta['concepto'] ?? 1,
            'DocTipo' => $venta['tipo_documento'] ?? 80,
            'DocNro' => $venta['numero_documento'],
            'CbteDesde' => $venta['numero_comprobante'],
            'CbteHasta' => $venta['numero_comprobante'],
            'CbteFch' => date('Ymd', strtotime($venta['fecha'])),
            'ImpTotal' => $venta['total'],
            'ImpTotConc' => $venta['no_gravado'] ?? 0,
            'ImpNeto' => $venta['neto'],
            'ImpOpEx' => $venta['exento'] ?? 0,
            'ImpIVA' => $venta['iva'],
            'ImpTrib' => $venta['tributos'] ?? 0,
            'MonId' => 'PES',
            'MonCotiz' => 1,
        ];
    }

    /**
     * Actualiza venta según el resultado (unifica exitoso y rechazado)
     */
    private function actualizarVenta(int $idventa, string $estado, array $actualizar = []): void
    {
        // Si es error pongo en sin_especificar el estado que no re-intenta hasta que se ejecuta el script sincae
        if ($estado == 'ERROR')
            $actualizar['estadocae'] = 'sin_especificar';

        if (count($actualizar)) {
            $campos = [];
            foreach ($actualizar as $key => $value)
                $campos[] = "$key = '$value'";
            $sql = "UPDATE ventas SET ".implode(', ', $campos)." WHERE idventa = $idventa";
        }

        $this->db->consultaSql($sql);
        echo "Venta $idventa actualizada como $estado\n";

        // Enviar mail de error cuando corresponde
        if (in_array($estado, ['RECHAZADO', 'ERROR']) && !empty($actualizar['enviar_mail'])) {
            $this->errorHandler->error(
                "Venta $idventa $estado: " . ($actualizar['motivo'] ?? $actualizar['error'] ?? 'Error desconocido'),
                ['idventa' => $idventa, 'estado' => $estado, 'datos' => $actualizar],
                true
            );
        }
    }

    /**
     * Verifica si una empresa está en pausa
     */
    private function enPausa(int $idempresa): bool
    {
        $pausaGeneral = $_ENV['IDEMPRESAS_PAUSADAS'] ?? '';

        // Si está vacío, no hay pausa
        if (empty($pausaGeneral)) {
            return false;
        }

        // Si es *, pausar todo
        if ($pausaGeneral === '*') {
            return true;
        }

        // Verificar si la empresa específica está pausada
        $empresasPausadas = explode(',', $pausaGeneral);
        return in_array((string)$idempresa, $empresasPausadas);
    }

    /**
     * Verifica si debe aprobar la factura
     */
    private function debeAprobar(array $venta, array &$logData): bool
    {
        $idventa = $venta['idventa'];

        // Verificar si corresponde intentar emitir
        if ($venta['tipofacturacion'] != 'electronico' ||
            strlen($venta['cae'] ?? '') == 14 ||
            ($venta['estadocae'] ?? '') != 'pendiente') {

            $logData['estado'] = 'NO_CORRESPONDE';
            $logData['error'] = 'Factura no es electrónica, ya tiene CAE o no está pendiente';

            echo "No corresponde aprobar factura $idventa: tipofacturacion={$venta['tipofacturacion']}, cae_length=" . strlen($venta['cae'] ?? '') . ", estadocae={$venta['estadocae']}\n";
            return false;
        }

        // Por el momento no reviso si hay factura en cola
        return true;

        // Verificar si hay una factura del mismo tipo esperando CAE
        $sql = "SELECT COUNT(*) as cantidad
                FROM ventas v
                LEFT JOIN categorias_ventas cv ON v.idtipoventa = cv.idtipoventa
                WHERE v.idventa < ?
                AND cv.idcomportamiento = ?
                AND cv.puntodeventa = ?
                AND v.estadocae = 'pendiente'
                AND v.tipofacturacion = 'electronico'";

        $resultado = $this->db->consultaSql($sql, [
            $idventa,
            $venta['idcomportamiento'],
            $venta['puntodeventa']
        ]);

        $cantidad = $resultado[0]['cantidad'] ?? 0;

        if ($cantidad > 0) {
            $logData['estado'] = 'EN_COLA';
            $logData['error'] = "Hay $cantidad factura(s) del mismo tipo esperando CAE";

            echo "Factura $idventa en cola: hay $cantidad facturas anteriores pendientes del mismo tipo\n";
            return false;
        }

        return true;
    }

    /**
     * Obtiene el último número de comprobante de AFIP
     */
    private function ultimoNumero(array $venta, array $certificados): int
    {
        // Configurar AFIPSDK
        $afip = new Afip([
            'CUIT' => $venta['configuracion_cuit'],
            'production' => $this->isProduction,
            'cert' => file_get_contents($certificados['crt']),
            'key' => file_get_contents($certificados['key']),
        ]);

        try {
            // Obtener último número de comprobante
            $ultimoComprobante = $afip->ElectronicBilling->getLastVoucher(
                $venta['puntodeventa'],
                $venta['idcomportamiento']
            );

            echo "Último comprobante AFIP: " . json_encode($ultimoComprobante) . "\n";

            // Si la respuesta es exitosa y tiene número válido
            if (isset($ultimoComprobante['CbteNro']) && is_numeric($ultimoComprobante['CbteNro'])) {
                echo "Último número obtenido exitosamente: {$ultimoComprobante['CbteNro']}\n";
                return (int)$ultimoComprobante['CbteNro'];
            }

            // Si llegamos aquí, hay un error en la respuesta de AFIP
            $errorMsg = $ultimoComprobante['err_msg'] ?? '';
            $output = $ultimoComprobante['output'] ?? '';
            $errorCompleto = $errorMsg . ' ' . $output;

        } catch (\Exception $e) {
            echo "Error obteniendo último número: " . $e->getMessage() . "\n";

            // Analizar el error de la excepción
            $errorCompleto = $e->getMessage() . ' ' . $e->getTraceAsString();
        }

        // Errores de configuración del usuario (rechazar factura)
        $erroresUsuario = [
            'El punto de venta no se encuentra habilitado a usar en el presente WS',
            'key values mismatch',
            'Imposible abrir',
            'Archivo de configuracion',
            'No aparecio CUIT en lista de relaciones',
            'Computador no autorizado a acceder al servicio',
            '11002',
            'El punto de venta no se encuentra habilitado'
        ];

        foreach ($erroresUsuario as $errorPattern) {
            if (strpos($errorCompleto, $errorPattern) !== false) {
                // Error que requiere rechazar la venta
                $exception = new \Exception($this->getMensajeError($errorPattern));
                $exception->estado = 'RECHAZADO';
                $exception->actualizar = [
                    'estadocae' => 'rechazado',
                    'obscae' => $this->getMensajeError($errorPattern)
                ];
                throw $exception;
            }
        }

        // Errores de AFIP caído (no actualizar venta, para que Lambda reintente)
        $erroresAfip = ['Tag not found', 'Consultar ultimo numero:', 'timeout'];
        foreach ($erroresAfip as $errorPattern) {
            if (strpos($errorCompleto, $errorPattern) !== false) {
                // Error de comunicación con AFIP - no actualizar venta
                throw new \Exception('Factura cerrada pendiente de CAE por problema de AFIP');
            }
        }

        // Error desconocido (enviar para análisis)
        $exception = new \Exception('Error desconocido obteniendo último número');
        $exception->estado = 'ERROR';
        throw $exception;
    }

    /**
     * Obtiene mensaje de error específico según el patrón
     */
    private function getMensajeError(string $errorPattern): string
    {
        $mensajes = [
            'El punto de venta no se encuentra habilitado a usar en el presente WS' =>
                'Factura RECHAZADA: Punto de venta no configurado correctamente en AFIP',
            'key values mismatch' =>
                'Factura RECHAZADA: Certificado Digital incorrecto en AFIP',
            'Imposible abrir' =>
                'Factura RECHAZADA: Certificado Digital incorrecto en AFIP',
            'Archivo de configuracion' =>
                'Factura RECHAZADA: Certificado Digital incorrecto en AFIP',
            'No aparecio CUIT en lista de relaciones' =>
                'Factura RECHAZADA: Error de empadronamiento en AFIP',
            'Computador no autorizado a acceder al servicio' =>
                'Factura RECHAZADA: Error de empadronamiento en AFIP',
            '11002' =>
                'Factura RECHAZADA: Punto de venta no habilitado para Webservice',
            'El punto de venta no se encuentra habilitado' =>
                'Factura RECHAZADA: Punto de venta no habilitado para Webservice'
        ];

        foreach ($mensajes as $pattern => $mensaje) {
            if (strpos($errorPattern, $pattern) !== false) {
                return $mensaje;
            }
        }

        return 'Factura RECHAZADA: Error de configuración en AFIP';
    }

    /**
     * Verifica un comprobante YA EXISTENTE contra AFIP (para auditoría/verificación posterior)
     * NOTA: Este método se usa para verificar comprobantes ya creados, no para validar antes de crear
     */
    private function verificarComprobanteExistente(array $venta, array $certificados, bool $verificarCae = true): bool
    {
        if (!$venta['idcomportamiento'] || !$venta['puntodeventa'] || !$venta['numero']) {
            echo "Error: Faltan datos para verificar comprobante\n";
            return false;
        }

        try {
            // Configurar AFIPSDK
            $afip = new Afip([
                'CUIT' => $venta['configuracion_cuit'],
                'production' => $this->isProduction,
                'cert' => file_get_contents($certificados['crt']),
                'key' => file_get_contents($certificados['key']),
            ]);

            // Obtener información del comprobante
            $comprobanteInfo = $afip->ElectronicBilling->getVoucherInfo(
                $venta['numero'],
                $venta['puntodeventa'],
                $venta['idcomportamiento']
            );

            // Si el comprobante no existe
            if ($comprobanteInfo === null) {
                echo "Comprobante no existe en AFIP\n";
                return false;
            }

            // La fecha sin hora y en formato Y-m-d
            list($fecha, $hora) = explode(' ', $venta['fecha']);

            // Verificar datos del comprobante
            $tipoDoc = $comprobanteInfo['DocTipo'] ?? null;
            $nroDoc = $comprobanteInfo['DocNro'] ?? null;
            $fechaAfip = $comprobanteInfo['CbteFch'] ?? null;
            $numeroAfip = $comprobanteInfo['CbteDesde'] ?? null;
            $caeAfip = $comprobanteInfo['CAE'] ?? null;
            $totalAfip = $comprobanteInfo['ImpTotal'] ?? null;

            // Convertir fecha de AFIP (yyyymmdd) a formato Y-m-d
            if ($fechaAfip && strlen($fechaAfip) == 8) {
                $fechaAfipFormateada = substr($fechaAfip, 0, 4) . '-' .
                                      substr($fechaAfip, 4, 2) . '-' .
                                      substr($fechaAfip, 6, 2);
            } else {
                echo "Fecha de AFIP inválida: $fechaAfip\n";
                return false;
            }

            // Verificar discrepancias
            $errores = [];

            if (($tipoDoc == '80' && $venta['cuit'] != $nroDoc) ||
                ($tipoDoc == '96' && $venta['dni'] != $nroDoc)) {
                $errores[] = "Documento: venta={$venta['cuit']}/{$venta['dni']} != afip=$nroDoc (tipo=$tipoDoc)";
            }

            if ($fecha != $fechaAfipFormateada) {
                $errores[] = "Fecha: venta=$fecha != afip=$fechaAfipFormateada";
            }

            if ($venta['numero'] != $numeroAfip) {
                $errores[] = "Número: venta={$venta['numero']} != afip=$numeroAfip";
            }

            if ($verificarCae && $venta['cae'] != $caeAfip) {
                $errores[] = "CAE: venta={$venta['cae']} != afip=$caeAfip";
            }

            if (abs((float)$venta['total'] - (float)$totalAfip) > 0.01) {
                $errores[] = "Total: venta={$venta['total']} != afip=$totalAfip";
            }

            if (!empty($errores)) {
                echo "Errores verificando comprobante: " . implode(', ', $errores) . "\n";
                return false;
            }

            echo "Comprobante verificado exitosamente\n";
            return true;

        } catch (\Exception $e) {
            echo "Error verificando comprobante: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * Validaciones previas antes de crear comprobante en AFIP
     */
    private function validarAntes(array $venta): void
    {
        $errores = [];
        $actualizar_fecha = false;

        // Validar datos obligatorios
        if (!$venta['idcomportamiento']) {
            $errores[] = "Falta tipo de comprobante (idcomportamiento)";
        }

        if (!$venta['puntodeventa']) {
            $errores[] = "Falta punto de venta";
        }

        if (!$venta['numero']) {
            $errores[] = "Falta número de comprobante";
        }

        // Validar fecha
        if (!$venta['fecha']) {
            $errores[] = "Falta fecha del comprobante";
        } else {
            $fechaComprobante = strtotime($venta['fecha']);
            $diasAtras = strtotime('-10 days');
            $diasAdelante = strtotime('+1 day');

            if ($fechaComprobante > $diasAdelante || $fechaComprobante < $diasAtras) {
                $actualizar_fecha = true;
            }
        }

        // Validar totales
        if (!isset($venta['total']) || $venta['total'] <= 0) {
            $errores[] = "El total del comprobante debe ser mayor a cero";
        }

        // Validar IVA para facturas A
        if ($venta['discrimina'] === 'A' && (!isset($venta['cuit']) || strlen($venta['cuit']) != 11)) {
            $errores[] = "Para facturas A es obligatorio un CUIT válido del cliente";
        }

        // Validar concepto y fechas de servicio
        if (isset($venta['concepto']) && $venta['concepto'] > 1) {
            if (!$venta['vencimiento1']) {
                $errores[] = "Para servicios es obligatorio informar fecha de vencimiento";
            }
            if (!$venta['fechainicio'] || !$venta['fechafin']) {
                $errores[] = "Para servicios es obligatorio informar período del servicio";
            }
        }

        // Si hay errores, lanzar excepción
        if (!empty($errores)) {
            $exception = new Exception("Error que requiere actualizar la venta");
            $exception->estado = 'RECHAZADO';
            $exception->actualizar = [
                'estadocae' => 'rechazado',
                'obscae' => implode(', ', $errores)
            ];
            throw $exception;
        }

        // Si va bien y sólo tengo que actualizar la fecha
        if ($actualizar_fecha) {
            $this->actualizarVenta($venta['idventa'], '', [
                'fecha' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * Actualiza el número de venta siguiendo la lógica de AFIP
     */
    private function actualizarNumero(array $venta, int $ultimonumero): array
    {
        $idventa = $venta['idventa'];

        // Obtener comprobaciones necesarias
        $sql = "SELECT
                (SELECT idventa FROM ventas
                    WHERE estado = 'cerrado'
                        AND estadocae = 'aprobado'
                        AND idtipoventa = ?
                        AND numero = (? - 1)
                    LIMIT 1) AS numero_anterior,
                (SELECT ultimo_idventa_aprobado FROM categorias_ventas
                    WHERE idtipoventa = ?
                    LIMIT 1) AS ultimo_idventa_aprobado,
                (SELECT idventa FROM ventas
                    WHERE estado = 'cerrado'
                        AND estadocae = 'aprobado'
                        AND idtipoventa = ?
                        AND numero = ?
                    LIMIT 1) AS mismo_numero,
                (SELECT idventa FROM ventas
                    WHERE estado = 'cerrado'
                        AND estadocae = 'aprobado'
                        AND idtipoventa = ?
                        AND numero = (? + 1)
                    LIMIT 1) AS numero_siguiente";

        $resultado = $this->db->consultaSql($sql, [
            $venta['idtipoventa'], $venta['numero'],
            $venta['idtipoventa'],
            $venta['idtipoventa'], $venta['numero'],
            $venta['idtipoventa'], $venta['numero']
        ]);

        $comprobaciones = $resultado[0] ?? [];

        echo "Comprobaciones numeración: " . json_encode($comprobaciones) . "\n";
        echo "Último número AFIP: $ultimonumero, Número venta: {$venta['numero']}\n";

        // Es el primer comprobante a aprobar
        if (is_numeric($ultimonumero) // Es un número
            && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
            && !$comprobaciones['numero_siguiente'] // NO hay una venta con el número siguiente
            && !$comprobaciones['numero_anterior'] // NO hay una venta con el número anterior
        ) {
            $nuevoNumero = $ultimonumero + 1;
            $this->actualizarNumeracionVenta($idventa, $venta, $nuevoNumero);

            return [
                'success' => true,
                'numero' => $nuevoNumero,
                'mensaje' => "Primer comprobante: número actualizado a $nuevoNumero"
            ];
        }

        // Es el número correcto
        if ($venta['numero'] == $ultimonumero + 1 // Es el próximo número a solicitar
            && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
            && !$comprobaciones['numero_siguiente'] // NO hay una venta con el número siguiente
            && $comprobaciones['numero_anterior'] // SI hay una venta con el número anterior
        ) {
            return [
                'success' => true,
                'numero' => $venta['numero'],
                'mensaje' => "Número correcto: {$venta['numero']}"
            ];
        }

        // Es el último número aprobado (porque se perdió la conexión pero AFIP aprobó)
        if ($venta['numero'] == $ultimonumero // Es el mismo número
            && !$comprobaciones['numero_siguiente'] // NO hay una venta con el número siguiente
            && $comprobaciones['numero_anterior'] // SI hay una venta con el número anterior
            && $this->verificarComprobanteAnterior($comprobaciones['numero_anterior']) // Está ok el número anterior
            && $this->verificarComprobanteExistente($venta, [], false) // Está ok esta venta excepto el CAE
        ) {
            // En este caso la factura ya está aprobada en AFIP, solo hay que obtener el CAE
            return [
                'success' => true,
                'numero' => $venta['numero'],
                'ya_aprobado' => true,
                'mensaje' => "Comprobante ya aprobado en AFIP: {$venta['numero']}"
            ];
        }

        // Es un número viejo (recuperable si todos los datos coinciden)
        if ($venta['numero'] < $ultimonumero + 1 // Es un número anterior
            && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
            && $comprobaciones['numero_siguiente'] // SI hay UNA venta con el número siguiente
            && $comprobaciones['numero_anterior'] // SI hay una venta con el número anterior
            && $this->verificarComprobanteAnterior($comprobaciones['numero_anterior']) // Está ok el número anterior
            && $this->verificarComprobanteExistente($venta, [], false) // Está ok esta venta excepto el CAE
        ) {
            // En este caso la factura ya está aprobada en AFIP, solo hay que obtener el CAE
            return [
                'success' => true,
                'numero' => $venta['numero'],
                'ya_aprobado' => true,
                'mensaje' => "Número viejo recuperable: {$venta['numero']}"
            ];
        }

        // Es un número viejo (no recuperable, usar otro sistema o faltan números)
        if ($venta['numero'] < $ultimonumero + 1 // Es un número anterior
            && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
            && $comprobaciones['numero_anterior'] // SI hay una venta con el número anterior
            && !$this->verificarComprobanteExistente($venta, [], false) // NO está ok esta venta
        ) {
            if ($this->sinControlNumeracion()) {
                $nuevoNumero = $ultimonumero + 1;
                $this->actualizarNumeracionVenta($idventa, $venta, $nuevoNumero, true);

                return [
                    'success' => true,
                    'numero' => $nuevoNumero,
                    'mensaje' => "Sin control numeración: número actualizado a $nuevoNumero"
                ];
            }

            return [
                'success' => false,
                'action' => 'ERROR',
                'motivo' => 'Faltan números correlativos en la numeración',
                'enviar_mail' => true,
                'debug' => [
                    'ultimonumero' => $ultimonumero,
                    'numero_venta' => $venta['numero'],
                    'comprobaciones' => $comprobaciones
                ]
            ];
        }

        // Es un número adelantado (facturas hechas con otro sistema)
        if ($venta['numero'] > $ultimonumero + 1 // Es un número mayor
            && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
            && !$comprobaciones['numero_siguiente'] // NO hay UNA venta con el número siguiente
            && !$comprobaciones['numero_anterior'] // NO hay una venta con el número anterior
            && !$this->verificarComprobanteExistente($venta, [], false) // NO está ok esta venta
        ) {
            return [
                'success' => false,
                'action' => 'SINCAE',
                'motivo' => 'Numeración faltante: se hicieron facturas con otro sistema',
                'enviar_mail' => false
            ];
        }

        // Es un número que quedó atrás y hay que actualizarlo
        if ($venta['numero'] < $ultimonumero + 1 // Es un número anterior
            && $comprobaciones['mismo_numero'] // SI hay una venta con este número
        ) {
            $nuevoNumero = $ultimonumero + 1;

            return [
                'success' => true,
                'numero' => $nuevoNumero,
                'actualizar' => true,
                'mensaje' => "Número duplicado: actualizado de {$venta['numero']} a $nuevoNumero"
            ];
        }

        // Es un número adelantado (numeración corrida por problema nuestro)
        $ultimoNumeroLocal = $this->db->consultaSql(
            "SELECT numero FROM ventas v
             LEFT JOIN categorias_ventas cv ON v.idtipoventa = cv.idtipoventa
             WHERE v.estado = 'cerrado'
             AND v.estadocae = 'aprobado'
             AND v.idtipoventa = ?
             ORDER BY v.numero DESC LIMIT 1",
            [$venta['idtipoventa']]
        );

        if ($venta['numero'] > $ultimonumero + 1 // Es un número mayor
            && !$comprobaciones['mismo_numero'] // NO hay una venta con este número
            && !$comprobaciones['numero_siguiente'] // NO hay UNA venta con el número siguiente
            && !empty($ultimoNumeroLocal) // SI hay una venta anterior local
            && $ultimoNumeroLocal[0]['numero'] == $ultimonumero // Y coincide con AFIP
            && $this->verificarUltimoComprobanteLocal($ultimoNumeroLocal[0], $venta['idtipoventa']) // Está ok la venta anterior
        ) {
            $nuevoNumero = $ultimonumero + 1;

            return [
                'success' => true,
                'numero' => $nuevoNumero,
                'actualizar' => true,
                'mensaje' => "Numeración corrida: actualizado de {$venta['numero']} a $nuevoNumero"
            ];
        }

        // Ningún caso identificado
        return [
            'success' => false,
            'action' => 'ERROR',
            'motivo' => 'Caso de numeración no identificado',
            'enviar_mail' => true,
            'debug' => [
                'ultimonumero' => $ultimonumero,
                'numero_venta' => $venta['numero'],
                'comprobaciones' => $comprobaciones
            ]
        ];
    }

    /**
     * Actualiza la numeración de una venta
     */
    private function actualizarNumeracionVenta(int $idventa, array $venta, int $nuevoNumero, bool $actualizarFecha = false): void
    {
        $campos = ['numero = ?'];
        $valores = [$nuevoNumero];

        if ($actualizarFecha) {
            $campos[] = 'fecha = ?';
            $valores[] = date('Y-m-d H:i:s');
        }

        $valores[] = $idventa;

        // Actualizar venta
        $sql = "UPDATE ventas SET " . implode(', ', $campos) . " WHERE idventa = ?";
        $this->db->consultaSql($sql, $valores);

        // Actualizar número en ventasxclientes
        $numeroCompleto = $this->formatearNumeroCompleto($venta, $nuevoNumero);
        $this->db->consultaSql(
            "UPDATE ventasxclientes SET numero = ? WHERE idtipoventa = ? AND id = ?",
            [$numeroCompleto, $venta['idtipoventa'], $idventa]
        );

        // Actualizar último número en categoría
        $this->db->consultaSql(
            "UPDATE categorias_ventas SET ultimonumero = ? WHERE idtipoventa = ?",
            [$nuevoNumero, $venta['idtipoventa']]
        );

        echo "Numeración actualizada: venta $idventa, nuevo número $nuevoNumero\n";
    }

    /**
     * Formatea el número completo del comprobante
     */
    private function formatearNumeroCompleto(array $venta, int $numero): string
    {
        $letra = $venta['letra'] ?? 'A';
        $puntoVenta = str_pad($venta['puntodeventa'], 5, '0', STR_PAD_LEFT);
        $numeroFormateado = str_pad($numero, 8, '0', STR_PAD_LEFT);

        return "{$letra}{$puntoVenta}-{$numeroFormateado}";
    }

    /**
     * Verifica si una empresa tiene control de numeración deshabilitado
     */
    private function sinControlNumeracion(): bool
    {
        $empresasSinControl = [8905, 8980, 11597, 11854];

        // Obtener idempresa desde la conexión actual de la base de datos
        $sql = "SELECT DATABASE() as db_name";
        $resultado = $this->db->consultaSql($sql);
        $dbName = $resultado[0]['db_name'] ?? '';

        // Extraer idempresa del nombre de la base de datos (asumiendo formato saas_XXXX)
        if (preg_match('/saas_(\d+)/', $dbName, $matches)) {
            $idempresa = (int)$matches[1];
            return in_array($idempresa, $empresasSinControl);
        }

        return false;
    }

    /**
     * Verifica un comprobante anterior por ID
     */
    private function verificarComprobanteAnterior(int $idventa): bool
    {
        if (!$idventa) {
            return false;
        }

        // Obtener datos del comprobante anterior
        $sql = "SELECT v.*, cv.idcomportamiento, cv.puntodeventa
                FROM ventas v
                LEFT JOIN categorias_ventas cv ON v.idtipoventa = cv.idtipoventa
                WHERE v.idventa = ? LIMIT 1";

        $resultado = $this->db->consultaSql($sql, [$idventa]);
        if (empty($resultado)) {
            return false;
        }

        $ventaAnterior = $resultado[0];

        // Por ahora solo verificamos que exista y esté aprobada
        // En el futuro se puede agregar verificación contra AFIP
        return $ventaAnterior['estadocae'] === 'aprobado' &&
               !empty($ventaAnterior['cae']) &&
               strlen($ventaAnterior['cae']) == 14;
    }

    /**
     * Verifica el último comprobante local
     */
    private function verificarUltimoComprobanteLocal(array $ultimoComprobante, int $idtipoventa): bool
    {
        // Verificar que el último comprobante local esté bien
        return !empty($ultimoComprobante['numero']) &&
               $ultimoComprobante['estadocae'] === 'aprobado' &&
               !empty($ultimoComprobante['cae']) &&
               strlen($ultimoComprobante['cae']) == 14;
    }

    /**
     * Obtiene y actualiza el CAE de un comprobante ya aprobado en AFIP
     */
    private function obtenerCaeExistente(array $venta, array $certificados): array
    {
        try {
            // Configurar AFIPSDK
            $afip = new Afip([
                'CUIT' => $venta['configuracion_cuit'],
                'production' => $this->isProduction,
                'cert' => file_get_contents($certificados['crt']),
                'key' => file_get_contents($certificados['key']),
            ]);

            // Obtener información del comprobante
            $comprobanteInfo = $afip->ElectronicBilling->getVoucherInfo(
                $venta['numero'],
                $venta['puntodeventa'],
                $venta['idcomportamiento']
            );

            if (!$comprobanteInfo || !isset($comprobanteInfo['CAE'])) {
                throw new \Exception("No se pudo obtener información del comprobante desde AFIP");
            }

            return [
                'success' => true,
                'CAE' => $comprobanteInfo['CAE'],
                'CAEFchVto' => $this->formatearFechaAfip($comprobanteInfo['CAEFchVto'] ?? ''),
                'mensaje' => 'CAE obtenido desde AFIP para comprobante existente'
            ];

        } catch (\Exception $e) {
            echo "Error obteniendo CAE existente: " . $e->getMessage() . "\n";
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Formatea fecha de AFIP (yyyymmdd) a formato Y-m-d
     */
    private function formatearFechaAfip(string $fechaAfip): string
    {
        if (strlen($fechaAfip) == 8) {
            return substr($fechaAfip, 0, 4) . '-' .
                   substr($fechaAfip, 4, 2) . '-' .
                   substr($fechaAfip, 6, 2);
        }
        return date('Y-m-d', strtotime('+10 days')); // Fecha por defecto
    }

    /**
     * Verifica y actualiza la fecha del comprobante si es necesario
     */
    private function actualizarFecha(array &$venta, int $idventa): bool
    {
        // Obtener fecha actual
        $hoy = date('Y-m-d');
        list($fechaComprobante, $hora) = explode(' ', $venta['fecha']);

        // Si la fecha del comprobante es futura, actualizarla a hoy
        if ($fechaComprobante > $hoy) {
            $nuevaFecha = $hoy . ' ' . $hora;

            // Actualizar en base de datos
            $this->db->consultaSql(
                "UPDATE ventas SET fecha = ? WHERE idventa = ?",
                [$nuevaFecha, $idventa]
            );

            // Actualizar el array local
            $venta['fecha'] = $nuevaFecha;

            echo "Fecha actualizada de $fechaComprobante a $hoy para venta $idventa\n";
            return true;
        }

        // Verificar que la fecha no sea muy antigua (más de 10 días)
        $fechaLimite = date('Y-m-d', strtotime('-10 days'));

        if ($fechaComprobante < $fechaLimite) {
            echo "ADVERTENCIA: Fecha del comprobante muy antigua: $fechaComprobante\n";
            // No actualizamos fechas muy antiguas automáticamente
            return false;
        }

        echo "Fecha del comprobante válida: $fechaComprobante\n";
        return true;
    }
}

// Manejo de ejecución
$handler = new AfipSdk();

// Modo CLI para pruebas locales
if (PHP_SAPI === 'cli' && isset($argv) && count($argv) === 3) {
    $idempresa = $argv[1];
    $idventa = $argv[2];
    echo "Modo prueba local: procesando $idempresa|$idventa\n";
    $handler->procesarMensaje("$idempresa|$idventa");
    exit;
}

return $handler;
