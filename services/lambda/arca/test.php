<?php
// Incluir el handler
require __DIR__ . '/public/afipsdk.php';

// Si se proporcionaron argumentos, usarlos
if ($argc !== 3) {
    echo "Uso: php test.php <idempresa> <idventa>\n";
    exit(1);
}

$idempresa = $argv[1];
$idventa = $argv[2];

// Crear una instancia del handler
$handler = new AfipSdk();

// Procesar el mensaje
echo "Ejecutando prueba con idempresa=$idempresa, idventa=$idventa\n";
$handler->procesarMensaje("$idempresa|$idventa");