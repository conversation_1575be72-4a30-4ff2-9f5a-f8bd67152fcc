#!/bin/bash

# Script de ayuda para consultar logs de facturas AFIP
# Uso: ./query-logs.sh [opción] [parámetros]

LOG_GROUP="afip-facturas-log"
REGION="sa-east-1"

show_help() {
    echo "🔍 Script de consulta de logs de facturas AFIP"
    echo ""
    echo "Uso: $0 [opción] [parámetros]"
    echo ""
    echo "Opciones:"
    echo "  recent [N]           - Últimos N logs (por defecto 20)"
    echo "  today               - Logs de hoy"
    echo "  yesterday           - Logs de ayer"
    echo "  date YYYY-MM-DD     - Logs de una fecha específica"
    echo "  stage STAGE         - Logs de un stage (dev/alfa/beta/prod)"
    echo "  empresa ID          - Logs de una empresa específica"
    echo "  estado ESTADO       - Logs por estado (APROBADO/ERROR/PAUSADO)"
    echo "  errors              - Solo errores"
    echo "  follow              - Seguir logs en tiempo real"
    echo "  help                - Mostrar esta ayuda"
    echo ""
    echo "Ejemplos:"
    echo "  $0 recent 50        - Últimos 50 logs"
    echo "  $0 stage prod       - Logs de producción"
    echo "  $0 empresa 161      - Logs de empresa 161"
    echo "  $0 date 2024-01-15  - Logs del 15 de enero"
    echo "  $0 errors           - Solo errores"
}

# Función para mostrar logs recientes
recent_logs() {
    local limit=${1:-20}
    echo "📋 Últimos $limit logs:"
    aws logs filter-log-events \
        --log-group-name "$LOG_GROUP" \
        --limit "$limit" \
        --region "$REGION" \
        --query 'events[*].[timestamp,message]' \
        --output table
}

# Función para logs de hoy
today_logs() {
    local start_time=$(date -d 'today 00:00:00' +%s)000
    echo "📅 Logs de hoy:"
    aws logs filter-log-events \
        --log-group-name "$LOG_GROUP" \
        --start-time "$start_time" \
        --region "$REGION" \
        --query 'events[*].[timestamp,message]' \
        --output table
}

# Función para logs de ayer
yesterday_logs() {
    local start_time=$(date -d 'yesterday 00:00:00' +%s)000
    local end_time=$(date -d 'today 00:00:00' +%s)000
    echo "📅 Logs de ayer:"
    aws logs filter-log-events \
        --log-group-name "$LOG_GROUP" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --region "$REGION" \
        --query 'events[*].[timestamp,message]' \
        --output table
}

# Función para logs de una fecha específica
date_logs() {
    local date_str="$1"
    if [[ ! "$date_str" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]]; then
        echo "❌ Error: Formato de fecha inválido. Usar YYYY-MM-DD"
        exit 1
    fi

    local start_time=$(date -d "$date_str 00:00:00" +%s)000
    local end_time=$(date -d "$date_str 23:59:59" +%s)000
    echo "📅 Logs del $date_str:"
    aws logs filter-log-events \
        --log-group-name "$LOG_GROUP" \
        --start-time "$start_time" \
        --end-time "$end_time" \
        --region "$REGION" \
        --query 'events[*].[timestamp,message]' \
        --output table
}

# Función para filtrar por stage
stage_logs() {
    local stage="$1"
    echo "🏷️  Logs del stage '$stage':"
    aws logs filter-log-events \
        --log-group-name "$LOG_GROUP" \
        --filter-pattern "\"stage\":\"$stage\"" \
        --region "$REGION" \
        --query 'events[*].[timestamp,message]' \
        --output table
}

# Función para filtrar por empresa
empresa_logs() {
    local empresa="$1"
    echo "🏢 Logs de la empresa $empresa:"
    aws logs filter-log-events \
        --log-group-name "$LOG_GROUP" \
        --filter-pattern "\"idempresa\":$empresa" \
        --region "$REGION" \
        --query 'events[*].[timestamp,message]' \
        --output table
}

# Función para filtrar por estado
estado_logs() {
    local estado="$1"
    echo "📊 Logs con estado '$estado':"
    aws logs filter-log-events \
        --log-group-name "$LOG_GROUP" \
        --filter-pattern "\"estado\":\"$estado\"" \
        --region "$REGION" \
        --query 'events[*].[timestamp,message]' \
        --output table
}

# Función para solo errores
error_logs() {
    echo "❌ Solo errores:"
    aws logs filter-log-events \
        --log-group-name "$LOG_GROUP" \
        --filter-pattern '"estado":"ERROR"' \
        --region "$REGION" \
        --query 'events[*].[timestamp,message]' \
        --output table
}

# Función para seguir logs en tiempo real
follow_logs() {
    echo "👁️  Siguiendo logs en tiempo real (Ctrl+C para salir):"
    aws logs tail "$LOG_GROUP" --follow --region "$REGION"
}

# Verificar que AWS CLI esté configurado
if ! command -v aws &> /dev/null; then
    echo "❌ Error: AWS CLI no está instalado"
    exit 1
fi

if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ Error: Credenciales de AWS no configuradas"
    exit 1
fi

# Procesar argumentos
case "${1:-help}" in
    "recent")
        recent_logs "$2"
        ;;
    "today")
        today_logs
        ;;
    "yesterday")
        yesterday_logs
        ;;
    "date")
        if [ -z "$2" ]; then
            echo "❌ Error: Especifica una fecha (YYYY-MM-DD)"
            exit 1
        fi
        date_logs "$2"
        ;;
    "stage")
        if [ -z "$2" ]; then
            echo "❌ Error: Especifica un stage (dev/alfa/beta/prod)"
            exit 1
        fi
        stage_logs "$2"
        ;;
    "empresa")
        if [ -z "$2" ]; then
            echo "❌ Error: Especifica un ID de empresa"
            exit 1
        fi
        empresa_logs "$2"
        ;;
    "estado")
        if [ -z "$2" ]; then
            echo "❌ Error: Especifica un estado (APROBADO/ERROR/PAUSADO)"
            exit 1
        fi
        estado_logs "$2"
        ;;
    "errors")
        error_logs
        ;;
    "follow")
        follow_logs
        ;;
    "help"|*)
        show_help
        ;;
esac
