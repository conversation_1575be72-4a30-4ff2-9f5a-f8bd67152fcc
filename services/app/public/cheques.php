<?php
//Especifico el nombre del módulo en plural y singular para ser utilizado en varias partes de inclusiones
$modulo = 'cajas';
$modulo_singular = 'caja';

//Ejecuto el archivo que se encarga de revisar que todo esté ok e incluye todas las librerías.
require 'cargadores/iniciar.php';

//Ejecuto el switch principal con el comando enviado en $a
switch ($a) {
    default:
    case 'buscar':
        html_inicio(false, true);
        html_encabezado();
        html_menu();
        html_cuerpo();
        $ventana = 'cheques_buscar';
        marco_inicio();
        include('ventanas/'.$ventana.'.php');
        marco_fin();
        html_cuerpo_fin();
        html_fin();
        break;

    case 'ver';
        if (!$id || !(array_sql(consulta_sql("SELECT * FROM cheques WHERE idcheque = '$id'"))))
            ir_inicio($i18n_funciones['url_incorrecto'], $modulo, $a, $id);
        if (!$_SESSION['perfil_cajas_ver'])
            ir_atras('No tiene permiso para ver cheques', $modulo, $a, $id);
        else {
            html_inicio();
            html_encabezado();
            html_menu();
            html_cuerpo();
            $ventana = 'cheques_ver';
            marco_inicio();
            include('ventanas/'.$ventana.'.php');
            marco_fin();
            html_cuerpo_fin();
            html_fin();
        }
        break;
}
