<?php
switch ($boton) {
    case $i18n_funciones[22]: // Aceptar
        $idproveedor = recibir_variable('proveedores_id');
        $idcompra = recibir_variable('idcompra');

        if (!$idproveedor || !$idcompra)
            break;

        $proveedor = obtener_datos_proveedor($idproveedor);
        $compra = obtener_datos_compra($idcompra);

        if ($compra['estado'] == 'cerrado') {
            consulta_sql("UPDATE compras SET
                idproveedor = '".$proveedor['idproveedor']."'
                WHERE idcompra = '".$id."'");

        } else {
            if(compras_obtener_habilitados($proveedor['idtipoiva'], $compra['discrimina'] == 'A')){
                consulta_sql("UPDATE compras SET
                    idproveedor = '".$proveedor['idproveedor']."',
                    idtipoiva = '".$proveedor['idtipoiva']."',
                    cuit = '".$proveedor['cuit']."',
                    razonsocial = '".escape_sql($proveedor['razonsocial'])."',
                    domicilio = '".escape_sql($proveedor['domicilio'])."',
                    idlocalidad = '".(!$proveedor['idlocalidad'] ? campo_sql(consulta_sql("SELECT idlocalidad FROM configuraciones LIMIT 1")) : $proveedor['idlocalidad'])."'
                    WHERE idcompra = '".$id."'
                ");
            } else {
                script_flotante('alerta', 'El proveedor tiene configurada como condición de iva '
                   .$proveedor['tipoiva']
                   .' y no se le puede emitir un comprobante '
                   .$compra['discrimina'], 5000);
                modal_cerrar();
            }
        }

        $idmoneda_proveedor_origen = idmoneda('proveedores', $compra['idproveedor']);
        $idmoneda_proveedor_destino = idmoneda('proveedores', $idproveedor);
        $total = cotizacion($idmoneda_proveedor_destino, $idmoneda_proveedor_origen,
            $compra['operacioninversa'] ? -$compra['total'] : $compra['total']);

        consulta_sql("UPDATE comprasxproveedores SET
                idproveedor = '{$proveedor['idproveedor']}',
                total = '{$total}'
            WHERE id = '$idcompra'
                AND idtipocompra > 0");

        /*consulta_sql("UPDATE comprasxproveedores SET
                idproveedor = '{$proveedor['idproveedor']}'
            WHERE id IN (SELECT idcomprapago FROM compraspagos WHERE idcompra = '$idcompra')
                AND idtipocompra <= 0");*/

        $resultado_sql = consulta_sql("SELECT idcompraxproveedor, total FROM comprasxproveedores WHERE id IN (SELECT idcomprapago FROM compraspagos WHERE idcompra = '$idcompra') AND idtipocompra <= 0");
        while ($comprasxproveedores = array_sql($resultado_sql)) {
            consulta_sql("UPDATE comprasxproveedores SET
                            idproveedor = '".$proveedor['idproveedor']."',
                            total = '".cotizacion($idmoneda_proveedor_destino, $idmoneda_proveedor_origen, $comprasxproveedores['total'])."'
                        WHERE idcompraxproveedor = '".$comprasxproveedores['idcompraxproveedor']."'");
        }

        consulta_sql("UPDATE compraspagos SET
                idproveedor = '{$proveedor['idproveedor']}'
            WHERE idcompra = '$idcompra'");

        if ($compra['estado'] == 'cerrado' && $compra['muevesaldo']) {
            $diferencia = obtener_saldo('compras', $idcompra);
            actualizar_saldo('proveedores', $compra['idproveedor'], - $diferencia);
            actualizar_saldo('proveedores', $idproveedor, cotizacion($idmoneda_proveedor_destino, $idmoneda_proveedor_origen, $diferencia));
        }

        $script = 'location.reload();';
        modal_cerrar($script);
        break;

    case $i18n_funciones[23]: // Cancelar
        modal_cerrar();
        break;

}
$compra = obtener_datos_compra($id);

ventana_inicio($i18n[310]);
{
    contenido_inicio($i18n[306]);
    {
        entrada('hidden', 'idcompra', '', $id);
        if ($compra['estado'] == 'cerrado') {
            texto('italica', false, $i18n[307], 'auto', false, 'info');
            bajo_linea();bajo_linea();
        }
        seleccionador('proveedores');
    }
    contenido_fin();

    botones(array(
        array('valor' => $i18n_funciones[22]),
        array('valor' => $i18n_funciones[23])
    ));
}
ventana_fin();
