<?php
 $i18n_informes = array(
    'habilitado' => 'Habilitado',
    'deshabilitado' => 'Deshabilitado',
    'estado_1' => 'Sin comenzar',
    'estado_2' => 'Comenzado',
    'estado_3' => 'Terminado',
    'estado_4' => 'Pausado',
    'estado_5' => 'Anulado',
    'prioridad_1' => 'Muy Alta',
    'prioridad_2' => 'Alta',
    'prioridad_3' => 'Normal',
    'prioridad_4' => 'Baja',
    'prioridad_5' => 'Muy Baja',
 	 1 => 'Comprobantes emitidos',
    2 => 'Filtro por tipo de venta',
    3 => 'Seleccionar tipo de venta',
    4 => 'Todos los tipo de venta',
    5 => 'Seleccionar modo de facturación',
    6 => 'Todos los modos de facturación',
    7 => 'Filtro por movimientos del comprobante',
    8 => 'Seleccionar comportamiento',
    9 => 'Todos los comportamientos',
    10 => 'Filtro por datos',
    11 => 'Seleccionar usuario/vendedor',
    12 => 'Todos los usuarios',
    13 => 'Seleccionar estado',
    14 => 'Todos los estados',
    15 => 'Seleccionar situación',
    16 => 'Todas las situaciones',
    17 => 'Filtro por fechas',
    18 => 'Fecha desde',
    19 => 'Fecha hasta',
    20 => 'Campos para mostrar',
    21 => 'Mostrar subtotales',
    22 => 'Campos opcionales para mostrar',
    23 => 'Usuarios/Vendedores',
    24 => 'Localidades',
    25 => 'Tipo y número de documento',
    26 => 'Generar informe',
    27 => 'Comprobante interno',
    28 => 'Factura electrónica',
    29 => 'Talonario fiscal manual o pre-impreso',
    30 => 'Factura en Linea RECEL',
    31 => 'Abierto',
    32 => 'Cerrado',
    33 => 'Anulado',
    34 => 'Generan movimientos de cuenta corriente y de stock',
    35 => 'Generan movimientos de cuenta corriente',
    36 => 'Generan movimientos de stock',
    37 => 'No generan movimientos de cuenta corriente, ni de stock',
    38 => 'Generan movimientos de cuenta corriente pero no de stock',
    39 => 'Generan movimientos de stock pero no de cuenta corriente',
    40 => 'Sin especificar',
    41 => 'Pendiente',
    42 => 'Aprobado',
    43 => 'Rechazado',
    44 => 'Sin subtotales',
    45 => 'Subtotales por día',
    46 => 'Subtotales por mes',
    47 => 'Subtotales por año',
    49 => 'Comprobantes recibidos',
    50 => 'Seleccionar tipo de compra',
    51 => 'Todos los tipos de compra',
    52 => 'Factura',
    53 => 'Remito',
    54 => 'Nota de crédito',
    55 => 'Nota de débito',
    56 => 'Remito de devolución',
    57 => 'Presupuesto',
    58 => 'Pedido',
    59 => '',
    60 => 'Seleccionar movimientos',
    61 => 'Todos los movimientos',
    62 => 'Salidas por venta',
    63 => 'Entradas por compra',
    64 => 'Seleccionar rubro',
    65 => 'Todos los rubros',
    66 => 'Seleccionar proveedor',
    67 => 'Todos los proveedores',
    68 => 'Seleccionar producto',
    69 => 'Todos los productos',
    70 => 'Filtro por texto',
    71 => 'Texto para filtrar',
    72 => 'Seleccione el campo',
    73 => 'Todos los campos que se pueden filtrar',
    74 => 'Nombre del producto',
    75 => 'Código del producto',
    76 => 'Observaciones del producto',
    77 => 'Observaciones de la venta',
    78 => 'Agrupar resultados',
    79 => 'Sin agrupar',
    80 => 'Por comprobantes',
    81 => 'Por productos',
    82 => 'Fecha',
    83 => 'Código',
    84 => 'Movimiento',
    85 => 'Producto',
    86 => 'IVA',
    87 => 'Cantidad Vendida',
    88 => 'Precio Unitario',
    89 => 'Cantidad Comprada',
    90 => 'Costo Unitario',
    91 => 'Stock inicial',
    92 => 'Stock actual',
    93 => 'Total ventas',
    94 => 'Lista de servicios',
    95 => 'Seleccionar estado del servicio',
    96 => 'Todos los estados',
    97 => 'Pendiente (Sin comenzar, comenzado y pausado)',
    98 => 'Sin comenzar',
    99 => 'Comenzado',
    100 => 'Terminado',
    101 => 'Pausado',
    102 => 'Cancelado',
    103 => 'Seleccionar categoria',
    104 => 'Todas las categorias servicios',
    105 => 'Seleccionar prioridad',
    106 => 'Todas las prioridades',
    107 => 'Muy Alta',
    108 => 'Alta',
    109 => 'Normal',
    110 => 'Baja',
    111 => 'Muy Baja',
    112 => 'Fecha de solicitado desde',
    113 => 'Fecha de solicitado hasta',
    114 => 'Fecha de terminado desde',
    115 => 'Fecha de terminado hasta',
    116 => 'Todas las observaciones',
    117 => 'Solicitado por el cliente',
    118 => 'Realizado en el servicio',
    119 => 'Observaciones internas',
    120 => 'Campo para ordenar el listado',
    121 => 'Prioridad',
    122 => 'N° de servicio',
    123 => 'N° de cliente',
    124 => 'Estado de servicio',
    125 => 'Categoría',
    126 => 'Fecha de solicitado',
    127 => 'Fecha de terminado',
    128 => 'Tipo de orden',
    129 => 'Ascendente',
    130 => 'Descendente',
    131 => 'Categorias',
    132 => 'Fecha límite',
    133 => 'Asunto',
    134 => 'Localidad',
    135 => 'Tiempo estimado',
    136 => 'Tiempo dedicado',
    137 => 'Detalle de lo solicitado por el cliente',
    138 => 'Detalle de lo realizado en el servicio',
    139 => 'Detalle de las observaciones internas',
    140 => 'N°',
    141 => 'Cliente',
    142 => 'Usuario',
    143 => 'Título',
    144 => 'Acciones',
    145 => 'Modificar este servicio',
    156 => 'Eliminar este servicio',
    157 => 'Lista de clientes',
    158 => 'Seleccionar localidad',
    159 => 'Todas las localidades',
    160 => 'Seleccionar categoría del cliente',
    161 => 'Todas las categorias de cliente',
    162 => 'Seleccionar estado de cliente',
    163 => 'Todos los estados de cliente',
    164 => 'Habilitado',
    165 => 'No Habilitado',
    166 => 'Seleccionar Condición de IVA',
    167 => 'Todas las condiciones de IVA',
    168 => 'Consumidor Final',
    169 => 'Responsable Inscripto',
    170 => 'Monotributista',
    171 => 'Responsable No Inscripto',
    172 => 'Exento',
    173 => 'Nombre',
    174 => 'Contacto',
    175 => 'Telefonos',
    176 => 'Opciones de búsqueda',
    177 => 'Filtrar palabras completas únicamente',
    178 => 'Solamente clientes de contactos',
    179 => 'Domicilio',
    180 => 'Condicion de IVA',
    181 => 'Categoría del cliente',
    182 => 'Teléfonos',
    183 => 'Mail',
    184 => 'Ranking de clientes',
    185 => 'Mostrar',
    186 => 'Los 10 más facturados',
    187 => 'Los 100 más facturados',
    188 => 'Los 10 menos facturados',
    189 => 'Los 100 menos facturados',
    190 => 'Los que no tienen facturado',
    191 => 'Comprobantes vencidos',
    192 => 'Seleccionar tipo de vencimiento',
    193 => 'Comprobantes con fecha de vencimiento en el período',
    194 => 'Comprobantes vencidos con saldo y sin pagos relacionados',
    195 => 'Vencimiento desde',
    196 => 'Vencimiento hasta',
    197 => 'Recibos de pagos',
    198 => 'Seleccionar forma de pago',
    199 => 'Todas las formas de pago',
    200 => 'Productos en ventas',
    201 => 'Filtro por movimientos del comprobante',
    202 => 'Opciones',
    203 => 'Utilizar valores sin IVA',
    204 => 'Por clientes',
    205 => 'Por productos',
    206 => 'Por tipos de venta',
    207 => 'Subdiario de IVA VENTAS mensual',
    208 => 'Período del subdiario',
    209 => 'Ver subdiario',
    210 => 'Seleccionar período',
    211 => 'Subdiario de IVA COMPRAS mensual',
    212 => 'Cajas cerradas',
    213 => 'Seleccionar categoría de caja',
    214 => 'Todas las cajas',
    215 => 'Movimientos de cajas',
    216 => 'Seleccionar concepto',
    217 => 'Todos los conceptos',
    218 => 'Ranking de servicios',
    219 => 'Seleccionar categoría del proveedor',
    220 => 'Todos los tipos de proveedor',
    221 => 'N° de proveedor',
    222 => 'Todos los tipos de proveedores',
    223 => 'Ordenes de pago',
    224 => 'Productos comprados',
    225 => 'Observaciones de la compra',
    226 => 'Productos por proveedor',
    227 => 'Razón Social',
    228 => 'Mail',
    229 => 'Conceptos incluidos',
    230 => 'Provincias',
    231 => 'Todos los comprobantes fiscales',
    232 => 'Nº de comprobante de venta (Sin punto de venta)',
    233 => 'CAE (completo)',
    234 => 'Subtotales por localidad',
    235 => 'Subtotales por provincias',
    236 => 'Subtotales por usuario/vendedor',
    237 => 'Filtro por clientes',
    238 => 'Filtrar por cliente',
    239 => 'Filtro por categorías',
    240 => 'Todos los clientes',
    241 => 'Filtrar por categorias',
    242 => 'Lista de productos',
    243 => 'Filtro por proveedores',
    244 => 'Filtrar por proveedor',
    245 => 'Filtrar por rubro de producto',
    246 => 'Habilitados para la venta',
    247 => 'Habilitados para la compra',
    248 => 'Mostrar sólo productos con stock',
    249 => 'Mostrar sólo productos por debajo del stock ideal',
    250 => 'Filtro por stock',
    251 => 'Por proveedor',
    252 => 'Por rubro',
    253 => 'Unidad',
    254 => 'Costo',
    255 => 'Precio',
    256 => 'Alicuota IVA',
    257 => 'Precio final',
    258 => 'Stock ideal',
    259 => 'Stock mínimo',
    260 => 'Descripción para la venta',
    261 => 'Datos extra',
    262 => 'N° publicación ML',
    263 => 'Filtro por productos compuestos',
    264 => 'Productos que integran un producto compuesto',
    265 => 'Productos compuestos',
    266 => 'Proveedor',
    267 => 'Rubro',
    268 => 'Código de proveedor',
    269 => 'Habilitados',
    270 => 'Sumar todos los valores listados',
    271 => 'Independientemente al movimiento generado por los comprobantes, esta opción suma todos los valores de los registros del informe',
    272 => 'Subtotal por clientes',
    273 => 'Subtotal por productos',
    274 => 'Subtotal por tipos de ventas',
    275 => 'Subtotal por rubro',
    276 => 'Mostrar solo subtotales',
    277 => 'Datos extra del producto',
    278 => 'Filtrar por producto',
    279 => 'Filtrar por rubro',
    280 => 'Disponibles para la venta',
    281 => 'Disponibles para la compra',
    282 => 'Deshabilitados',
    283 => 'No disponibles para la venta',
    284 => 'No disponibles para la compra',
    285 => 'No tiene compras agregadas',
    286 => 'No tiene ventas agregadas',
    288 => 'Información de iva para mostrar',
    289 => 'Impuestos para mostrar',
    290 => 'Lista de precios',
    291 => 'Depósito',
    292 => 'Utilidad',
    293 => 'Todas las listas de precios',
    294 => 'Todos los depósitos',
    295 => 'Total de stock en todos los depósitos',
    296 => 'Stock valorizado en todos los depósitos',
    297 => 'Calculado desde el total de stock en todos los depósitos multiplicando el costo sin iva',
    298 => 'Ver informe anterior',
    299 => 'Si desea ver la versión anterior de este informe haga un clic aquí',
    300 => 'Neto No Gravado',
    301 => 'Neto Exento',
    302 => 'Código universal de producto (UPC)',
    303 => 'Subtotal por Proveedor',
    304 => 'Subtotal por tipos de compras',
    305 => 'Filtro por tipo de compra',
    306 => 'Todos los tipo de compra',
    307 => 'CUIT',
    308 => 'Resumen de movimientos',
    309 => 'Clientes deudores',
    310 => 'Puede especificar una fecha para obtener el saldo a una fecha determinada o dejar en blanco para obtener el saldo actual',
    311 => 'Saldo a proveedores',
    312 => 'Lista de proveedores',
    313 => 'Generan movimientos de cuenta corriente y tienen saldo deudor',
    314 => 'Datos de operación de MercadoLibre',
    315 => 'Productos habilitados para integrar un producto compuesto',
    316 => 'Observaciones del recibo de pago y orden de pago',
    317 => 'Seleccionar caja',
    318 => 'Todas las cajas',
    319 => 'Subtotales por cajas',
    320 => 'Ranking de productos en ventas',
    321 => 'Proveedor predeterminado',
    322 => 'Ranking',
    323 => 'Criterio de ranking',
    324 => 'Mayor cantidad vendida',
    325 => 'Menor cantidad vendida',
    326 => 'Mayor importe total',
    327 => 'Menor importe total',
    328 => 'Mayor utilidad',
    329 => 'Menor utilidad',
    330 => 'Mostrar 10 resultados',
    331 => 'Mostrar 100 resultados',
    332 => 'Cantidad para mostrar',
    333 => 'Clientes',
    334 => 'Proveedores',
    335 => 'Mostrar los productos compuestos relacionados',
    336 => 'Depósito',
    367 => 'Facturas electrónicas pendientes de CAE',
    368 => 'Estado de CAE',
    369 => 'Pendientes de CAE',
    370 => 'CAEs rechazados',
    371 => 'CAEs aprobados',
    372 => 'Filtro por fecha de emisión',
    373 => 'Desde',
    374 => 'Hasta',
    375 => 'Se incluirán los sub-conceptos en el informe',
    376 => 'Descargar informe',
    377 => 'Saldo',
    378 => ' de la venta',
    379 => ' del cliente',
    380 => 'Concepto de movimiento de caja',
    381 => 'Incluir sub-rubros',
    382 => 'Vendedor',
    393 => 'El sistema intenta automáticamente cada 15 minutos obtener la autorización de las facturas pendientes de CAE. Para este proceso no es necesaria su intervención o contacto con soporte.',
    394 => 'Tiene {cantidad_pendientes} facturas pendientes de obtener CAE',
    395 => 'Puede ver más información aquí',
    396 => 'No tiene facturas pendientes de CAE',
    397 => 'Seleccione rango de fechas',
    398 => 'Últimas 48 hs',
    399 => 'Últimos 30 días',
    400 => 'Últimos 60 días',
    401 => ' de la compra',
    402 => ' del proveedor',
    403 => 'Moneda',
    404 => 'Filtro por moneda',
    405 => 'El informe se generará con los registros almacenados en la moneda seleccionada',
    406 => 'Todas las fechas',
    407 => 'Hoy',
    408 => 'Ayer',
    409 => 'Esta semana',
    410 => 'Semana anterior',
    411 => 'Últimos 7 días',
    412 => 'Este mes',
    413 => 'Mes anterior',
    414 => 'Últimos 30 días',
    415 => 'Este año',
    416 => 'Año anterior',
    417 => 'Últimos 12 meses',
    418 => 'Personalizado',
    419 => 'Seleccione fechas',
    420 => 'Tipo de informe',
    421 => 'Clientes habilitados con saldo deudor en sus cuentas corrientes',
    422 => 'Clientes habilitados con saldo acreedor en sus cuentas corrientes',
    423 => 'Clientes deshabilitados con saldo diferente de cero',
    424 => 'Seleccionar moneda',
    425 => 'Seleccionar tipo de informe',
    426 => 'Todas las monedas',
    427 => 'Tolerancia para considerar deuda',
    428 => 'Fecha de última venta',
);
