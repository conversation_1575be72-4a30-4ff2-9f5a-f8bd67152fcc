<?php

use Aws\Common\Aws;
use Guzzle\Http\EntityBody;

function conectar_aws($servicio)
{
    try {
        switch ($servicio) {
            case 's3':
                $aws = Aws::factory([
                    'key' => AWS_KEY,
                    'secret' => AWS_SECRET,
                    'region' => AWS_REGION])
                    ->get('s3');
                break;

            case 'sqs':
                $aws = Aws::factory([
                    'key' => AWS_SQS_QUEUER_KEY,
                    'secret' => AWS_SQS_QUEUER_SECRET,
                    'region' => AWS_REGION])
                    ->get('sqs');
                break;
        }
        return $aws;

    } catch (Exception $e) {
        mostrar_error('Error al conectarse con AWS '.$servicio, true);
    }
}

function archivos_baja($ids = array())
{
    global $modulo;
    global $id;

    if (!AWS_KEY)
        return true;

    $s3 = conectar_aws('s3');

    if (count($ids)) {
        $str_ids = '';
        foreach ($ids as $value)
            $str_ids.= "'$value',";
        $str_ids[mb_strlen($str_ids) - 1] = ' ';
        $resultado_sql = consulta_sql("SELECT iua, archivo, url, tipo, idusuario FROM archivos WHERE idarchivo IN ($str_ids)");

    } else {
        $resultado_sql = consulta_sql("SELECT iua, archivo, url, tipo, idusuario FROM archivos WHERE modulo='".$modulo."' AND id='".$id."'");
    }

    while ($archivo = array_sql($resultado_sql)) {

        if (!$_SESSION['perfil_archivos_baja_todos'] && $archivo['idusuario'] != $_SESSION['usuario_idusuario'])
            return false;

        $objetos = array();
        if ($archivo['tipo'] == 'imagen') {
            $objetos[] = array('Key' => $archivo['iua'].'/imagen');
            $objetos[] = array('Key' => $archivo['iua'].'/miniatura');
        }
        $objetos[] = array('Key' => $archivo['iua'].'/'.$archivo['archivo']);
        $objetos[] = array('Key' => $archivo['iua']);

        if (!$s3->deleteObjects(array('Bucket' => AWS_BUCKET, 'Objects' => $objetos)))
            return false;

    }

    if (count($ids))
        consulta_sql("DELETE FROM archivos WHERE idarchivo IN ($str_ids)");
    else
        consulta_sql("DELETE FROM archivos WHERE modulo = '$modulo' AND id = '$id'");

    return true;
}

function existe_wsfe($cuit, $extension)
{
    $local_file = PATH_WSFE.$cuit.'.'.$extension;
    $s3_file = $cuit.'/'.$cuit.'.'.$extension;

    if (is_readable($local_file))
        return true;

    if (ESTADO == 'desarrollo' && AWS_SECRET == '')
        return false;

    $s3 = conectar_aws('s3');
    if (!$s3->doesObjectExist(AWS_WSFE, $cuit.'/'.$cuit.'.'.$extension))
        return false;

    descargar_wsfe($cuit, $extension);
    return true;
}

function descargar_wsfe($cuit, $unica_extension = false)
{
    $s3 = conectar_aws('s3');
    $extensiones = array('key', 'req', 'ini', 'crt');

    foreach ($extensiones as $extension) {
        if (!$unica_extension || $unica_extension == $extension) {

            $local_file = PATH_WSFE.$cuit.'.'.$extension;
            $s3_file = $cuit.'/'.$cuit.'.'.$extension;
            if (is_readable($local_file))
                continue;

            if ($s3->doesObjectExist(AWS_WSFE, $s3_file)) {
                $objeto = $s3->getObject(array(
                    'Bucket' => AWS_WSFE,
                    'Key'    => $s3_file));

                file_put_contents($local_file, $objeto['Body']);
                continue;
            }

            mostrar_error("Se solicitó descargar_wsfe pero la extension $extension no existe para el cuit $cuit", true);
        }
    }
}

function subir_wsfe($cuit)
{
    $s3 = conectar_aws('s3');
    $extensiones = array('key', 'req', 'ini');

    foreach ($extensiones as $extension) {
        $s3->putObject(array(
            'Bucket' => AWS_WSFE,
            'Key' => $cuit .'/'.$cuit.'.'.$extension,
            'Body' => Guzzle\Http\EntityBody::factory(fopen(PATH_WSFE.$cuit.'.'.$extension, 'r')),
        ));
    }
}

function baja_wsfe($cuit, $extension)
{
    $local_file = PATH_WSFE.$cuit.'.'.$extension;
    $s3_file = $cuit.'/'.$cuit.'.'.$extension;

    $s3 = conectar_aws('s3');
    if (!$s3->doesObjectExist(AWS_WSFE, $s3_file)) {
        unlink(PATH_WSFE.$cuit.'.'.$extension);
        return true;
    }

    if ($s3->deleteObjects(array(
        'Bucket' => AWS_WSFE,
        'Objects' => array(array('Key' => $s3_file))))) {
       unlink(PATH_WSFE.$cuit.'.'.$extension);
       return true;
    }
}

function crear_png($temp_imagen)
{
    $colorTransparente  = imagecolorallocatealpha($temp_imagen, 0, 0, 0, 127); // 127 = transparente
    //imagecolortransparent($temp_imagen, $colorTransparente);
    imagefill($temp_imagen, 0, 0, $colorTransparente);
    imagesavealpha($temp_imagen, true);

    return $temp_imagen;
}

function email_queue($remitente, $destinatario, $asunto, $texto, $copia_remitente = false, $adjuntos = [])
{
    // SQS Message must be shorter than 262144 bytes
    if ((strlen($texto)
        + (count($adjuntos) && isset($adjuntos[0]['valor']) ? strlen($adjuntos[0]['valor']) : 0)) > 260000) { // 256 KB
            mostrar_error('No se envió un mail con SQS porque el texto es demasiado largo: '.(strlen($texto) + strlen($adjuntos[0]['valor'])).' bytes', true);
        return false;
    }

    try {
        $sqs = conectar_aws('sqs');

        if (is_array($destinatario)) {
            $copia = $destinatario[1];
            $destinatario = $destinatario[0];
        } else {
            $copia = false;
        }

        if (is_array($remitente)) {
            $remitente_nombre = current($remitente);
            $remitente_mail = key($remitente);
        } else {
            $remitente_mail = $remitente_nombre = $remitente;
        }

        if ($remitente_mail == '<EMAIL>') {
            mostrar_error('Intento de <NAME_EMAIL> con el texto:<br>'.$texto, true);
            return true;
        }

        $messageAttributes = [
            "Subject" => [
                'DataType' => "String",
                'StringValue' => $asunto
            ],
            "To" => [
                'DataType' => "String",
                'StringValue' => $destinatario
            ],
            "From" => [
                'DataType' => "String",
                'StringValue' => $remitente_mail
            ],
            "FromName" => [
                'DataType' => "String",
                'StringValue' => $remitente_nombre
            ],
        ];

        if ($copia)
            $messageAttributes['Cc'] = [
                'DataType' => "String",
                'StringValue' => $copia
            ];
        if ($copia_remitente)
            $messageAttributes['Bcc'] = [
                'DataType' => "String",
                'StringValue' => $remitente_mail
            ];

        if (count($adjuntos)) {
            $messageAttributes['FileName'] = [
                'DataType' => 'String',
                'StringValue' => $adjuntos[0]['nombre'],
            ];
            $messageAttributes['FileContent'] = [
                'DataType' => 'String',
                'StringValue' => base64_encode($adjuntos[0]['valor']),
            ];
        }

        $params = [
            'QueueUrl' => AWS_URL_EMAIL_QUEUE,
            'DelaySeconds' => 10,
            'MessageBody' => $texto.'<br><br><hr>Enviado desde <a href="'.URL_SITE.'">'.URL_HOST.'</a>',
            'MessageAttributes' => $messageAttributes,
        ];

        $sqs->sendMessage($params);

    } catch (Exception $e) {
        mostrar_error('No se envió un mail con SQS.<br>'
            .'Error: '.$e->getMessage().'<br>'
            .'Parametros: '.json_encode($params), true);
        return false;
    }
    return true;
}

function afipsdk_lambda($idempresa, $idventa)
{
    try {
        $sqs = conectar_aws('sqs');

        $params = [
            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE.'-'.strtolower($_SESSION['servidor_version']),
            'DelaySeconds' => 10,
            'MessageBody' => "$idempresa|$idventa",
        ];

        $sqs->sendMessage($params);

    } catch (Exception $e) {
        mostrar_error('No se envió un mensaje con SQS.<br>'
            .'Error: '.$e->getMessage().'<br>'
            .'Parametros: '.json_encode($params), true);
        return false;
    }
    return true;
}
