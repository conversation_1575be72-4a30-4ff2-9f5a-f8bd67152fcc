<?php

function saldo_empresa($idcliente)
{
    // Si no tiene idcliente hay algún error en la configuración de la empresa que hay que revisar
    if (!$idcliente) {
        mostrar_error('Empresa sin idcliente', true);
        return 0;

    } else {
        $saldo = obtener_saldo('clientes', $idcliente, 'admin');
        $_SESSION['saldo'] = $saldo > SALDO_MINIMO_INFORMAR
            ? $saldo
            : false;

	    return $saldo;
    }
}

function crear_caja_mp()
{
    consulta_sql("INSERT INTO cajas SET
        idtipocaja = 0,
        fechaapertura = now()");
    $idcaja = id_sql();

    consulta_sql("INSERT INTO categorias_cajas SET
        nombre = 'MercadoPago',
        compartida = 1,
        estado = 1,
        idcaja = '$idcaja',
        tipo = 'banco'");
    $idtipocaja = id_sql();

    consulta_sql("UPDATE cajas SET
        idtipocaja = '$idtipocaja'
        WHERE idcaja = '$idcaja'");
    insertar_saldo('cajas', $idcaja);

    return $idtipocaja;
}

function cerrar_pago_configuraciones($datos) {

    $idcliente = campo_sql(consulta_sql(
        "SELECT empresas.idcliente
        FROM empresas
        WHERE empresas.idempresa = '".$_SESSION['empresa_idempresa']."'
        LIMIT 1", 'saasargentina'), 0);

    $venta = array_sql(consulta_sql(
        "SELECT total, id AS idventa, idtipoventa, numero
        FROM ventasxclientes
        WHERE idcliente = '$idcliente'
            AND idtipoventa > 0
        ORDER BY fecha DESC LIMIT 1",
        'admin'));

    $idcaja = campo_sql(consulta_sql(
        "SELECT idcaja FROM categorias_cajas WHERE idtipocaja = '".IDTIPOCAJA_DEFAULT."'", 'admin'), 0);

    // Si no está pagando el mismo valor que la última factura, va como pago a cuenta
    if ($venta['total'] != $_SESSION['saldo'])
        $venta = [];

    // Por el momento sólo aceptamos pagos en Pesos
    // $buscar_monedas = [
    //     'clientes' => $idcliente,
    //     'ventas' => $venta['idventa'],
    //     'cajas' => $idcaja,
    // ];
    // $idmonedas = idmonedas($buscar_monedas);
    $idmonedas = [
        'ventas' => '1',
        'clientes' => '1',
        'cajas' => '1',
    ];

    $fecha = date('Y-m-d H:i:s');
    consulta_sql("INSERT INTO ventaspagos SET
        idcliente = '".$idcliente."',
        idusuario = '0',
        idventa = '".$venta['idventa']."',
        fecha = '".$fecha."',
        idformapago = '".IDFORMAPAGO_DEFAULT."',
        total = '".$datos['importe']."'",
        'admin');

    $idventapago = campo_sql(consulta_sql(
        "SELECT idventapago FROM ventaspagos
        WHERE idcliente = '".$idcliente."'
            AND idventa = '".$venta['idventa']."'
            AND fecha = '".$fecha."' LIMIT 1",
        'admin'), 0);

    consulta_sql(
        "UPDATE ventaspagos SET idnumeroventapago = '".$idventapago."'
        WHERE idventapago = '$idventapago'", 'admin');

    consulta_sql("INSERT INTO ventasxclientes SET
        idcliente = '".$idcliente."',
        idtipoventa = '0',
        id = '".$idventapago."',
        fecha = NOW(),
        total = '".cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $datos['importe'])."',
        numero = '".$venta['numero']."'
    ", 'admin');

    consulta_sql("INSERT INTO movimientosxcajas SET
        idcaja = $idcaja,
        idusuario = '0',
        idconcepto = '".IDCONCEPTO_VENTA."',
        fecha = '".fecha_sql($datos['fecha'])."',
        total = '".cotizacion($idmonedas['cajas'], $idmonedas['ventas'], $datos['importe'])."',
        detalle = 'Recibo de pago RP".completar_numero($idventapago, 8)."',
        tiporelacion = 'clientepago',
        idrelacion = '".$idventapago."'
    ", 'admin');

    $diferencia = $datos['importe'] * -1;
    if ($venta['idventa']) {
        actualizar_saldo('ventas', $venta['idventa'], $diferencia, 'admin');
    }
    actualizar_saldo('clientes', $idcliente, cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia), 'admin');

    if ($_SESSION['empresa_estado'] == 'vencida' && saldo_empresa($idcliente) < SALDO_MINIMO) {
        activar_empresa($_SESSION['empresa_idempresa'], $idcliente);
        mensajes_alta('Gracias por informar su pago. Reactivamos su empresa y registramos el pago en su estado de cuenta, mientras está pendiente de verificación en banco.', 'Confirmacion');
        email_queue(MAIL_SERVIDOR, MAIL_INFO, 'Nuevo pago por transferencia y empresa reactivada',
            'La empresa <a href="'.URL_SAAS.'/saas.php?a=ver&id='.$_SESSION['empresa_idempresa'].'">'.$_SESSION['empresa_idempresa'].'</a> ha cargado el pago <a href="'.URL_SAAS.'/ventas.php?a=verpago&id='.$idventapago.'">RP'.completar_numero($idventapago, 8).'</a> para verificar.<br>La empresa fue reactivada.');

    } else {
        mensajes_alta('Gracias por informar su pago. Ya fue registrado en su estado de cuenta, pendiente de verificación en banco.', 'Confirmacion');
        email_queue(MAIL_SERVIDOR, MAIL_INFO, 'Nuevo pago por transferencia',
            'La empresa <a href="'.URL_SAAS.'/saas.php?a=ver&id='.$_SESSION['empresa_idempresa'].'">'.$_SESSION['empresa_idempresa'].'</a> ha cargado el pago <a href="'.URL_SAAS.'/ventas.php?a=verpago&id='.$idventapago.'">RP'.completar_numero($idventapago, 8).'</a> para verificar.');
    }

    return $idventapago;
}

function activar_empresa($idempresa, $idcliente)
{
    consulta_sql("UPDATE empresas SET estado = 'activada' WHERE idempresa = '".$idempresa."'", 'saasargentina');
    consulta_sql("UPDATE clientes SET estado = 1 WHERE idcliente = '".$idcliente."'", 'admin');

    $mensual = contar_sql(consulta_sql("SELECT idcliente FROM clientes WHERE idcliente = '$idcliente' AND idtipocliente = 1", 'admin'));

    if ($mensual) {
        $GLOBALS['modulo'] = 'ventas';
        $GLOBALS['idmodulo'] = 'idventa';
        // crear_factura($idcliente);
        $GLOBALS['modulo'] = 'configuraciones';
        $GLOBALS['idmodulo'] = 'idconfiguracion';
        $asunto = 'Pago, factura y reactivación de empresa: '.$idempresa;
        $texto_mail = 'Se recibió un pago, por lo que se realizó la nueva factura y se reactivó la empresa: <a href="'.URL_SAAS.'/saas.php?a=verempresa&id='.$idempresa.'">'.$idempresa.'</a><br>Revisar si es correcto el pago.';
    } else {
        $asunto = 'Empresa con facturación ANUAL reactivada: '.$idempresa;
        $texto_mail = 'Pago ANUAL y reactivación de empresa: <a href="'.URL_SAAS.'/saas.php?a=verempresa&id='.$idempresa.'">'.$idempresa.'</a><br>Revisar si es correcto y realizar la próxima facturación ANUAL.';
    }
    email_queue(MAIL_INFO, MAIL_INFO, $asunto, $texto_mail);
}

function obtener_debito_automatico() {
    $idcliente = campo_sql(consulta_sql(
        "SELECT empresas.idcliente
        FROM empresas
        WHERE empresas.idempresa = '".$_SESSION['empresa_idempresa']."'
        LIMIT 1", 'saasargentina'), 0);

    $datos = array_all_sql(consulta_sql(
        "SELECT idlistaxextra, texto FROM `datosxextras`
        WHERE idextraxmodulo IN (1, 2)
        AND idrelacion = $idcliente", 'admin'));

    $datos['debito_automatico'] = '';
    $datos['cbu'] = '';
    if (!empty($datos)) {
        $datos['debito_automatico'] = ($datos[0]['idlistaxextra'] == 1 ? 0 : $datos[0]['idlistaxextra']);
        $datos['cbu'] = $datos[1]['texto'];
    }

    $datos['id_debito_automatico'] = 1;
    $datos['id_cbu'] = 2;
    $datos['idcliente'] = $idcliente;

    return $datos;
}

function obtener_multimoneda() {
    return array(
        '000' => array('id' => '000', 'valor' => 'OTRAS MONEDAS'),
        'PES' => array('id' => 'PES', 'valor' => 'PESOS'),
        'DOL' => array('id' => 'DOL', 'valor' => 'Dólar ESTADOUNIDENSE'),
        '002' => array('id' => '002', 'valor' => 'Dólar EEUU LIBRE'),
        '003' => array('id' => '003', 'valor' => 'FRANCOS FRANCESES'),
        '004' => array('id' => '004', 'valor' => 'LIRAS ITALIANAS'),
        '005' => array('id' => '005', 'valor' => 'PESETAS'),
        '006' => array('id' => '006', 'valor' => 'MARCOS ALEMANES'),
        '007' => array('id' => '007', 'valor' => 'FLORINES HOLANDESES'),
        '008' => array('id' => '008', 'valor' => 'FRANCOS BELGAS'),
        '009' => array('id' => '009', 'valor' => 'FRANCOS SUIZOS'),
        '010' => array('id' => '010', 'valor' => 'PESOS MEJICANOS'),
        '011' => array('id' => '011', 'valor' => 'PESOS URUGUAYOS'),
        '012' => array('id' => '012', 'valor' => 'REAL'),
        '013' => array('id' => '013', 'valor' => 'ESCUDOS PORTUGUESES'),
        '014' => array('id' => '014', 'valor' => 'CORONAS DANESAS'),
        '015' => array('id' => '015', 'valor' => 'CORONAS NORUEGAS'),
        '016' => array('id' => '016', 'valor' => 'CORONAS SUECAS'),
        '017' => array('id' => '017', 'valor' => 'CHELINES AUTRIACOS'),
        '018' => array('id' => '018', 'valor' => 'Dólar CANADIENSE'),
        '019' => array('id' => '019', 'valor' => 'YENS'),
        '021' => array('id' => '021', 'valor' => 'LIBRA ESTERLINA'),
        '022' => array('id' => '022', 'valor' => 'MARCOS FINLANDESES'),
        '023' => array('id' => '023', 'valor' => 'BOLIVAR (VENEZOLANO)'),
        '024' => array('id' => '024', 'valor' => 'CORONA CHECA'),
        '025' => array('id' => '025', 'valor' => 'DINAR (YUGOSLAVO)'),
        '026' => array('id' => '026', 'valor' => 'Dólar AUSTRALIANO'),
        '027' => array('id' => '027', 'valor' => 'DRACMA (GRIEGO)'),
        '028' => array('id' => '028', 'valor' => 'FLORIN (ANTILLAS HOLANDESAS)'),
        '029' => array('id' => '029', 'valor' => 'GUARANI'),
        '030' => array('id' => '030', 'valor' => 'SHEKEL (ISRAEL)'),
        '031' => array('id' => '031', 'valor' => 'PESO BOLIVIANO'),
        '032' => array('id' => '032', 'valor' => 'PESO COLOMBIANO'),
        '033' => array('id' => '033', 'valor' => 'PESO CHILENO'),
        '034' => array('id' => '034', 'valor' => 'RAND (SUDAFRICANO)'),
        '035' => array('id' => '035', 'valor' => 'NUEVO SOL PERUANO'),
        '036' => array('id' => '036', 'valor' => 'SUCRE (ECUATORIANO)'),
        '040' => array('id' => '040', 'valor' => 'LEI RUMANOS'),
        '041' => array('id' => '041', 'valor' => 'DERECHOS ESPECIALES DE GIRO'),
        '042' => array('id' => '042', 'valor' => 'PESOS DOMINICANOS'),
        '043' => array('id' => '043', 'valor' => 'BALBOAS PANAMEÑAS'),
        '044' => array('id' => '044', 'valor' => 'CORDOBAS NICARAGÜENSES'),
        '045' => array('id' => '045', 'valor' => 'DIRHAM MARROQUÍES'),
        '046' => array('id' => '046', 'valor' => 'LIBRAS EGIPCIAS'),
        '047' => array('id' => '047', 'valor' => 'RIYALS SAUDITAS'),
        '048' => array('id' => '048', 'valor' => 'BRANCOS BELGAS FINANCIERAS'),
        '049' => array('id' => '049', 'valor' => 'GRAMOS DE ORO FINO'),
        '050' => array('id' => '050', 'valor' => 'LIBRAS IRLANDESAS'),
        '051' => array('id' => '051', 'valor' => 'Dólar DE HONG KONG'),
        '052' => array('id' => '052', 'valor' => 'Dólar DE SINGAPUR'),
        '053' => array('id' => '053', 'valor' => 'Dólar DE JAMAICA'),
        '054' => array('id' => '054', 'valor' => 'Dólar DE TAIWAN'),
        '055' => array('id' => '055', 'valor' => 'QUETZAL (GUATEMALTECOS)'),
        '056' => array('id' => '056', 'valor' => 'FORINT (HUNGRIA)'),
        '057' => array('id' => '057', 'valor' => 'BAHT (TAILANDIA)'),
        '058' => array('id' => '058', 'valor' => 'ECU'),
        '059' => array('id' => '059', 'valor' => 'DINAR KUWAITI'),
        '060' => array('id' => '060', 'valor' => 'EURO'),
        '061' => array('id' => '061', 'valor' => 'ZLTYS POLACOS'),
        '062' => array('id' => '062', 'valor' => 'RUPIAS HINDÚES'),
        '063' => array('id' => '063', 'valor' => 'LEMPIRAS HONDUREÑAS'),
        '064' => array('id' => '064', 'valor' => 'YUAN (Rep. Pop. China)'),
    );
}

function insertarCategoriaVenta($idcomportamiento, $discrimina) {
    global $bd_link;

    consulta_sql("INSERT INTO categorias_ventas SET
            idcomportamiento = '$idcomportamiento',
            nombre = 'Pedido desde Tienda online',
            letra = 'P',
            puntodeventa = '1',
            ultimonumero = '0',
            muevestock = '',
            muevesaldo = '1',
            operacioninversa = '',
            tienesituacion = '1',
            situacion = 'pendiente',
            auto_aprobar = '1',
            tipofacturacion = 'interno',
            tipoimpresion = 'predeterminado',
            discrimina = '$discrimina'
        ");
    return id_sql($bd_link);
}
