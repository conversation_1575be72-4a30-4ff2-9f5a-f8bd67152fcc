<?php

/* Esta funcion es propia de clientes por eso debe estar acá */
function verficar_email($mail)
{
    global $i18n;
    global $id;
    return verificar_mail_cliente($mail, $i18n[181], $i18n[182], $id);
}

function comprobantes_habilitados_clientes($idcliente, $pago_a_cuenta = "", $devolucion_a_cuenta = "")
{
    $opciones = array();
    if (($pago_a_cuenta != "") && !$_SESSION['sistema_gratis'])
        $opciones[] = array('a' => $pago_a_cuenta, 'permiso' => 'ventaspagos_alta');
    if (($devolucion_a_cuenta != "") && !$_SESSION['sistema_gratis'])
        $opciones[] = array('a' => $devolucion_a_cuenta, 'permiso' => 'ventaspagos_alta');
    $resultado_sql = comprobantes_obtener_habilitados($idcliente);
    while ($tipoventa = array_sql($resultado_sql)) {
        $opciones[] = array('a' => $tipoventa['nombre'], 'permiso' => 'ventas_alta');
    }

    return $opciones;
}

function comprobantes_habilitados_validar($idcliente, $idtipoventa = 0)
{
    $resultado_sql = comprobantes_obtener_habilitados($idcliente,$idtipoventa);
    return contar_sql($resultado_sql);
}

function comprobantes_habilitados_condicion($condicion_cliente, $excluir = false) // $condicion_cliente puede ser 0 para CF, 1 para RI o 2 para Monotributos
{
    $opciones = array();
    $resultado_sql = comprobantes_obtener_habilitados(false, false, $condicion_cliente, $excluir);
    while ($tipoventa = array_sql($resultado_sql)) {
        $opciones[] = array('a' => $tipoventa['nombre'], 'permiso' => 'ventas_alta');
    }

    return $opciones;
}

function comprobantes_obtener_habilitados($idcliente, $idtipoventa = false, $condicion_cliente = false, $excluir = false)
{
    $condicion_empresa = ($_SESSION['configuracion_idtipoiva'] == 1 ? true : false);

    if ($idcliente) {
        $condicion_cliente = campo_sql(consulta_sql(
            "SELECT idtipoiva FROM clientes WHERE idcliente = '$idcliente'"), 0);

    } else if ($condicion_cliente != 1)
        $condicion_cliente = 0; // Cualquiera que no sea Responsable Inscripto

    if ($condicion_empresa && $condicion_cliente == 1)
        // Ambos RI
        $discrimina =("('A','R')");
    elseif ($condicion_empresa && in_array($condicion_cliente, [2, 4, 5]))
        // Empresa RI y cliente Monotributo
        $discrimina =("('A','R')");
    elseif ($condicion_empresa)
        // Empresa RI y cliente Consumidor Final o Exento
        $discrimina =("('B','R')");
    else
        // Ninguno RI
        $discrimina =("('C','R')");

    if ($excluir == 'interno') {
        $sql_discrimina = " tipofacturacion != 'interno' AND discrimina IN $discrimina ";
    } else {
        $sql_discrimina = " (tipofacturacion = 'interno' OR discrimina IN $discrimina) ";
    }

    return consulta_sql(
        "SELECT nombre
        FROM categorias_ventas
        WHERE estado = '1'
            AND $sql_discrimina"
            .($idtipoventa
                ? "AND idtipoventa = '$idtipoventa'"
                : "")
        ." ORDER BY nombre");
}

function obtener_datos_cliente($idcliente)
{
    return array_sql(consulta_sql(
        "SELECT clientes.idcliente, clientes.nombre AS cliente, clientes.idtipocliente,
            clientes.idlocalidad, clientes.idtipoiva, clientes.razonsocial, clientes.cuit, clientes.dni,
            clientes.domicilio, categorias_localidades.idlocalidad, categorias_localidades.nombre AS localidad, tablas_condiciones.nombre AS iva
        FROM clientes
            LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad
            LEFT JOIN tablas_condiciones ON clientes.idtipoiva = tablas_condiciones.idtipoiva
        WHERE idcliente = '$idcliente'"));
}

function obtener_datos_venta($idventa)
{
    return array_sql(consulta_sql(
        "SELECT ventas.idventa, ventas.idcliente, ventas.idrelacion, ventas.tiporelacion, ventas.operacioninversa, ventas.total, ventas.muevesaldo,
            ventas.estado, ventas.idtipoventa, categorias_ventas.discrimina, ventaspagos.idventapago
        FROM ventas
            INNER JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
            LEFT JOIN ventaspagos ON ventas.idventa = ventaspagos.idventa
        WHERE ventas.idventa = '$idventa'"));
}

function obtener_datos_ventas_desde_servicios($idservicio)
{
    return array_all_sql(consulta_sql(
        "SELECT ventas.idventa, ventas.idcliente, ventas.idrelacion, ventas.tiporelacion, ventas.operacioninversa, ventas.total,
            ventas.estado, ventas.idtipoventa, categorias_ventas.discrimina, ventaspagos.idventapago
        FROM ventas
            INNER JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa
            LEFT JOIN ventaspagos ON ventas.idventa = ventaspagos.idventa
        WHERE ventas.idrelacion = '$idservicio'
        AND ventas.tiporelacion = 'servicio'"));
}

function cambiar_cliente_en_ventas($idventa, $cliente, $venta) {

    if (!$idventa || !$venta['idventa']) {
        mostrar_error("No se ha especificado la venta a la que se le quiere cambiar el cliente.");
        return false;
    }

    if ($venta['estado'] == 'cerrado') {
        consulta_sql("UPDATE ventas SET
            idcliente = '".$cliente['idcliente']."'
            WHERE idventa = '".$idventa."'");
    } else {
        consulta_sql("UPDATE ventas SET
            idcliente = '".$cliente['idcliente']."',
            idtipoiva = '".$cliente['idtipoiva']."',
            dni = '".$cliente['dni']."',
            cuit = '".$cliente['cuit']."',
            tipodoc = '".$cliente['tipodoc']."',
            razonsocial = '".escape_sql($cliente['razonsocial'])."',
            domicilio = '".escape_sql($cliente['domicilio'])."',
            idlocalidad = '".(!$cliente['idlocalidad'] ? campo_sql(consulta_sql("SELECT idlocalidad FROM configuraciones LIMIT 1")) : $cliente['idlocalidad'])."'
            WHERE idventa = '".$idventa."'
        ");
    }

    $idmoneda_venta = idmoneda('ventas', $idventa);
    $idmoneda_cliente_origen = idmoneda('clientes', $venta['idcliente']);
    $idmoneda_cliente_destino = idmoneda('clientes', $cliente['idcliente']);
    $total = cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen,
        $venta['operacioninversa'] ? -$venta['total'] : $venta['total']);

    consulta_sql("UPDATE ventasxclientes SET
            idcliente = '".$cliente['idcliente']."',
            total = '".$total."'
        WHERE id = '".$idventa."'
            AND idtipoventa > 0");

    $resultado_sql = consulta_sql("SELECT idventaxcliente, total FROM ventasxclientes WHERE id IN (SELECT idventapago FROM ventaspagos WHERE idventa = '$idventa') AND idtipoventa <= 0");
    while ($ventasxclientes = array_sql($resultado_sql)) {
        consulta_sql("UPDATE ventasxclientes SET
                        idcliente = '".$cliente['idcliente']."',
                        total = '".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $ventasxclientes['total'])."'
                    WHERE idventaxcliente = '".$ventasxclientes['idventaxcliente']."'");
    }

    consulta_sql("UPDATE ventaspagos SET
            idcliente = '".$cliente['idcliente']."'
        WHERE idventa = '".$idventa."'");

    if ($venta['estado'] == 'cerrado' && $venta['muevesaldo']) {
        $diferencia = obtener_saldo('ventas', $idventa);
        actualizar_saldo('clientes', $venta['idcliente'], cotizacion($idmoneda_cliente_origen, $idmoneda_venta, - $diferencia));
        actualizar_saldo('clientes', $cliente['idcliente'], cotizacion($idmoneda_cliente_destino, $idmoneda_venta, $diferencia));
    }
}

function cambiar_cliente_en_servicios($idservicio, $idcliente)
{
    consulta_sql("UPDATE servicios SET
            idcliente = '".$idcliente."'
            WHERE idservicio = '".$idservicio."'");
}