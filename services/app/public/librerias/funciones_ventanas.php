<?php
function marco_inicio()
{
    global $modulo;
    global $id;
    global $ventana;
    global $a;

// TODO: Todo lo de este echo tiene que terminar en un JS
    echo '
<script>
function actualizar_marco_'.$ventana.'(boton) {
    bloquear();
    if (boton) {
        var ser=$("#marco_'.$ventana.' form").serialize();
        $("#marco_'.$ventana.'").load("cargadores/actualizar.php",{t: window.t, modulo: "'.$modulo.'", ventana: "'.$ventana.'", id: "'.$id.'", boton: boton.val(), id_boton: boton.attr("id"), serialize: ser}, function() {
            desbloquear();
        });
    } else {
        $("#marco_'.$ventana.'").load("cargadores/actualizar.php",{t: window.t, modulo: "'.$modulo.'", ventana: "'.$ventana.'", id: "'.$id.'"}, function() {
            desbloquear();
        });
    }
}

$(function() {
    $("#marco_'.$ventana.' input[name='."'actualizar'".']").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
        window.confirmarsalida = false;
        e.preventDefault();
        e.stopPropagation();
        if (!(window.validacion_'.$ventana.') || validacion_'.$ventana.'($(this).val())) {
            actualizar_marco_'.$ventana.'($(this));
        }
    });';

    if ($modulo != 'informes' && preg_match("/mod|alta/i", $a))
        echo '
    $("input, select, textarea, checkbox").change(function() {
        window.confirmarsalida = true;
    });
    window.desbloqueable = true;
    $(window).bind("beforeunload", window.onbeforeunload());
';

    echo '
});
</script>

<div class="marco" id="marco_'.$ventana.'">
';
}

function marco_fin()
{
    echo '
</div>
';
}

function modal_inicio($sinModal = false)
{
    global $modulo;
    global $id;
    global $ventana;
if ($sinModal) {
    echo '<div class="" id="'.$ventana.'">';
} else {

    echo '
    <div class="modal" id="modal_'.$ventana.'">';
}
echo '
<script>
    $("#modal_'.$ventana.' input[name='."'actualizar'".']").click(function(e) {
        e.preventDefault();
        e.stopPropagation();
        if (!(window.validacion_'.$ventana.') || validacion_'.$ventana.'($(this).val())) {
            var ser=$("#modal_'.$ventana.' form").serialize();
            $("#modal_'.$ventana.'").load("cargadores/modal.php",{modulo: "'.$modulo.'", ventana: "'.$ventana.'", id: "'.$id.'", boton: $(this).val(), id_boton: $(this).attr("id"), serialize: ser});
        }
    });
</script>
';
}

function modal_fin()
{
    echo '
</div>
';
}

function modal_cerrar($script = '')
{
    echo '
<script>
    '.$script.'
    $(".modal").remove();
    desbloquear();
</script>';
}

function ventana_inicio($nombre, $ancho = '100', array $botones = array(), $opciones = '', $imagen = false)
{
    global $i18n_funciones;
    global $modulo;
    global $a;
    global $ventana;
    global $id;

    if (!in_array($ancho, array('33', '50', '66', '100')))
        $ancho = 'auto';
    else {
        echo '
<script>
    $(function() {
        $(".ventana_'.$ancho.' .linea_contenido_0").addClass("linea_contenido_0_'.$ancho.'");
        $(".ventana_'.$ancho.' .linea_contenido_1").addClass("linea_contenido_1_'.$ancho.'");
        $(".ventana_'.$ancho.' .linea_contenido_2").addClass("linea_contenido_2_'.$ancho.'");
        $(".ventana_'.$ancho.' .linea_contenido_3").addClass("linea_contenido_3_'.$ancho.'");
        $(".ventana_'.$ancho.' .linea_contenido_4").addClass("linea_contenido_4_'.$ancho.'");
        $(".ventana_'.$ancho.' .linea_contenido_5").addClass("linea_contenido_5_'.$ancho.'");
        $(".ventana_'.$ancho.' .linea_contenido_6").addClass("linea_contenido_6_'.$ancho.'");
    });
</script>';
    }
    echo '
<div class="ventana_'.$ancho.'" '.$opciones.'>
    <form id="form_'. str_replace(" ","_",$nombre).'" name="form_'.str_replace(" ","_",$nombre).'" accept-charset="utf-8" action="'.$_SERVER['REQUEST_URI'].'" method="post" enctype="multipart/form-data" class="form_'.$modulo.'">
    <div class="ventana_inicio">
    <span class="ventana_titulo">';
    if ($imagen) {
        echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_'.$imagen.'.png" class="ventana_iconito icono-'.$modulo.'"/>';
    } elseif (file_exists('estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$modulo.'.png') ||
        // El segundo file_exists es por si se está actualizando la ventana que está dentro de la carpeta cargadores, se puede sacar cuando hagamos MVC
        file_exists('../estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$modulo.'.png')) {

        echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_'.$modulo.'.png" class="ventana_iconito icono-'.$modulo.'"/>';
    }
    echo $nombre.'</span>';
    //if ((!$_SESSION['mobile']) || (!empty($botones) && $_SESSION['mobile'])) {
        echo '<div class="ventana_botones">';
        foreach ($botones as $boton) {
            switch ($boton['tipo']) {
            default:
            case 'enlace':
                if (!$boton['url']) {
                    echo '
                <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" />';
                } elseif (!isset($boton['permiso']) || $_SESSION['perfil_'.$boton['permiso']]) {
                    echo '
                <a href="'.$boton['url'].'" '.$boton['opciones'].'><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" /></a>';
                } else {
                    echo '
                <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'_no.png" title="'.$i18n_funciones[15].strtolower($boton['title']).'" />';
                }
                break;

            case 'desplegable':
                $aleatorio = rand();
                if (preg_match('/php/i', $boton['url'])) {
                    $temp_array = explode('.php?', $boton['url']);
                    $boton['url'] = $temp_array[0];
                    $get = $temp_array[1];
                }
                echo '
                    <script>
                    $(function() {
                        //Desplegables ventas relacionadas
                        $("#'.$aleatorio.'").position(
                            {
                            my: "right top",
                            at: "right bottom",
                            of: "#'.$aleatorio.'_boton"
                        });
                        $("#'.$aleatorio.'_boton").bind("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            $("#'.$aleatorio.'").fadeIn(500);
                        });
                        $("#'.$aleatorio.' a").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                            //e.preventDefault();
                            //e.stopPropagation();
                            bloquear();
                            var ser=$("form").serialize();
                            $(this).parents(".marco").load("cargadores/actualizar.php",{t: window.t, modulo: "'.$modulo.'", ventana: "'.$boton['url'].'", id: "'.$id.'", boton: $(this).text(), serialize: ser'.(($get) ? ', get: "'.$get.'"' : '').'}, function() {
                                desbloquear();
                            });
                        });
                        //$("#'.$aleatorio.'").mouseleave(function() {
                            //$("#'.$aleatorio.'").fadeOut(500);
                        //});
                    });
                    </script>
                    <a id="'.$aleatorio.'_boton" href="#" onclick="event.preventDefault()"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].'/></a>
                    <div id="'.$aleatorio.'" class="opciones_flotantes" style="display: none;">';
                if (!count($boton['desplegable']))
                    echo '<span class="enlace">'.($boton['desplegable_vacio'] ? $boton['desplegable_vacio'] : $i18n_funciones[106]).'</span><br>';
                foreach ($boton['desplegable'] as $desplegable) {
                    if (!isset($desplegable['permiso']) || $_SESSION['perfil_'.$desplegable['permiso']]) {
                        echo '<a class="enlace" href="#" onclick="event.preventDefault()">'.$desplegable['a'].'</a><br />';
                    } else {
                        echo '<span class="enlace" title="'.$i18n_funciones[15].$desplegable['a'].'">'.$desplegable['a'].'</span><br>';
                    }
                }
                echo '
                    </div>';
                break;

            case 'modal':
                if (!isset($boton['permiso']) || $_SESSION['perfil_'.$boton['permiso']]) {
                    if ($boton['modulo']) {
                        $temp_modulo = $boton['modulo'];
                    } else {
                        $temp_modulo = $modulo;
                    }

                    $aleatorio = rand();
                    echo '
                    <script>
                    $(function() {
                        $("#'.$aleatorio.'").click(function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            marco_modal();
                            $.ajax(
                            {
                                url: "cargadores/modal.php",
                                type: "post",
                                data: (
                                {
                                    modulo: "'.$temp_modulo.'",
                                    id: "'.$id.'",
                                    ventana: "'.$boton['url'].'"
                                }),
                                success: function(data) {
                                    $("#marco_flotante").html(data);
                                }
                            });
                        });
                    });
                    </script>
                    <a id="'.$aleatorio.'" href="javascript:void(0)" '.$boton['opciones'].'><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" /></a>';
                } else {
                    echo '
                <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'_no.png" title="'.$i18n_funciones[15].strtolower($boton['title']).'" />';
                }
                break;

            case 'exportar':
                $aleatorio = rand();
                // PARCHE: hasta que ventaspagos y compraspagos tengan sus propios módulos
                if ($modulo == 'ventas' && ($a == 'verpago' || $a == 'verpagoindividual'))
                    $temp_modulo = 'ventaspagos';
                elseif ($modulo == 'compras' && ($a == 'verpago' || $a == 'verpagoindividual'))
                    $temp_modulo = 'compraspagos';
                elseif ($modulo == 'productos' && in_array($a, ['traslados', 'traslados_ver', 'traslados_mod']))
                    $temp_modulo = 'traslados';
                else
                    $temp_modulo = $modulo;

                if ($modulo == 'ventas') {
                    //Para habilitar mensajería instantánea
                    $ventas = array_sql(consulta_sql("SELECT ML_order_id, mail, ML_estado
                        FROM ventas
                        LEFT JOIN clientes ON ventas.idcliente = clientes.idcliente
                        LEFT JOIN tienda ON ML_estado = 1
                        WHERE idventa = '".$id."'
                        LIMIT 1
                        "));
                }

                $fecha_mail = campo_sql(consulta_sql("SELECT fecha FROM mails_enviados WHERE idrelacion = '".$id."' AND tiporelacion = '".$temp_modulo."'"));

                if ($a == 'verpago' && ($temp_modulo == 'ventaspagos' || $temp_modulo == 'compraspagos')) {
                    $idnumeroventapago = campo_sql(consulta_sql("SELECT ".($temp_modulo == 'ventaspagos' ? 'idnumeroventapago' : 'idnumerocomprapago')."  FROM ".$temp_modulo." WHERE ".($temp_modulo == 'ventaspagos' ? 'idventapago' : 'idcomprapago')." = '".$id."' LIMIT 1"));
                }

                if ($fecha_mail) date("d-m-Y H:i:s", strtotime($fecha_mail));

                echo '
                    <script>
                    $(function() {
                        $("#'.$aleatorio.'").position(
                            {
                            my: "right top",
                            at: "right bottom",
                            of: "#'.$aleatorio.'_boton"
                        });
                        $("#'.$aleatorio.'_boton").bind("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            (detectMobile() ? $("#bloquea").fadeIn(500) : "" );
                            $("#'.$aleatorio.'").fadeIn(500);
                        });
                        //$("#'.$aleatorio.'").mouseleave(function() {
                            //$("#bloquea").fadeOut(500);
                            //$("#'.$aleatorio.'").fadeOut(500);
                        //});
                        $("#bloquea").bind("click", function(e) {
                            $("#bloquea").fadeOut(500);
                        });
                        $("#'.$aleatorio.' a").bind("click", function(e) {
                            //e.preventDefault();
                            $("#bloquea").fadeOut(500);
                            $("#'.$aleatorio.'").fadeOut(500);
                        });
                        $("#'.$aleatorio.' #mail").live("click", function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            marco_modal();
                            $.ajax(
                            {
                                url: "cargadores/modal.php",
                                type: "post",
                                data: (
                                {
                                    modulo: "'.$temp_modulo.'",
                                    ventana: "mail",
                                    id: "'.($a == 'verpago' && ($temp_modulo == 'ventaspagos' || $temp_modulo == 'compraspagos') ? $idnumeroventapago : $id).'"
                                }),
                                success: function(data) {
                                    $("#marco_flotante").html(data);
                                }
                            });
                        });
                        $("#'.$aleatorio.' #mensajeria_ml").live("click", function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            marco_modal();
                            $.ajax(
                            {
                                url: "cargadores/modal.php",
                                type: "post",
                                data: (
                                {
                                    modulo: "ventas",
                                    ventana: "mensajeria_ml",
                                    id: "'.($a == 'verpago' ? $idnumeroventapago : $id).'"
                                }),
                                success: function(data) {
                                    $("#marco_flotante").html(data);
                                }
                            });
                        });
                    });
                    </script>
                    <a id="'.$aleatorio.'_boton" href="#" onclick="event.preventDefault();"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].'/></a>
                    <div id="'.$aleatorio.'" class="opciones_flotantes" style="display: none;">';
                    $pagos = '';
                    if ($a == 'verpago') {
                        $pagos = '&pagos=multiples';
                    }

                    if ($opciones['tipo_formapago'] == 'retencion')
                        $temp_modulo = 'compraspagos_retenciones';

                    echo '
                        <a href="exportar.php?modulo='.$temp_modulo.'&a=prn&id='.$id.$pagos.'" target="_blank"><img title="'.$i18n_funciones[40].'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/imprimir.png" />'.($_SESSION['mobile'] ? $i18n_funciones[40] : '').'</a><br>';
                    if ($modulo == 'ventas' && $ventas['ML_estado'] && !$ventas['mail'] && $ventas['ML_order_id']) {
                        echo '
                            <a href="#" onclick="event.preventDefault();" id="mensajeria_ml"><img title="'.$i18n_funciones[197].($fecha_mail ? " (".$i18n_funciones[216].$fecha_mail.")" : "").'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.($fecha_mail ? 'mail-enviado.png' : 'ml-mail.png').'" />'.($_SESSION['mobile'] ? $i18n_funciones[197] : '').'</a><br>';
                    } else {
                        echo '
                            <a href="#" onclick="event.preventDefault();" id="mail"><img title="'.$i18n_funciones[41].($fecha_mail ? " (".$i18n_funciones[216].$fecha_mail.")" : "").'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.($fecha_mail ? 'mail-enviado.png' : 'mail.png').'" />'.($_SESSION['mobile'] ? $i18n_funciones[41] : '').'</a><br>';
                    }
                    echo '
                        <a href="exportar.php?modulo='.$temp_modulo.'&a=html&id='.$id.$pagos.'"><img title="'.$i18n_funciones[42].'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/html.png" />'.($_SESSION['mobile'] ? $i18n_funciones[42] : '').'</a><br>
                        <a href="exportar.php?modulo='.$temp_modulo.'&a=pdf&id='.$id.$pagos.'"><img title="'.$i18n_funciones[43].'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/pdf.png" />'.($_SESSION['mobile'] ? $i18n_funciones[43] : '').'</a><br>
                        <a href="exportar.php?modulo='.$temp_modulo.'&a=planilla&id='.$id.$pagos.'"><img title="'.$i18n_funciones[44].'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/planilla.png" />'.($_SESSION['mobile'] ? $i18n_funciones[44] : '').'</a><br>
                        '.($_SESSION['mobile'] ? '<hr><button onclick="event.preventDefault(); cerrar_modal();" class="boton">'.$i18n_funciones['cerrar'].'</button>' : '').'
                    </div>';
                break;
            }
        }
    /*} else {
        echo '<div class="ventana_botones_mobile">';
    }*/

    if (file_exists('sistemas/sistema_'.$_SESSION['sistema_idsistema'].'_idioma_'.$_SESSION['usuario_ididioma'].'/ayudas/ayudas_'.$ventana.'.php') ||
        // El segundo file_exists es por si se está actualizando la ventana que está dentro de la carpeta cargadores, se puede sacar cuando hagamos MVC
        file_exists('../sistemas/sistema_'.$_SESSION['sistema_idsistema'].'_idioma_'.$_SESSION['usuario_ididioma'].'/ayudas/ayudas_'.$ventana.'.php')) {
        $aleatorio = rand();
        echo '
            <script>
            $(function() {
                $("#'.$aleatorio.'").bind("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                    e.preventDefault();
                    marco_flotante();
                    $("#marco_flotante").load("cargadores/flotante.php", {modulo: "ayudas", ventana: "ayudas_ver", id: "'.$ventana.'"});
                });
            });
            </script>
            <a href="#" id="'.$aleatorio.'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/ayuda.png" title="Ayuda de esta ventana" style="cursor: pointer;"/></a>';
    }
    echo '
        </div>
    </div>
    <div class="ventana_contenido">
    <div id="ayuda_flotante"></div>';
}

function ventana_informe($nombre)
{
    global $i18n_funciones;
    global $modulo;
    global $a;
    global $id;

    $ancho = '100'; // Todos los informes tienen el ancho total
    echo '
<script>
    $(function() {
        $(".ventana_'.$ancho.' .linea_contenido_0").addClass("linea_contenido_0_'.$ancho.'");
    });
</script>';
    $url_informes = $_SESSION['servidor_version'] == 'BETA'
        ? URL_INFORMES_BETA
        : ($_SESSION['servidor_version'] == 'ALFA' ? URL_INFORMES_ALFA : URL_INFORMES);
    echo '
<div class="ventana_'.$ancho.'">
    <form id="form_'.$id.'" name="form_'.$id.'" accept-charset="utf-8"
        action="'.$url_informes.'/informes/mod/'.$id.'"
        method="get" enctype="multipart/form-data" target="_blank">
        <input type="hidden" name="id" value="'.$id.'">
        <input type="hidden" name="a" value="mod">
        <input type="hidden" name="descarga_informe" value="">
    <div class="ventana_inicio">
    <span class="ventana_titulo">';
    if (file_exists('estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_'.$modulo.'.png')) {
        echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/icono_'.$modulo.'.png" class="ventana_iconito icono-'.$modulo.'"/>';
    }
    echo $nombre.'</span>
        <div class="ventana_botones">';
    echo '
        </div>
    </div>
    <div class="ventana_contenido">
    <div id="ayuda_flotante"></div>';
}

function ventana_fin()
{
    echo '
    </div>
    <div class="ventana_fin"></div>
    </form>
</div>';
}

function contenido_inicio($titulo = false, $ancho = '100', $desplegable = false, $desplegado = false, $ayuda_puntual = false, $opciones = '', $imagen = false)
{

    if (!in_array($ancho, array('33', '50', '66', '100')))
        $ancho = 'auto';
    if ($desplegable) $ancho .= " desplegable_ver"; //Nueva clase desplegable_ver

    if (is_array($opciones)) {
        $class = $opciones['class'];
        $opciones = 'id="'.$opciones['id'].'" style="'.$opciones['style'].'" '.$opciones['opciones'];
    } else {
        $class = '';
    }

    $aleatorio = rand();
    if ($titulo && $desplegable) {
        echo '
    <script>
    $(function() {';
        if ($desplegado) {
            echo '
        var flag_'.$aleatorio.' = 1;';
        } else {
            echo '
        var flag_'.$aleatorio.' = 0;
        $("#'.$aleatorio.'").hide();';
        }
        echo '
        $("#boton_'.$aleatorio.'").bind("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (flag_'.$aleatorio.' == 0) {
                $("#'.$aleatorio.'").slideDown(400);
                flag_'.$aleatorio.' = 1;
                $("#boton_'.$aleatorio.'").html('."'".'<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/vermenos.png" style="float: left;" />'."'".');
                $("#'.$aleatorio.' textarea").redactor({
                    lang: "es",
                    autoresize: false,
                    minHeight: 100,
                    plugins: ["fullscreen"]
                });
            } else {
                $("#'.$aleatorio.'").slideUp(400);
                flag_'.$aleatorio.' = 0;
                $("#boton_'.$aleatorio.'").html('."'".'<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/vermas.png" style="float: left;" />'."'".');
            }
        });
    });
    </script>

        <div class="contenido_'.$ancho.' '.$class.'" '.$opciones.'>';
        if ($desplegado) {
            echo '
            <a href="#" id="boton_'.$aleatorio.'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/vermenos.png" style="float: left;" /></a><span class="contenido_titulo">'.$titulo.'</span>';
            ayuda_puntual($ayuda_puntual);
            echo '
            <div id="'.$aleatorio.'">';
        } else {
            echo '
            <a href="#" id="boton_'.$aleatorio.'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/vermas.png" style="float: left;"/></a><span class="contenido_titulo">'.$titulo.'</span>';
            ayuda_puntual($ayuda_puntual);
            echo '
            <div id="'.$aleatorio.'">';
        }
    } elseif ($titulo) {
        echo '
        <div class="contenido_'.$ancho.' '.$class.'" '.$opciones.'>';
        if ($imagen) {
            echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$imagen.'.png" class="titulo_iconito"/>';
        }
        echo'
            <span class="contenido_titulo">'.$titulo.'</span>';
        ayuda_puntual($ayuda_puntual);
        echo '
            <div id="'.$aleatorio.'">';
    } else {
        echo '
        <div class="contenido_'.$ancho.' '.$class.'" '.$opciones.'>
            <div id="'.$aleatorio.'">';
    }
}

function contenido_fin()
{
    echo '
            </div>
        </div>';
}

function linea_inicio($tipo = 'fila', $cantidad_botones = 0, $url = false, $opciones = '')
{
    global $modulo;
    global $id;

    if (is_array($opciones)) {
        $class = $opciones['class'];
        if (isset($opciones['id']))
            $opciones = 'id="'.$opciones['id'].'"';
        if (isset($opciones['id']))
            $style = ' '.$opciones['id'];
        $opciones.= ' '.$opciones['opciones'];
    } else {
        $class = '';
        $style = '';
    }

    $aleatorio = rand();
    if (!in_array($tipo, array('fila', 'titulo', 'subtotal', 'total', 'division', 'flotante', 'modal')))
        $tipo = 'fila';
    if (($tipo == 'flotante' || $tipo == 'modal') && $url) {
        $temp_array = explode('.php', $url);
        $ventana = $temp_array[0];
        $temp_array = explode('id=', $url);
        echo '
        <script>
        $(function() {
            $("#'.$aleatorio.'").live("click", function(e) {
                e.preventDefault();
                e.stopPropagation();
                marco_'.$tipo.'();
                $.ajax(
                {
                    url: "cargadores/'.$tipo.'.php",
                    type: "post",
                    data: (
                    {
                        modulo: "'.$modulo.'",
                        ventana: "'.$ventana.'",
                        id: "'.$temp_array[1].'"
                        '.($modulo == 'saas' ? ',idticket: "'.$id.'"' : '').'
                    }),
                    success: function(data) {
                        $("#marco_flotante").html(data);
                    }
                });
            });
        });
        </script>';
        $tipo = 'fila';
    } elseif ($url) {
        echo '
        <script>
        $(function() {
            $("#'.$aleatorio.' div:not(.celda_imagen)").live("click", function() {
                document.location="'.$url.'";
            });
        });
        </script>';
    }
    if ($tipo == 'fila') {
        static $paroimpar = 'par';
        if ($paroimpar == 'impar')
            $paroimpar = 'par';
        else
            $paroimpar = 'impar';
        $tipo = $paroimpar;
    }
    echo '
            <div class="linea_'.$tipo.' '.$class.'" '.$opciones.(($url) ? ' style="cursor: pointer; '.$style.'"' : '').'>
                <div id="'.$aleatorio.'" class="linea_contenido linea_contenido_'.$cantidad_botones.'">';
}

function linea_fin(array $botones = array(), $opciones = '', $linea_completa = array())
{
    global $modulo;
    global $i18n_funciones;
    global $id;

    $aleatorio = rand();
    $cantidad_botones = count($botones);
    if ($cantidad_botones == 1 && $botones[0]['tipo'] == 'submit') {
        $cantidad_botones = 3;
    }
    if ($linea_completa['tipo'] == 'enlace') {
        echo '<div class="enlace linea_completa"> <a style="" href="'.$linea_completa['url'].'">'.$linea_completa['texto'].'</a></div>';
    }
    echo '
                </div>
                <div class="linea_botones linea_botones_'.$cantidad_botones.'" id="'.$aleatorio.'" '.$opciones.'>';
    foreach ($botones as $boton) {
        switch ($boton['tipo']) {
        default:
            if (!$boton['a'])
                break;
        case 'imagen':
            if (!$boton['url']) {
                echo '
                    <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" />';
            } elseif (!isset($boton['permiso']) || $_SESSION['perfil_'.$boton['permiso']]) {
                echo '
                    <a href="'.$boton['url'].'" '.$boton['opciones'].'><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" /></a>';
            } else {
                echo '
                    <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'_no.png" title="'.$i18n_funciones[15].strtolower($boton['title']).'" />';
            }
            break;

        case 'submit':
            if (!isset($boton['permiso']) || $_SESSION['perfil_'.$boton['permiso']]) {
                if ($boton['a'])
                    echo('
                    <input type="image" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" alt="'.$boton['title'].'" '.$boton['opciones'].' />');
            } else {
                //echo '
                //  <input type="submit" name="actualizar" class="entrada_boton" value="'.$boton['value'].'" title="'.$i18n_funciones[15].strtolower($boton['value']).'" '.$boton['opciones'].' />';
            }
            break;

        case 'flotante':
        case 'modal':
            $aleatorio = rand();
            $temp_array = explode('.php', $boton['url']);
            $ventana = $temp_array[0];
            $temp_array = explode('id=', $boton['url']);
            echo '
                <script>
                $(function() {
                    $("#'.$aleatorio.'").click(function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        marco_'.$boton['tipo'].'();
                        $.ajax(
                        {
                            url: "cargadores/'.$boton['tipo'].'.php",
                            type: "post",
                            data: (
                            {
                                modulo: "'.$modulo.'",
                                ventana: "'.$ventana.'",
                                id: "'.$temp_array[1].'"
                            }),
                            success: function(data) {
                                $("#marco_flotante").html(data);
                            }
                        });
                    });
                });
                </script>
                <a id="'.$aleatorio.'" href="javascript:void(0)"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].' /></a>';
            break;

        case 'desplegable':
            $aleatorio = rand();
            if (preg_match('/php/i', $boton['url'])) {
                $temp_array = explode('.php?', $boton['url']);
                $boton['url'] = $temp_array[0];
                $get = $temp_array[1];
            }
            echo '
                <script>
                //Desplegables ventas relacionadas
                $(function() {
                    $("#'.$aleatorio.'").position(
                        {
                        my: "right top",
                        at: "right bottom",
                        of: "#'.$aleatorio.'_boton"
                    });
                    $("#'.$aleatorio.'_boton").live("click", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        (detectMobile() ? $("#bloquea").fadeIn(500) : "" );
                        $("#'.$aleatorio.'").fadeIn(500);
                    });
                    $("#'.$aleatorio.' a").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        bloquear();
                        var ser=$("form").serialize();
                        $(this).parents(".marco").load("cargadores/actualizar.php",{t: window.t, modulo: "'.$modulo.'", ventana: "'.$boton['url'].'", id: "'.$id.'", boton: $(this).html(), serialize: ser'.(($get) ? ', get: "'.$get.'"' : '').'}, function() {
                            desbloquear();
                        });
                    });
                    $("#'.$aleatorio.'_boton").bind("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                        if ($(".opciones_flotantes:visible").length > 0 && e.target.nodeName == "IMG") {
                            $(".opciones_flotantes").fadeOut();
                        }
                        e.preventDefault();
                        e.stopPropagation();
                        (detectMobile() ? $("#bloquea").fadeIn(500) : "" );
                        $("#'.$aleatorio.'").fadeIn(500);
                    });
                    //$("#'.$aleatorio.'").mouseleave(function() {
                        //$("#'.$aleatorio.'").fadeOut(500);
                    //});
                });
                </script>
                <a id="'.$aleatorio.'_boton" href="#"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].'/></a>
                <div id="'.$aleatorio.'" class="opciones_flotantes" style="display: none;">';
            if (!count($boton['desplegable']))
                echo '<span class="enlace">'.($boton['desplegable_vacio'] ? $boton['desplegable_vacio'] : $i18n_funciones[106]).'</span><br>';
            foreach ($boton['desplegable'] as $desplegable) {
                if (!isset($desplegable['permiso']) || $_SESSION['perfil_'.$desplegable['permiso']]) {
                    echo '<a class="enlace" href="javascript:void(0);" '.$desplegable['opciones'].'>'.$desplegable['a'].'</a><br>';
                } else {
                    echo '<span class="enlace" title="'.$i18n_funciones[15].$desplegable['a'].'">'.$desplegable['a'].'</span><br>';
                }
            }
            echo ($_SESSION['mobile'] ? '<hr><button onclick="event.preventDefault(); cerrar_modal();" class="boton">'.$i18n_funciones['cerrar'].'</button>' : '').'
                </div>';
            break;

        case 'ajax':
            if ($boton['a'] == 'actualizar') {
                $aleatorio = rand();
                $temp_array = explode('id=', $boton['url']);
                echo '
                <script>
                $(function() {
                    $("#'.$aleatorio.'").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        bloquear();
                        $(this).parents(".marco").load("cargadores/actualizar.php",{t: window.t, modulo: "'.$modulo.'", ventana: "'.$boton['url'].'", id: "'.$id.'"}, function() {
                            desbloquear();
                        });
                    });
                });
                </script>
                <a class="actualizar" href="javascript:void(0);" id="'.$aleatorio.'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$i18n_funciones[16].'" '.$boton['opciones'].'/></a>';
            } elseif ($boton['a'] == 'alta' && (!isset($boton['permiso']) || $_SESSION['perfil_'.$boton['permiso']])) {
                $aleatorio = rand();
                $temp_array = explode('.php?', $boton['url']);
                $ventana = $temp_array[0];
                echo '
                <script>
                $(function() {
                    $("#'.$aleatorio.'").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {    //Click en + agregar productos
                        e.preventDefault();
                        e.stopPropagation();
                        $("#linea_agregar #preciofinal").removeAttr("disabled");
                        $("#linea_agregar #descuento").removeAttr("disabled");
                        var parent = $(this).parent().parent();
                        var ser = parent.find("input, checkbox, textarea, select").serialize();
                        if (typeof(tributos) != "undefined")
                            var data = "&neto="+neto+"&tributos="+tributos;
                        else if (typeof(neto) != "undefined")
                            var data = "&neto="+neto;
                        else
                            var data = "";
                        $.ajax(
                        {
                            url: "cargadores/ajax.php",
                            type: "post",
                            data: "t="+window.t+"&modulo='.$modulo.'&ventana='.$ventana.'_alta&id='.$id.'&serialize="+ser+data,
                            beforeSend: function() {
                                parent.before('."'".'<div class="linea_temp"><div style="text-align: center;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/ajax.gif" title="Comunicandose con el servidor..." /></div></div>'."'".');
                                if (window.'.$ventana.'_alta_vaciar)
                                    '.$ventana.'_alta_vaciar();
                            },
                            success: function(data) {
                                parent.parent().find(".linea_temp:first").replaceWith(data);
                                parent.parents(".marco").find(".actualizar").fadeIn(500);
                            }
                        });
                    });
                });
                </script>
                <a id="'.$aleatorio.'" href="#" onclick="event.preventDefault();"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].' /></a>';
            } elseif ($boton['a'] == 'alta') {
                echo '
                    <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'_no.png" title="'.$i18n_funciones[15].strtolower($boton['title']).'" />';
            }
            if ($boton['a'] == 'mod' && (!isset($boton['permiso']) || $_SESSION['perfil_'.$boton['permiso']])) {
                $aleatorio = rand();
                $temp_array = explode('id=', $boton['url']);
                echo '
                <script>
                $(function() {
                    $("#'.$aleatorio.'").bind("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        $("#linea_'.$temp_array[1].'").hide();
                        $("#entrada_'.$temp_array[1].'").show();
                    });
                });
                </script>
                <a id="'.$aleatorio.'" href="#" onclick="event.preventDefault();"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].' /></a>';
            } elseif ($boton['a'] == 'mod') {
                echo '
                    <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'_no.png" title="'.$i18n_funciones[15].strtolower($boton['title']).'" />';
            } elseif ($boton['a'] == 'no') {
                $aleatorio = rand();
                $temp_array = explode('id=', $boton['url']);
                echo '
                <script>
                $(function() {
                    $("#'.$aleatorio.'").live("click", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        $("#linea_'.$temp_array[1].'").show();
                        $("#entrada_'.$temp_array[1].'").hide();
                    });
                });
                </script>
                <a id="'.$aleatorio.'" href="# onclick="event.preventDefault();"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].' /></a>';
            } elseif ($boton['a'] == 'ok') {
                $aleatorio = rand();
                $temp_array = explode('.php?', $boton['url']);
                $ventana = $temp_array[0];
                $temp_array = explode('id=', $boton['url']);
                echo '
                <script>
                $(function() {
                    $("#'.$aleatorio.'").live("click", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        var parent = $(this).parent().parent();
                        var ser = parent.find("input, checkbox, textarea, select").serialize();
                        if (typeof(tributos) != "undefined")
                            var data = "&neto="+neto+"&tributos="+tributos;
                        else if (typeof(neto) != "undefined")
                            var data = "&neto="+neto;
                        else
                            var data = "";
                        $.ajax(
                        {
                            url: "cargadores/ajax.php",
                            type: "post",
                            data: "t="+window.t+"&modulo='.$modulo.'&ventana='.$ventana.'_mod&id='.$temp_array[1].'&serialize="+ser+data,
                            beforeSend: function() {
                                $("#'.$aleatorio.'").parent().html('."'".'<div style="text-align: center;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/ajax.gif" title="Comunicandose con el servidor..." /></div>'."'".');
                                parent.addClass("linea_mod");
                            },
                            success: function(data) {
                                parent.parents(".marco, #marco_flotante").find(".actualizar").fadeIn(500);
                                parent.removeClass("linea_mod");
                                parent.parents(".marco, #marco_flotante").find("#linea_'.$temp_array[1].'").remove();
                                parent.parents(".marco, #marco_flotante").find("#entrada_'.$temp_array[1].'").replaceWith(data);
                            }
                        });
                    });
                });
                </script>
                <a id="'.$aleatorio.'" href="#" onclick="event.preventDefault();" class="ok"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].' /></a>';
            } elseif ($boton['a'] == 'baja' && (!isset($boton['permiso']) || $_SESSION['perfil_'.$boton['permiso']])) {
                $aleatorio = rand();
                $temp_array = explode('.php?', $boton['url']);
                $ventana = $temp_array[0];
                $temp_array = explode('id=', $boton['url']);
                echo '
                <script>
                $(function() {
                    $("#'.$aleatorio.'").live("click", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        if (confirm("'.$i18n_funciones[112].strtolower($boton['title']).'?")) {
                            var parent = $(this).parent().parent();
                            var ser = parent.find("input, checkbox, textarea, select").serialize();
                            if (typeof(tributos) != "undefined")
                                var data = "&neto="+neto+"&tributos="+tributos;
                            else if (typeof(neto) != "undefined")
                                var data = "&neto="+neto;
                            else
                                var data = "";
                            $.ajax(
                            {
                                url: "cargadores/ajax.php",
                                type: "post",
                                data: "t="+window.t+"&modulo='.$modulo.'&ventana='.$ventana.'_baja&id='.$temp_array[1].'&serialize="+ser+data'.($modulo == 'saas' ? '+"&idticket='.$id.'"' : '').',
                                beforeSend: function() {
                                    $("#'.$aleatorio.'").parent().html('."'".'<div style="text-align: center;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/ajax.gif" title="Comunicandose con el servidor..." /></div>'."'".');
                                    parent.addClass("linea_baja");
                                },
                                success: function(data) {
                                    parent.parents(".marco").find(".actualizar").fadeIn(500);
                                    parent.slideUp(700,function() {
                                        if (data) {
                                            parent.replaceWith(data);
                                        } else {
                                            parent.remove();
                                        }
                                    });
                                }
                            });
                        }
                    });
                });
                </script>
                <a id="'.$aleatorio.'" href="javascript:void(0);"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].' /></a>';
            } elseif ($boton['a'] == 'baja') {
                echo '
                    <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'_no.png" title="'.$i18n_funciones[15].strtolower($boton['title']).'" />';
            } elseif ($boton['a'] == 'descargar') {
                $aleatorio = rand();
                $temp_array = explode('id=', $boton['url']);
                echo '
                <script>
                $(function() {
                    $("#'.$aleatorio.'").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        document.location="cargadores/descargar.php?id='.$temp_array[1].($modulo == 'saas' ? '&idticket='.$id : '').'";
                    });
                });
                </script>
                <a id="'.$aleatorio.'" href="javascript:void(0);"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].' /></a>';
            }
            break;

       case 'exportar':
            $aleatorio = rand();
            $temp_array = explode('.php', $boton['url']);
            $temp_modulo = $temp_array[0];
            $temp_array = explode('id=', $boton['url']);
            $temp_id = $temp_array[1];
            if ($modulo == 'clientes' || $modulo == 'ventas') {
                //Para habilitar mensajería instantánea
                $ventas = array_sql(consulta_sql("SELECT ML_order_id, mail, ML_estado
                    FROM ventas
                    LEFT JOIN clientes ON ventas.idcliente = clientes.idcliente
                    LEFT JOIN tienda ON ML_estado = 1
                    WHERE idventa = '".$temp_id."'
                    LIMIT 1
                    "));
            }

            $fecha_mail = campo_sql(consulta_sql("SELECT fecha FROM mails_enviados WHERE idrelacion = '".$temp_id."' AND tiporelacion = '".$temp_modulo."'"));
            if ($fecha_mail) date("d-m-Y H:i:s", strtotime($fecha_mail));

            echo '
                <script>
                //Desplegables exportar
                $(function() {
                    $("#'.$aleatorio.'").position(
                        {
                        my: "right top",
                        at: "right bottom",
                        of: "#'.$aleatorio.'_boton"
                    });
                    $("#'.$aleatorio.'_boton").bind("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                        if ($(".opciones_flotantes:visible").length > 0 && e.target.nodeName == "IMG") {
                            $(".opciones_flotantes").fadeOut();
                        }
                        e.preventDefault();
                        e.stopPropagation();
                        (detectMobile() ? $("#bloquea").fadeIn(500) : "" );
                        $("#'.$aleatorio.'").fadeIn(500);
                    });
                    //$("#'.$aleatorio.'").mouseleave(function() {
                    //    $("#bloquea").fadeOut(500);
                    //    $("#'.$aleatorio.'").fadeOut(500);
                    //});
                    $("#bloquea").bind("click", function(e) {
                        $("#bloquea").fadeOut(500);
                    });
                    $("#'.$aleatorio.' a").bind("click", function(e) {
                        $("#bloquea").fadeOut(500);
                        $("#'.$aleatorio.'").fadeOut(500);
                    });
                    $("#'.$aleatorio.' #mail").live("click", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        marco_modal();
                        $.ajax(
                        {
                            url: "cargadores/modal.php",
                            type: "post",
                            data: (
                            {
                                modulo: "'.$temp_modulo.'",
                                ventana: "mail",
                                id: "'.$temp_id.'"
                            }),
                            success: function(data) {
                                $("#marco_flotante").html(data);
                            }
                        });
                    });
                    $("#'.$aleatorio.' #mensajeria_ml").live("click", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        marco_modal();
                        $.ajax(
                        {
                            url: "cargadores/modal.php",
                            type: "post",
                            data: (
                            {
                                modulo: "ventas",
                                ventana: "mensajeria_ml",
                                id: "'.$temp_id.'"
                            }),
                            success: function(data) {
                                $("#marco_flotante").html(data);
                            }
                        });
                    });
                });
                </script>
                <a id="'.$aleatorio.'_boton" href="#"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].'/></a>
                <div id="'.$aleatorio.'" class="opciones_flotantes" style="display: none;">';

            if (isset($opciones['tipo_formapago']) && $opciones['tipo_formapago'] == 'retencion')
                $temp_modulo = 'compraspagos_retenciones';

                echo '
                    <a href="exportar.php?modulo='.$temp_modulo.'&a=prn&id='.$temp_id.'" target="_blank"><img title="'.$i18n_funciones[40].'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/imprimir.png" />'.($_SESSION['mobile'] ? $i18n_funciones[40] : '').'</a><br>';
            if (($modulo == 'clientes' || $modulo == 'ventas') && $ventas['ML_estado'] && !$ventas['mail'] && $ventas['ML_order_id'] && $temp_modulo != 'ventaspagos') {
                echo '
                        <a href="#" onclick="event.preventDefault();" id="mensajeria_ml"><img title="'.$i18n_funciones[197].($fecha_mail ? " (".$i18n_funciones[216].$fecha_mail.")" : "").'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.($fecha_mail ? 'mail-enviado.png' : 'ml-mail.png').'" />'.($_SESSION['mobile'] ? $i18n_funciones[197] : '').'</a><br>';
            } else {
                echo '
                        <a href="#" onclick="event.preventDefault();" id="mail"><img title="'.$i18n_funciones[41].($fecha_mail ? " (".$i18n_funciones[216].$fecha_mail.")" : "").'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.($fecha_mail ? 'mail-enviado.png' : 'mail.png').'" />'.($_SESSION['mobile'] ? $i18n_funciones[41] : '').'</a><br>';
            }
                echo '
                    <a href="exportar.php?modulo='.$temp_modulo.'&a=html&id='.$temp_id.'"><img title="'.$i18n_funciones[42].'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/html.png" />'.($_SESSION['mobile'] ? $i18n_funciones[42] : '').'</a><br>
                    <a href="exportar.php?modulo='.$temp_modulo.'&a=pdf&id='.$temp_id.'"><img title="'.$i18n_funciones[43].'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/pdf.png" />'.($_SESSION['mobile'] ? $i18n_funciones[43] : '').'</a><br>
                    <a href="exportar.php?modulo='.$temp_modulo.'&a=planilla&id='.$temp_id.'"><img title="'.$i18n_funciones[44].'" class="no_title" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/planilla.png" />'.($_SESSION['mobile'] ? $i18n_funciones[44] : '').'</a><br>
                    '.($_SESSION['mobile'] ? '<hr><button onclick="event.preventDefault(); cerrar_modal();" class="boton">'.$i18n_funciones['cerrar'].'</button>' : '').'
                </div>';
            break;
        }
    }
    echo '
                </div>
            </div>';
}

function linea_unica(array $botones = array(), $titulo, $opciones = '', $ayuda_puntual = false)
{
    global $modulo;
    global $i18n_funciones;

    echo '<div class="contenido_100" '. $opciones .'>';
    echo '  <div class="linea_contenido">';
    if ($titulo) {
        echo '<span class="campo_nombre_unico">'.$titulo;
        ayuda_puntual($ayuda_puntual);
        echo ': </span>';
    }
    foreach ($botones as $boton) {
        switch ($boton['tipo']) {
            default:
            if (!$boton['a'])
                break;

            case 'imagen':
                $aleatorio = rand();
                echo '<div class="celda_imagen">';
                if (!$boton['url']) {
                    echo '
                        <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" />';
                } elseif (!isset($boton['permiso']) || $_SESSION['perfil_'.$boton['permiso']]) {
                    if ($boton['elemento'] == 'submit') {
                        echo '  <input type="'.$boton['tipo'].'" id="'.$aleatorio.'_boton" name="'.$boton['nombre'].'" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" alt="'.$boton['title'].'" title="'.$boton['title'].'" class="input_boton_imagen" '.$boton['opciones'].
                            ($boton['confirma']
                                ? 'onclick="return confirma(\''.$boton['confirma'].'\')"'
                                : '').' />';
                    } else {
                        echo '
                            <a href="'.$boton['url'].'" '.$boton['opciones'].'><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" /></a>';
                    }
                } else {
                    echo '
                        <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'_no.png" title="'.$i18n_funciones[15].strtolower($boton['title']).'" />';
                }
                echo '</div>';
                break;

            case 'desplegable':
                $aleatorio = rand();
                if (preg_match('/php/i', $boton['url'])) {
                    $temp_array = explode('.php?', $boton['url']);
                    $boton['url'] = $temp_array[0];
                    $get = $temp_array[1];
                }

                echo '
                    <script>
                    $(function() {
                        let idList = [];
                        $("#'.$aleatorio.'").position(
                            {
                            my: "right top",
                            at: "right bottom",
                            of: "#'.$aleatorio.'_boton"
                        });

                        $("#linea_aplicar_seleccion").hide();

                        $("#'.$aleatorio.'_boton").live("click", function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            (detectMobile() ? $("#bloquea").fadeIn(500) : "" );
                            $("#'.$aleatorio.'").fadeIn(500);
                        });
                        $("#'.$aleatorio.' a").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            let confirmado = true;
                            '.($boton['confirma']
                                ? ' confirmado = confirma(\''.$boton['confirma'].'\')'
                                : '').'

                            if (confirmado) {
                                bloquear();
                                var ser=$("form").serialize();
                                $(this).parents(".marco").load("cargadores/actualizar.php",{t: window.t, modulo: "'.$modulo.'", ventana: "'.$boton['url'].'", id: "'.$id.'", a: "'.$boton['a'].'", boton: $(this).html(), serialize: ser'.(($get) ? ', get: "'.$get.'"' : '').'}, function() {
                                    desbloquear();
                                });
                            } else {
                                return false;
                            }
                        });
                        $("#'.$aleatorio.'").mouseleave(function() {
                            $("#'.$aleatorio.'").fadeOut(500);
                        });
                    });
                    </script>
                    <div class="celda_imagen">
                        <a id="'.$aleatorio.'_boton" href="#"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['a'].'.png" title="'.$boton['title'].'" '.$boton['opciones'].'/></a>
                    </div>
                    <div id="'.$aleatorio.'" class="opciones_flotantes" style="display: none;" >';
                if (!count($boton['desplegable']))
                    echo '<span class="enlace">'.($boton['desplegable_vacio'] ? $boton['desplegable_vacio'] : $i18n_funciones[106]).'</span><br>';
                foreach ($boton['desplegable'] as $desplegable) {
                    if (!isset($desplegable['permiso']) || $_SESSION['perfil_'.$desplegable['permiso']]) {
                        echo '<a class="enlace" href="#" '.$desplegable['opciones'].'>'.$desplegable['a'].'</a><br>';
                    } else {
                        echo '<span class="enlace" title="'.$i18n_funciones[15].$desplegable['a'].'">'.$desplegable['a'].'</span><br>';
                    }
                }
                echo ($_SESSION['mobile'] ? '<hr><button onclick="event.preventDefault(); cerrar_modal();" class="boton">'.$i18n_funciones['cerrar'].'</button>' : '').'
                    </div>';
                break;
        }
    }
    echo '  </div>';
    echo '</div>';
}

function solapas_inicio(array $solapas)
{
    global $modulo;
    global $id;

    $aleatorio = rand();
    echo '
    <script>
        $(function() {
            var marco_'.$aleatorio.' = $("#'.$aleatorio.'").parents(".ventana_contenido");

            $("#'.$aleatorio.' li:first").addClass("active").show();
            marco_'.$aleatorio.'.find(".solapa").hide();
            marco_'.$aleatorio.'.find(".solapa:first").show();

            $("#'.$aleatorio.' li").bind("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                e.preventDefault();
                $("#'.$aleatorio.' li").removeClass("active");
                $(this).addClass("active");
                marco_'.$aleatorio.'.find(".solapa").hide();
                var solapa = $(this).find("a").html().replace(/\ /g, "_").toLowerCase();
                $("#"+solapa).fadeIn(500);
                var href = $(this).find("a").attr("href");
                if (href[0] != "#" && $("#"+solapa).html() == "") {
                    $.ajax(
                    {
                        url: "cargadores/ajax.php",
                        type: "post",
                        data: "t="+window.t+"&modulo='.$modulo.'&ventana="+href+"&id='.$id.'",
                        beforeSend: function() {
                            $("#"+solapa).html('."'".'<div class="linea_temp"><div style="text-align: center;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/ajax.gif" title="Comunicandose con el servidor..." /></div></div>'."'".');
                        },
                        success: function(data) {
                            $("#"+solapa).html(data);
                        }
                    });
                }

                return false;
            });
        });
    </script>
    <ul class="solapas" id="'.$aleatorio.'">';

    /* SOLAPAS */
    foreach ($solapas as $solapa) {
        if (!$solapa['url']) {
            echo '
        <li><a href="#'.strtolower(str_replace(' ', '_', $solapa['nombre'])).'">'.$solapa['nombre'].'</a></li>';
        } else {
            echo '
        <li><a href="'.$solapa['url'].'">'.$solapa['nombre'].'</a></li>';
        }
    }
    echo '
    </ul>';
}

function solapas_multiples_inicio(array $solapas)
{
    $aleatorio = rand();
    echo '
    <script>
        $(function() {
            $("#'.$aleatorio.' li:first").addClass("active").show();

            $("#'.$aleatorio.' li").bind("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                var idsolapa = $(this).attr("value");
                var solapa = $(this).attr("class");
                if(!$(this).hasClass("active")) {
                    $("input[name="+solapa+"]").val(idsolapa);
                    var solapa_activa = $("#'.$aleatorio.' li.active").find("a").parent().attr("class");
                    $("[id*="+solapa+"]").hide();
                    $("#'.$aleatorio.' li").removeClass("active");
                    $(this).addClass("active");
                    var solapa_target = $(this).find("a").html().replace(/\ /g, "_").toLowerCase();

                    $("#"+solapa_target+" > #"+solapa).fadeIn(500);
                }
                return false;
            });
        });
    </script>
    <ul class="solapas" id="'.$aleatorio.'">';

    /* SOLAPAS */
    foreach ($solapas as $solapa) {
        //sin !$solapa['url']
        echo '
        <li class="'.$solapa['tipo'].'" value="'.$solapa['idsolapa'].'"><a href="#'.strtolower(str_replace(' ', '_', $solapa['nombre'])).'">'.$solapa['nombre'].'</a></li>';
    }
    echo '
    </ul>
    ';
}

function solapas_fin()
{

}

function solapa_inicio($nombre, $opciones = '')
{
    echo '
    <div id="'.strtolower(str_replace(' ', '_', $nombre)).'" '.$opciones.' class="solapa '.strtolower(str_replace(' ', '_', $nombre)).'">';
}

function solapa_fin()
{
    echo '</div>';
}

function bloque_inicio($nombre, $opciones = '')
{
    echo '
            <div id="'.$nombre.'" '.$opciones.'>';
}

function bloque_fin()
{
    echo '
            </div>';
}

function celda($tipo, $valor, $ancho = 'auto', $imagen = false, $url = false, $opciones = [], $ayuda_puntual = '', $simbolo = '$', $title = false)
{
    global $modulo;
    global $id;
    global $ventana;
    global $i18n_funciones;

    if (!is_array($opciones) && $opciones)
        $opciones = ['string' => $opciones];
    $opciones['string'] = $opciones['string'] ?? '';
    $opciones['clase'] = $opciones['clase'] ?? '';

    if ($tipo == 'largo') {
        $valor = strip_tags($valor, '<b>');
    } else {
        $valor = htmlspecialchars($valor, ENT_QUOTES);
    }

    if ($ancho > 0 && $ancho <= 100) {
        echo '
                    <div class="celda '.$opciones['clase'].'" style="width: '.($ancho - ($_SESSION['mobile'] && $opciones == 'id="codigo_nuevo"' ? 2 : 0)).'%; padding-left: 0.5%; padding-right: 0.5%;" '.$opciones['string'].($title ? ' title = "'.$valor.'"' : '').'>';
    } elseif ($ancho == 'imagen') {
        echo '
                    <div class="celda_imagen '.$opciones['clase'].'" '.$opciones['string'].'>';
    } else {
        echo '
                    <div class="celda_auto '.$opciones['clase'].'" '.$opciones['string'].($title ? ' title = "'.$valor.'"' : '').'>';
    }
    if ($url) {
        echo '<a href="'.$url.'" class="enlace"'.($tipo == 'url'?' target="_blank"':'').'>';
    }
    if ($tipo == 'imagen' && $imagen) {
        echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$imagen.'.png" title="'.$valor.'" >'.$opciones['texto'].'</>';
    } else {
        switch ($tipo) {
            case 'ordenar':
                $aleatorio = rand();
                echo '
                <script>
                $(function() {
                   $("#'.$aleatorio.'").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        bloquear();
                        var ser=$("form").serialize();
                        $(this).parents(".marco").load("cargadores/actualizar.php",{t: window.t, modulo: "'.$modulo.'", ventana: "'.$ventana.'", id: "'.$id.'", boton: $(this).html()}, function() {
                            desbloquear();
                        });
                    });
                });
                </script>';
                $valor = '<a href="" id="'.$aleatorio.'" title="'.$i18n_funciones[75].$valor.'" class="ordenar">'.$valor.'</a>&nbsp'.$imagen;
                break;

            case 'moneda':
                if ($valor || $valor === '0')
                    $valor = $simbolo.' '.($_SESSION['control_formato_separador_miles'] ? number_format($valor, 2, ',', '.') : number_format($valor, 2, '.', ''));
                else
                    $valor = '';
                break;

            case 'descuento':
                if ($valor || $valor === '0')
                    $valor = ($_SESSION['control_formato_separador_miles'] ? number_format($valor, 2, ',', '.') : number_format($valor, 2, '.', '')).' %';
                else
                    $valor = '';
                break;

            case 'iva':
                if ($valor == 'No gravado')
                    $valor = 'No Grav';
                elseif ($valor != 'Exento'){}
                    //$valor = number_format($valor, 2, '.', '').' %';  //Quito acá porque Andiaco ;)
                break;

            case 'porcentaje':
                if ($valor || $valor === '0')
                    $valor = number_format($valor, 2, '.', '').' %';
                else
                    $valor = '';
                break;

            case 'cantidad':
                if ($valor || $valor === '0')
                    $valor = number_format($valor, 2, '.', '');
                else
                    $valor = '';
                break;

            case 'fechayhora':
                if ($valor == 'ahora')
                    $valor = date("d-m-Y H:i");
                elseif ($valor == '0000-00-00 00:00:00')
                    $valor = '';
                elseif ($valor) {
                    $temp_array = explode(' ', $valor);
                    if ($temp_array[0] == '0000-00-00')
                        $valor = '';
                    else {
                        $temp_array2 = explode('-', $temp_array[0]);
                        if (strlen($temp_array2[0]) == 4)
                            $valor = $temp_array2[2].'-'.$temp_array2[1].'-'.$temp_array2[0];
                        elseif (strlen($temp_array2[2]) == 4)
                            $valor = $temp_array[0];
                        else
                            $valor = '';
                        if ($valor && $temp_array[1] && $temp_array[1] != '00:00' && $temp_array[1] != '00:00:00' && $temp_array[1][2] == ':')
                            $valor.= ' '.substr($temp_array[1], 0, 5);
                    }
                }
                break;

            case 'fecha':
                if ($valor == 'ahora')
                    $valor = date("d-m-Y");
                elseif ($valor == '0000-00-00' || $valor == '0000-00-00 00:00:00')
                    $valor = '';
                elseif ($valor) {
                    $temp_array = explode(' ', $valor);
                    $temp_array2 = explode('-', $temp_array[0]);
                    if (strlen($temp_array2[0]) == 4)
                        $valor = $temp_array2[2].'-'.$temp_array2[1].'-'.$temp_array2[0];
                    elseif (strlen($temp_array2[2]) != 4)
                        $valor = '';
                }
                break;

            case 'hora':
                if ($valor == 'ahora')
                    $valor = date("H:i");
                elseif ($valor && $valor[2] == ':')
                    $valor = ' '.substr($valor, 0, 5);
                else
                    $valor = '';
                break;

            case 'tiempo':
                break;

            case 'cuit':
                if ($valor)
                    $valor = $valor['0'].$valor['1'].'-'.$valor['2'].$valor['3'].$valor['4'].$valor['5'].$valor['6'].$valor['7'].$valor['8'].$valor['9'].'-'.$valor['10'];
                break;

            case 'mail':
                $valor = '<a href="mailto:'.$valor.'" class="enlace">'.$valor.'</a>';
                break;

            case 'resaltado':
                $valor = '<span class="resaltado">'.$valor.'</span>';
                break;

            case 'alerta':
                $valor = '<span class="alerta">'.$valor.'</span>';
                break;

            case 'largo':
                $valor = '<span title="'.$valor.'">'.$valor.'</span>';
                break;

            case 'bytes':
                if ($valor < 1)
                    $valor = '0 B';
                elseif ($valor < 1024)
                    $valor.= ' B';
                elseif ($valor < 1048576)
                    $valor = number_format($valor / 1024, 2, '.', '').' KB';
                elseif ($valor < 1073741824)
                    $valor = number_format($valor / 1048576, 2, '.', '').' MB';
                else
                    $valor = '';
                break;
        }
        echo $valor;
        ayuda_puntual($ayuda_puntual);
    }
    if ($url) {
        echo '</a>';
    }
    echo '</div>';
}

function texto($tipo, $titulo = '', $valor = '', $ancho = 'auto', $url = false, $imagen = false, $ayuda_puntual = false, $opciones = [], $simbolo = '$')
{
    global $modulo;
    global $id;

    if (!is_array($opciones) && $opciones)
        $opciones = ['string' => $opciones];
    $opciones['string'] = $opciones['string'] ?? '';

    // Puedo empezar con un <br> para que lo muestre abajo como en las entradas
    if (mb_substr($valor, 0, 4) === "<br>") {
        $valor = htmlspecialchars(mb_substr($valor, 4), ENT_QUOTES);
        $bajo_linea = true;
    } else {
        $valor = htmlspecialchars($valor, ENT_QUOTES);
    }

    switch ($tipo) {
        case 'moneda':
            if ($valor || $valor === '0')
                $valor = $simbolo.' '.($_SESSION['control_formato_separador_miles'] ? number_format($valor, 2, ',', '.') : number_format($valor, 2, '.', ''));
            break;

        case 'descuento':
            if ($valor || $valor === '0')
                $valor = ($_SESSION['control_formato_separador_miles'] ? number_format($valor, 2, ',', '.') : number_format($valor, 2, '.', ''));
            break;

        case 'porcentaje':
            if ($valor || $valor === '0')
                $valor = number_format($valor, 2, '.', '').'%';
            break;

        case 'cantidad':
            if ($valor || $valor === '0')
                $valor = number_format($valor, 2, '.', '');
            break;

        case 'fechayhora':
            if ($valor == '0000-00-00 00:00:00')
                $valor = '';
            elseif ($valor) {
                $temp_array = explode(' ', $valor);
                if ($temp_array[0] == '0000-00-00')
                    $valor = '';
                else {
                    $temp_array2 = explode('-', $temp_array[0]);
                    if (strlen($temp_array2[0]) == 4)
                        $valor = $temp_array2[2].'-'.$temp_array2[1].'-'.$temp_array2[0];
                    elseif (strlen($temp_array2[2]) == 4)
                        $valor = $temp_array[0];
                    else
                        $valor = '';
                    if ($valor && $temp_array[1] && $temp_array[1] != '00:00' && $temp_array[1] != '00:00:00' && $temp_array[1][2] == ':')
                        $valor.= ' '.substr($temp_array[1], 0, 5);
                }
            }
            break;

        case 'fecha':
            $temp_array = explode(' ', $valor);
            if (!$valor || $temp_array[0] == '0000-00-00')
                $valor = '';
            else {
                $temp_array2 = explode('-', $temp_array[0]);
                if (strlen($temp_array2[0]) == 4)
                    $valor = $temp_array2[2].'-'.$temp_array2[1].'-'.$temp_array2[0];
                elseif (strlen($temp_array2[2]) == 4)
                    $valor = $temp_array[0];
                else
                    $valor = '';
            }
            break;

        case 'hora':
            if ($valor && $valor[2] == ':')
                $valor = ' '.substr($valor, 0, 5);
            else
                $valor = '';
            break;

        case 'tiempo':
            if ($valor == '00:00:00')
                $valor = '';
            break;

        case 'cuit':
            $valor = mostrar_cuit($valor);
            break;

        case 'mail':
            $valor = '<a href="mailto:'.$valor.'" class="enlace">'.$valor.'</a>';
            break;

        case 'resaltado':
            $valor = '<span class="resaltado">'.$valor.'</span>';
            break;

        case 'italica':
            $valor = '<span class="italica">'.$valor.'</span>';
            break;

        case 'alerta':
            $valor = '<span class="alerta">'.$valor.'</span>';
            break;

        case 'largo':
            $valor = '<span title="'.$valor.'">'.$valor.'</span>';
            break;

        case 'bytes':
            if ($valor < 1)
                $valor = '0 B';
            elseif ($valor < 1024)
                $valor.= ' B';
            elseif ($valor < 1048576)
                $valor = number_format($valor / 1024, 2, '.', '').' KB';
            elseif ($valor < 1073741824)
                $valor = number_format($valor / 1048576, 2, '.', '').' MB';
            else
                $valor = '';
            break;
    }
    if ($url && $tipo != 'texto_icono') {
        $valor = '<a href="'.$url.'" class="enlace"'.($tipo == 'url'?' target="_blank"':'').'>'.$valor.'</a>';
    }
    if ($ancho > 0 && $ancho <= 100) {
        echo '
            <div class="texto" style="width: '.($ancho - 1).'%; padding-left: 0.5%; padding-right: 0.5%;">';
    } else {
        echo '
            <div class="texto" style="width: 99%; padding-left: 0.5%; padding-right: 0.5%;">';
    }
    if ($titulo) {
        echo '<span class="campo_nombre">'.$titulo;
        ayuda_puntual($ayuda_puntual);
        echo ': </span>';
    }
    if ($bajo_linea)
        bajo_linea();
    if ($valor || $imagen) {
        $title = isset($opciones['title']) ? $opciones['title'] : '';
        $img = $imagen
            ? '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'
                .$imagen . ($imagen == 'ajax' ? '.gif' : '.png')
                .'" style="margin-top: -5px;" title="'.$title.'">'
            : '';

        if ($url && (!isset($opciones['tipo']) || $opciones['tipo'] != 'flotante')) {
            $img = '<a href="'.$url.'" class="enlace">'.$img.'</a>&nbsp;';
        } else if ($opciones['tipo'] == 'flotante') {
            $url = $opciones['url'];
            $img = '<a href="javascript:void(0);" class="enlace" onclick="modal('."'$modulo'".', '."'$url'".', '."'$id'".'); return false;">'.$img.'</a>&nbsp;';
        } else if ($img) {
            $img.= '&nbsp;';
        }
        echo '<span class="campo_texto" '.$opciones['string'].'>' . $img . $valor . '</span>';
    }
    if (!$titulo && $ayuda_puntual) {
        ayuda_puntual($ayuda_puntual);
    }
    if (isset($opciones['clipboard'])) {
        clipboard($opciones['clipboard']);
    }
    echo '</div>';
}

function observacion($titulo, $valor, $ancho = 'auto', $imagen = false)
{
    if ($ancho > 0 && $ancho <= 100) {
        echo '
            <div class="observacion" style="width: '.($ancho - 1).'%; padding-left: 0.5%; padding-right: 0.5%;">';
    } else {
        echo '
            <div class="observacion" style="width: 99%; padding-left: 0.5%; padding-right: 0.5%;">';
    }
    if ($titulo) {
        echo '<span class="campo_nombre">'.$titulo.': </span><br>';
    }

    if ($imagen) {
        $img = $imagen
            ? '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'
                .$imagen . ($imagen == 'ajax' ? '.gif' : '.png"')
                .'" style="margin-top: -5px;">&nbsp;'
            : '';
    }
    echo '<span class="campo_observacion">',$img,$valor.'</span></div>';

}

function clipboard($textToCopy)
{
    echo '&nbsp;<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/duplicar.png" onclick="copyClipboard(\''.$textToCopy.'\')" title="Copiar al portapapeles">';
}

function prioridad()
{
    // TODO: HAY QUE HACER ESTA FUNCIÓN COMPLETA
}

function estado()
{
    // TODO: HAY QUE HACER ESTA FUNCIÓN COMPLETA
}

function ayuda_puntual($valor)
{

    if ($valor && $_SESSION['control_ayuda_puntual']) {
        $aleatorio = rand();
        echo '
                <script>
                $(function() {
                    $("#'.$aleatorio.'_boton").live("mouseover", function() {
                        $("#flotante").append('."'".'<div class="informacion_especifica" id="#'.$aleatorio.'"><span class="campo_texto">'.$valor.'</span></div>'."'".');
                        $(".informacion_especifica").position(
                        {
                            my: "center bottom",
                            at: "right top",
                            of: "#'.$aleatorio.'_boton"
                        });
                    });
                });
                </script>
                <img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/puntual.png" id="'.$aleatorio.'_boton">';
    }
}

function entrada($tipo, $nombre, $titulo = '', $valor = '', $ancho = 'auto', $maxlenght = false, $ayuda_puntual = false, $opciones = '', $agregar = false)
{
    global $i18n_funciones;

    $valor = htmlspecialchars($valor, ENT_QUOTES);
    $min_height = (($titulo) ? ' min-height: 50px;': '');
    if ($tipo != 'hidden') {
        if ($ancho > 0 && $ancho <= 100) {
            echo '
            <div class="entrada" style="width: '.($ancho - 1).'%;'.$min_height.' padding-left: 0.5%; padding-right: 0.5%;">';
        } else {
            echo '
            <div class="entrada" style="width: 99%;'.$min_height.' padding-left: 0.5%; padding-right: 0.5%;">';
        }
        if ($titulo) {
            echo '<label for="'.$nombre.'"><span class="campo_nombre">'.$titulo.'</span></label>';
            ayuda_puntual($ayuda_puntual);

            if ($agregar && $_SESSION['perfil_configuraciones_tablas'])
            {
                global $i18n_funciones;
                global $modulo;
                $aleatorio = rand();
                echo '
                    <script>
                    if (!',$nombre,')
                        var ',$nombre,' = "',$valor,'";
                    $(function()
                    {
                        $("#',$aleatorio,'").live("click", function(e)
                        {
                            e.preventDefault();
                            e.stopPropagation();
                            marco_modal();
                            $.ajax(
                            {
                                url: "cargadores/modal.php",
                                type: "post",
                                data: (
                                {
                                    modulo: "configuraciones",
                                    ventana: "configuraciones_'.$agregar.'"
                                }),
                                success: function(data) {
                                    $("#marco_flotante").html(data);
                                }
                            });
                        });
                        $("#select_'.$nombre.'").live("change", function(e)
                        {
                            window.',$nombre,' = $("#select_'.$nombre.' option:selected").val();

                        });
                    });';
                echo '
                    </script>
                    <a href="javascript:void(0)" id="',$aleatorio,'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_',$_SESSION['usuario_idestilo'],'/images/abm.png" title="',$i18n_funciones[38],'" /></a>';
            }
        }

    }

    $clase= VALIDADOR_PREFIJO_CLASE.$nombre;
    switch ($tipo) {
        default:
        case 'email':
        case 'texto':
            echo '<input type="'.$tipo.'" id="'.$nombre.'" name="'.$nombre.'" class="entrada_text '.$clase.'" value="'.$valor.'" ';
            if ($maxlenght)
                echo 'maxlength="'.$maxlenght.'" ';
            echo $opciones.'/>';
            // TODO: Al verificar que todos los text tienen su maxlenght sacar esto
            if (!$maxlenght && ESTADO == 'desarrollo') {
                echo '
            <script>
                $(function() {
                    alert("La entrada '.$nombre.' no tiene puesto el maxlenght");
                });
            </script>';
            }
            break;

        case 'password':
            echo '<input type="password" id="'.$nombre.'" name="'.$nombre.'" class="entrada_password '.$clase.'" value="'.$valor.'" ';
            if ($maxlenght)
                echo 'maxlength="'.$maxlenght.'" ';
            echo $opciones.'/>';
            break;

        case 'hidden':
            echo '
            <input type="hidden" id="'.$nombre.'" name="'.$nombre.'" value="'.$valor.'" '.$opciones.'/>';
            break;

        case 'file':
            echo '
            <input type="file" id="'.$nombre.'" name="'.$nombre.'" class="entrada_file '.$clase.'" '.$opciones.'/>';
            break;

        case 'camera':
            echo '
            <input type="file" accept="image/*" webkitdirectory="true" id="'.$nombre.'" name="'.$nombre.'" class="entrada_file '.$clase.'" '.$opciones.'/>';
            break;

        case 'moneda':
            //$aleatorio = rand();
            if ($valor || $valor === '0')
                $valor = number_format($valor, 2, '.', '');
            echo '
            <script>
                $(function() {
                    $("input[alt='.$tipo.']").setMask();
                });
            </script>
                <input type="text" name="'.$nombre.'" id="'.$nombre.'" class="entrada_text '.$clase.'" value="'.$valor.'" alt="moneda" '.$opciones.'/>';
            break;

        case 'monedanegativa':
            $aleatorio = rand();
            echo '
            <script>
                $(function() {
                    $("input[alt='.$tipo.']").setMask();
                });
            </script>
            <input type="text" name="'.$nombre.'" id="'.$nombre.'" class="entrada_text '.$clase.'" value="'.$valor.'" alt="monedanegativa" '.$opciones.'/>';
            break;

        case 'descuento':
        case 'porcentaje':
        case 'cantidad':
        case 'cantidadnegativa':
            if ($valor || $valor === '0')
                $valor = number_format($valor, 2, '.', '');
            $aleatorio = rand();
            echo '
            <script>
                $(function() {
                    $("input[alt='.$tipo.']").setMask();
                });
            </script>';
            echo'
            <input type="text" name="'.$nombre.'" id="'.$nombre.'" class="entrada_text '.$clase.'" value="'.$valor.'" alt="'.$tipo.'"  '.$opciones.'/>';
            break;

        case 'fechayhora':
            $aleatorio = rand();
            echo '
            <script>
                $(function() {
                    $("#'.$aleatorio.'").datetimepicker({dateFormat: "dd-mm-yy", timeFormat: "HH:mm", showButtonPanel: true, monthNamesShort: ["Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre"], dayNames: ["Domingo", "Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado"], dayNamesMin: ["Do", "Lu", "Ma", "Mi", "Ju", "Vi", "Sa"], currentText: "Ahora", closeText: "Aceptar", weekHeader: "", nextText: "Próximo", prevText: "Anterior", timeText: "Tiempo", hourText: "Horas", minuteText: "Minutos", showAnim: "slideDown", showOtherMonths: true, selectOtherMonths: true, showWeek: false, firstDay: 1, changeMonth: true, changeYear: true});
                });
            </script>';
            if ($valor == 'ahora')
                $valor = date("d-m-Y H:i");
            elseif ($valor == '0000-00-00 00:00:00')
                $valor = '';
            elseif ($valor) {
                $temp_array = explode(' ', $valor);
                if ($temp_array[0] == '0000-00-00')
                    $valor = '';
                else {
                    $temp_array2 = explode('-', $temp_array[0]);
                    if (strlen($temp_array2[0]) == 4)
                        $valor = $temp_array2[2].'-'.$temp_array2[1].'-'.$temp_array2[0];
                    elseif (strlen($temp_array2[2]) == 4)
                        $valor = $temp_array[0];
                    else
                        $valor = '';
                    if ($valor && $temp_array[1] && $temp_array[1] != '00:00' && $temp_array[1] != '00:00:00' && $temp_array[1][2] == ':')
                        $valor.= ' '.substr($temp_array[1], 0, 5);
                }
            }
            echo '
                <input type="text" name="'.$nombre.'" id="'.$aleatorio.'" class="entrada_text '.$clase.'" value="'.$valor.'" maxlength="16" '.$opciones.' placeholder="dd-mm-aaaa hh:mm" />';
            break;

        case 'fecha':
            $aleatorio = rand();
            echo '
            <script>
                $(function() {
                    $("#'.$nombre.'").datepicker({dateFormat: "dd-mm-yy", showButtonPanel: true, monthNamesShort: ["Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre"], dayNames: ["Domingo", "Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado"], dayNamesMin: ["Do", "Lu", "Ma", "Mi", "Ju", "Vi", "Sa"], currentText: "Hoy", closeText: "Aceptar", weekHeader: "", nextText: "Próximo", prevText: "Anterior", showAnim: "slideDown", showOtherMonths: true, selectOtherMonths: true, showWeek: false, firstDay: 1, changeMonth: true, changeYear: true});
                });
            </script>';
            if ($valor == 'ahora')
                $valor = date("d-m-Y");
            elseif ($valor == '0000-00-00')
                $valor = '';
            elseif ($valor) {
                $temp_array2 = explode('-', $valor);
                if (strlen($temp_array2[0]) == 4)
                    $valor = $temp_array2[2].'-'.$temp_array2[1].'-'.$temp_array2[0];
                elseif (strlen($temp_array2[2]) != 4)
                    $valor = '';
            }
            echo '
                <input type="text" name="'.$nombre.'" id="'.$nombre.'" class="entrada_text '.$clase.'" value="'.$valor.'" maxlength="10" '.$opciones.' placeholder="dd-mm-aaaa" />';
            break;

        case 'hora':
            $aleatorio = rand();
            echo '
            <script>
                $(function() {
                    $("#'.$nombre.'").timepicker({});
                });
            </script>';
            if ($valor == 'ahora')
                $valor = date("H:i");
            elseif ($valor && $valor[2] == ':')
                $valor = ' '.substr($valor, 0, 5);
            else
                $valor = '';
            echo '<input type="text" name="'.$nombre.'" id="'.$nombre.'" class="entrada_text '.$clase.'" value="'.$valor.'" maxlength="5" '.$opciones.'/>';
            break;

        case 'tiempo':
            $aleatorio = rand();
            echo '
            <script>
                $(function() {
                    $("input[alt='.$tipo.']").setMask();
                });
            </script>
            <input type="text" name="'.$nombre.'" alt="tiempo" id="'.$nombre.'" class="entrada_text '.$clase.'" value="'.$valor.'" maxlength="6" '.$opciones.'/>';
            break;

        case 'cuit':
            global $ventana;

            $aleatorio = rand();
            // Como tengo comentada la validación, solo me aseguro de que no se imprima un 0
            if (strlen($valor) < 11)
                $valor = '';
            // $cuit = $valor[0].$valor[1].'-'.$valor[2].$valor[3].$valor[4].$valor[5].$valor[6].$valor[7].$valor[8].$valor[9].'-'.$valor[10];

            if (!$_SESSION['mobile']) {
                echo '
                <script>
                    $(function() {
                        $("input[alt='.$tipo.']").setMask();';
                /*TODO volar este if entero cuando unifiquemos la validacion con jquery.validate*/
                // if ($ventana != 'clientes_alta' && $ventana != 'clientes_mod' ){
                //         echo '$("#'.$nombre.'_entrada").change(function() {
                //             var value = $("#'.$nombre.'_entrada").val();
                //             var '.$nombre.' = $("#'.$nombre.'_entrada").val();
                //             $("#'.$nombre.'").val('.$nombre.'.replace(/-/g,""));
                //             if (value.match(/^\d{2}\-\d{8}\-\d{1}$/)) {
                //                 var sum = 11 - ((value[0] * 5 + value[1] * 4 + value[3] * 3 + value[4] * 2 + value[5] * 7 + value[6] * 6 + value[7] * 5 + value[8] * 4 + value[9] * 3 + value[10] * 2) % 11);
                //                 sum = (sum==10)? 0 : sum;
                //                 if (value[12] != sum) {
                //                     $("#'.$nombre.'_entrada").parents(".marco").append('."'".'<div class="alerta_especifica" id="validacion_flotante_'.$aleatorio.'"><span class="campo_texto">'.$i18n_funciones[9].'</div>'."'".');
                //                     $("#validacion_flotante_'.$aleatorio.'").position(
                //                     {
                //                         my: "left bottom",
                //                         at: "center top",
                //                         of: "#'.$nombre.'_entrada"
                //                     });
                //                     setTimeout('."'".'$("#validacion_flotante_'.$aleatorio.'").remove()'."'".',5000);
                //                 } else {
                //                 }
                //             } else {
                //                 $("#'.$nombre.'").val("");
                //             }
                //         });';
                // }
                echo '});
                </script>';
            }
            echo '
            <input type="text" name="'.$nombre.'" id="'.$nombre.'"  class="entrada_text '.$clase.'" value="'.$valor.'" alt="cuit" '.$opciones.'/>';
            // <input type="hidden" name="'.$nombre.'" id="'.$nombre.'" value="'.$valor.'" />
            break;

        case 'numeros':
            $aleatorio = rand();
            if (!$maxlenght)
                $maxlenght = 8;
            if (!$_SESSION['mobile']) {
                echo '
                <script>
                    $(function() {
                        $.mask.masks.'.$nombre.' = {mask: "';
                for($i = 0; $i < $maxlenght; $i++)
                    echo '9';
                echo '", type : "reverse"}
                        $("#'.$nombre.'").setMask();
                    });
                </script>';
            }
            echo '
            <input type="text" name="'.$nombre.'" id="'.$nombre.'" class="entrada_text '.$clase.'" value="'.$valor.'" alt="'.$nombre.'" '.$opciones.'/>';
            break;

        case 'letras':
            $aleatorio = rand();
            if (!$maxlenght)
                $maxlenght = 8;
            echo '
            <script>
                $(function() {
                    $.mask.masks.'.$nombre.' = {mask: "';
            for($i = 0; $i < $maxlenght; $i++)
                echo 'a';
            echo '"}
                    $("#'.$nombre.'").setMask();
                });
            </script>
            <input type="text" name="'.$nombre.'" id="'.$nombre.'" class="entrada_text '.$clase.'" value="'.$valor.'" alt="'.$nombre.'" '.$opciones.'/>';
            break;

        case 'mail':
            $aleatorio = rand();
            if (!$maxlenght)
                $maxlenght = 320;
            echo '
            <input type="email" name="'.$nombre.'" id="'.$nombre.'" class="entrada_text '.$clase.'" value="'.$valor.'" alt="'.$nombre.'" '.$opciones.'/>';
            break;

        case 'droparea':
            echo('
                <iframe name="subir" id="subir" style="width:0px; height:0px; border-width:0px; display:none;"></iframe>');
            echo'
                <section class="droparea">
                    <div id="image-preview"></div>
                    <img id="insert-image" src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/insert_image.png" title="'.$i18n_funciones[312].'">
                    <p>'.$i18n_funciones[312].'</p>
                    <p><small>'.$i18n_funciones[313].'</small></p>
                </section>
            ';
            break;
    }
    if ($tipo != 'hidden') {
        echo '</div>';
    }
}

function area($nombre, $titulo, $valor, $ancho = 'auto', $ayuda_puntual = false, $opciones = '', $insertar_tags = false, $mp = false)
{
    if (!strpos($valor, '<p>') && !strpos($valor, '<br>'))
        $valor = nl2br($valor);
    $valor = str_replace('\\"', '"', $valor);
    $valor = htmlspecialchars($valor, ENT_QUOTES);
    $aleatorio = rand();
    $tags = "";
    if ($insertar_tags){
        $tags="insert_tags:true,";
    }
    if ($mp){
        $mp="insert_mp:true,";
    }
    echo '
        <script>
        $(function() {
            if ($("#'.$aleatorio.'").is(":visible")) {
                $("#'.$aleatorio.'").redactor({
                    lang: "es",'.
                    $tags.$mp.'
                    autoresize: false,
                    minHeight: 100,
                    plugins: ["fullscreen"]
                });
            }
        });
        </script>';
    if ($ancho > 0 && $ancho <= 100) {
        echo '
            <div class="entrada" style="width: '.($ancho - 1).'%; padding-left: 0.5%; padding-right: 0.5%;">';
    } else {
        echo '
            <div class="entrada" style="width: 99%; padding-left: 0.5%; padding-right: 0.5%;">';
    }
    if ($titulo) {
        echo '<label for="'.$nombre.'"><span class="campo_nombre">'.$titulo.'</span></label>';
        ayuda_puntual($ayuda_puntual);
    }
    echo '<textarea name="'.$nombre.'" id="'.$aleatorio.'" class="entrada_textarea '.VALIDADOR_PREFIJO_CLASE.$nombre.'" '.$opciones.'>'.$valor.'</textarea></div>';
}

function marca($nombre, $valor = false, $ancho = 'imagen', $opciones = '')
{
    echo '
                    <script>
                    $(function() {
                        $("input[name='."'".$nombre."'".']").click(function() {
                            $(this).parents(".marco").find(".realizar").fadeIn(500);
                            if ($(this).is(":checked"))
                                $(this).parent().parent().parent().addClass("linea_seleccionada");
                            else
                                $(this).parent().parent().parent().removeClass("linea_seleccionada");
                        });
                    });
                    </script>';
    if ($ancho > 0 && $ancho <= 100) {
        echo '
                    <div class="celda" style="width: '.($ancho - 1).'%; padding-left: 0.5%; padding-right: 0.5%;">';
    } elseif ($ancho == 'imagen') {
        echo '
                    <div class="celda_imagen">';
    } else {
        echo '
                    <div class="celda_auto">';
    }
    echo '<input type="checkbox" name="'.$nombre.'" class="entrada_checkbox" value="1"';
    if ($valor)
        echo ' checked="checked"';
    echo ' '.$opciones.'/></div>';
}

function marcas($titulo = false, $ancho = 'auto', array $marcas = array(), $ayuda_puntual = false)
{
    if ($ancho > 0 && $ancho <= 100) {
        echo '
            <div class="entrada" style="width: '.($ancho - 1).'%; padding-left: 0.5%; padding-right: 0.5%;">';
    } else {
        echo '
            <div class="entrada" style="width: 99%; padding-left: 0.5%; padding-right: 0.5%;">';
    }
    if ($titulo) {
        echo '<span class="campo_nombre">'.$titulo.'</span>';
        ayuda_puntual($ayuda_puntual);
        echo '<br>';
    }
    foreach ($marcas as $marca) { //checkeamos que sea 1 y no 'on'
        if (!empty($marca)) {
            echo '<span class="campo_texto"><input type="checkbox" name="'.$marca['nombre'].'" class="entrada_checkbox" value="1" '.$marca['opciones'].' ';
            if ($marca['valor'])
                echo 'checked="checked" ';
            echo '/><span class="campo_nombre_span">&nbsp;'.$marca['titulo'].'</span></span>';
            ayuda_puntual($marca['ayuda_puntual']);
            echo ' '.$marca['custom_field'];
            echo '<br>';
        }
    }
    echo '</div>';
}

function marcas_todas($nombre)
{
    global $i18n_funciones;

    $aleatorio = rand();
    echo '
                    <script>
                    $(function() {
                        $("#'.$aleatorio.'").position(
                            {
                            my: "left top",
                            at: "left bottom",
                            of: "#'.$aleatorio.'_boton"
                        });
                        $("#'.$aleatorio.'_boton").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                            //e.preventDefault();
                            $("#'.$aleatorio.'").fadeIn(300);
                        });
                        $("#'.$aleatorio.'").mouseleave(function() {
                            $("#'.$aleatorio.'").fadeOut(500);
                        });
                        $("#'.$aleatorio.' a[href=#marcar_todos]").bind("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                            e.preventDefault();
                            $(this).parents(".marco").find(".realizar").fadeIn(500);
                            $("#'.$aleatorio.'").parents(".marco").find(".linea_impar, .linea_par").addClass("linea_seleccionada");
                            $("#'.$aleatorio.'").parents(".marco").find("input[type='."'".'checkbox'."'".']").attr("checked", true);
                            $("#'.$aleatorio.'").fadeOut(500);
                        });
                        $("#'.$aleatorio.' a[href=#desmarcar_todos]").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                            e.preventDefault();
                            $(this).parents(".marco").find(".realizar").fadeIn(500);
                            $("#'.$aleatorio.'").parents(".marco").find(".linea_impar, .linea_par").removeClass("linea_seleccionada");
                            $("#'.$aleatorio.'").parents(".marco").find("input[type='."'".'checkbox'."'".']").attr("checked", false);
                            $("#'.$aleatorio.'").fadeOut(500);
                        });
                        $("#'.$aleatorio.' a[href=#invertir_marcas]").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                            e.preventDefault();
                            $(this).parents(".marco").find(".realizar").fadeIn(500);
                            $("#'.$aleatorio.'").parents(".marco").find("input[type='."'".'checkbox'."'".']").not("input[name='."'".$nombre."'".']").each(function() {
                                if ($(this).is(":checked")) {
                                    $(this).parent().parent().parent().removeClass("linea_seleccionada");
                                    $(this).attr("checked", false);
                                } else {
                                    $(this).parent().parent().parent().addClass("linea_seleccionada");
                                    $(this).attr("checked", true);
                                }
                            });
                            $("#'.$aleatorio.'").fadeOut(500);
                        });
                    });
                    </script>
                    <div class="celda_imagen"><a href="javascript:void(0);" id="'.$aleatorio.'_boton" title="'.$i18n_funciones[14].'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/marcas.png"/></a></div>
                    <div id="'.$aleatorio.'" class="opciones_flotantes" style="display: none; text-align: left;"><a class="enlace" href="#marcar_todos">'.$i18n_funciones[11].'</a><br><a class="enlace" href="#desmarcar_todos">'.$i18n_funciones[12].'</a><br><a class="enlace" href="#invertir_marcas">'.$i18n_funciones[13].'</a></div>';
}

function selector($nombre, $titulo, $valor, $ancho, $tabla, $orden = false, $agregar = false, $cero = true, $estado = false, $ayuda_puntual = false, $opciones = false, $especiales = array())
{
    global $i18n_funciones;

    $sql = "SELECT ".$nombre.", nombre FROM ".$tabla;

    $where = array();
    if ($estado)
        $where[] = 'estado = 1';
    if ($cero === false)
        $where[] = $nombre.' > 0';
    if (count($where))
        $sql.= " WHERE ".implode(' AND ', $where);

    $sql.= $orden
        ? " ORDER BY ".$orden
        : " ORDER BY ".$nombre;

    $resultado_sql = consulta_sql($sql);
    $valor = htmlspecialchars($valor, ENT_QUOTES);
    if ($titulo)
        $min_height = ' min-height: 50px;';
    else
        $min_height = '';

    if ($ancho > 0 && $ancho <= 100) {
        echo '
            <div class="entrada '.$nombre.'" style="width: '.($ancho - 1).'%;'.$min_height.' padding-left: 0.5%; padding-right: 0.5%;">';
    } else {
        echo '
            <div class="entrada '.$nombre.'" style="width: 99%;'.$min_height.' padding-left: 0.5%; padding-right: 0.5%;">';
    }
    if ($titulo) {
        echo '<label for="'.$nombre.'"><span class="campo_nombre">'.$titulo.'</span></label>';
        ayuda_puntual($ayuda_puntual);
        if ($agregar && $_SESSION['perfil_configuraciones_tablas'])
        {
            global $i18n_funciones;
            global $modulo;
            $aleatorio = rand();
            echo '
                <script>
                if (!',$nombre,')
                    var ',$nombre,' = "',$valor,'";
                $(function()
                {
                    $("#',$aleatorio,'").live("click", function(e)
                    {
                        e.preventDefault();
                        e.stopPropagation();
                        marco_modal();
                        $.ajax(
                        {
                            url: "cargadores/modal.php",
                            type: "post",
                            data: (
                            {
                                modulo: "configuraciones",
                                ventana: "configuraciones_'.$tabla.'"
                            }),
                            success: function(data) {
                                $("#marco_flotante").html(data);
                            }
                        });
                    });
                    $("#select_'.$nombre.'").live("change", function(e)
                    {
                        window.',$nombre,' = $("#select_'.$nombre.' option:selected").val();

                    });
                });';
            //ESTO ES LO QUE TENGO QUE HACER FUNCIONAR PARA QUE ME MUESTRE UNA AYUDA PUNTUAL SI NO HAY OPCIONES EN EL SELECTOR
            // if (contar_sql($resultado_sql) < 3) {
            //     echo '
            //     $("input[name=mail]").focus(function() {';
            //     script_validacion_flotante('informacion', 'ACA LA LA LA', 'input[name=mail]', false, 1000000);
            //     echo '
            //     });';
            // }
            echo '
                </script>
                <a href="javascript:void(0)" id="',$aleatorio,'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_',$_SESSION['usuario_idestilo'],'/images/abm.png" title="',$i18n_funciones[38],'" /></a>';
        }
    }
    echo '
                <select id="select_'.$nombre.'" name="'.$nombre.'" class="entrada_select '.VALIDADOR_PREFIJO_CLASE.$nombre.'" '.$opciones.'>';
    if (count($especiales)) {
        foreach ($especiales as $key => $value) {
            echo '
                    <option value="'.$key.'"'.(($key === $valor) ? ' selected="selected"' : '').'>'.$value.'</option>';
        }
    }
    if ($cero) {
        echo '
                    <option value=""'.(($valor === '0') ? ' selected="selected"' : '').'>'.(($cero === true) ? $i18n_funciones[24] : $cero).'</option>';
//cambio option value="0" x option value="" así manda x GET valor '' en lugar de '0'
    }
    while ($temp_array = array_sql($resultado_sql)) {
        if (($cero && $temp_array[$nombre] != 0) || !$cero) {
            echo '
                    <option value="'.$temp_array[$nombre].'"'.(($temp_array[$nombre] == $valor) ? ' selected="selected"' : '').'>'.$temp_array['nombre'].'</option>';
        }
    }
    echo '
              </select>
            </div>';
}

function options_sub_selector($nombre, $idpadre, $valor, $tabla, $estado = false)
{
    $resultado_sql = consulta_sql(
        "SELECT ".$nombre.", ".$nombre."padre, nombre, padres,
            IF (EXISTS(SELECT * FROM ".$tabla." AS sub WHERE sub.".$nombre."padre = c.".$nombre."), 1, 0) AS tiene_hijos
        FROM ".$tabla." AS c
        WHERE ".$nombre." > 0"
            . ($estado ? " AND estado='1'" : "")
            . " AND ".$nombre."padre = '$idpadre' ORDER BY nombre ASC");

    $tab = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
    while ($option = array_sql($resultado_sql)) {
        $temp_tabs = '';
        for ($j = 0; $j < $option['padres']; $j++)
            $temp_tabs.= $tab;
        if ($option['tiene_hijos']) {
            echo '
                    <option value="'.$option[$nombre].'"'.($option[$nombre] == $valor ? ' selected="selected"' : '').'>'.$temp_tabs.$option['nombre'].'</option>';
            options_sub_selector($nombre, $option[$nombre], $valor, $tabla, $estado);

        } else {
            echo '
                    <option value="'.$option[$nombre].'"'.($option[$nombre] == $valor ? ' selected="selected"' : '').'>'.$temp_tabs.$option['nombre'].'</option>';
        }
    }


}

function selector_familiar($nombre, $titulo, $valor, $ancho, $tabla, $agregar = false, $cero = false, $estado = false, $ayuda_puntual = false, $opciones = false, $vacio = false, $especiales = array())
{
    global $i18n_funciones;

    $valor = htmlspecialchars($valor, ENT_QUOTES);
    if ($titulo)
        $min_height = ' min-height: 50px;';
    else
        $min_height = '';

    if ($ancho > 0 && $ancho <= 100) {
        echo '
            <div class="entrada '.$nombre.'" style="width: '.($ancho - 1).'%;'.$min_height.' padding-left: 0.5%; padding-right: 0.5%;">';
    } else {
        echo '
            <div class="entrada '.$nombre.'" style="width: 99%;'.$min_height.' padding-left: 0.5%; padding-right: 0.5%;">';
    }
    if ($titulo) {
        echo '<label for="'.$nombre.'"><span class="campo_nombre">'.$titulo.'</span></label>';
        ayuda_puntual($ayuda_puntual);
        if ($agregar && $_SESSION['perfil_configuraciones_tablas'])
        {
            global $i18n_funciones;
            global $modulo;
            $aleatorio = rand();
            echo '
                <script>
                if (!',$nombre,')
                    var ',$nombre,' = "',$valor,'";
                $(function()
                {
                    $("#',$aleatorio,'").live("click", function(e)
                    {
                        e.preventDefault();
                        e.stopPropagation();
                        marco_modal();
                        $.ajax(
                        {
                            url: "cargadores/modal.php",
                            type: "post",
                            data: (
                            {
                                modulo: "configuraciones",
                                ventana: "configuraciones_'.$tabla.'"
                            }),
                            success: function(data) {
                                $("#marco_flotante").html(data);
                            }
                        });
                    });
                    $("#select_'.$nombre.'").live("change", function(e)
                    {
                        window.',$nombre,' = $("#select_'.$nombre.' option:selected").val();
                    });
                });';
            //ESTO ES LO QUE TENGO QUE HACER FUNCIONAR PARA QUE ME MUESTRE UNA AYUDA PUNTUAL SI NO HAY OPCIONES EN EL SELECTOR
            // if (contar_sql($resultado_sql) < 3) {
            //     echo '
            //     $("input[name=mail]").focus(function() {';
            //     script_validacion_flotante('informacion', 'ACA LA LA LA', 'input[name=mail]', false, 1000000);
            //     echo '
            //     });';
            // }
            echo '
                </script>
                <a href="javascript:void(0)" id="',$aleatorio,'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_',$_SESSION['usuario_idestilo'],'/images/abm.png" title="',$i18n_funciones[38],'" /></a>';
        }
    }
    echo '
                <select id="select_'.$nombre.'" name="'.$nombre.'" class="entrada_select '.VALIDADOR_PREFIJO_CLASE.$nombre.'" '.$opciones.'>';
    if (count($especiales)) {
        foreach ($especiales as $key => $value) {
            echo '
                    <option value="'.$key.'"'.(($key === $valor) ? ' selected="selected"' : '').'>'.$value.'</option>';
        }
    }
    if ($cero) {
        echo '
                    <option value="0">'.(($cero === true) ? $i18n_funciones[24] : $cero).'</option>';
    }
    if (!$vacio) {
        options_sub_selector($nombre, 0, $valor, $tabla, $estado);
    }
    echo '
              </select>
            </div>';
}

function selector_array($nombre, $titulo, $valor = false, $ancho = 'auto', array $selector_opciones = array(), $ayuda_puntual = false, $opciones = false, $agregar = false)
{
    if ($titulo){
        $min_height = ' min-height: 50px;';
    } else {
        $min_height = '';
    }

    if ($ancho > 0 && $ancho <= 100) {
        echo '
            <div class="entrada" style="width: '.($ancho - 1).'%;'.$min_height.' padding-left: 0.5%; padding-right: 0.5%;">';
    } else {
        echo '
            <div class="entrada" style="width: 99%;'.$min_height.' padding-left: 0.5%; padding-right: 0.5%;">';
    }
    if ($titulo) {
        echo '<label for="'.$nombre.'"><span class="campo_nombre">'.$titulo.'</span></label>';
        ayuda_puntual($ayuda_puntual);
    }
    if ($agregar && $_SESSION['perfil_configuraciones_tablas'])
    {
        global $i18n_funciones;
        global $modulo;
        $aleatorio = rand();
        $id = recibir_variable('id', true);
        echo '
            <script>
            if (!',$nombre,')
                var ',$nombre,' = "',$valor,'";
            $(function()
            {
                $("#',$aleatorio,'").live("click", function(e)
                {
                    e.preventDefault();
                    e.stopPropagation();
                    marco_modal();
                    $.ajax(
                    {
                        url: "cargadores/modal.php",
                        type: "post",
                        data: (
                        {
                            id: "'.$id.'",
                            modulo: "configuraciones",
                            ventana: "configuraciones_'.$nombre.'"
                        }),
                        success: function(data) {
                            $("#marco_flotante").html(data);
                        }
                    });
                });
                $("#select_'.$nombre.'").live("change", function(e)
                {
                    window.',$nombre,' = $("#select_'.$nombre.' option:selected").val();

                });
            });';
        echo '
            </script>
            <a href="javascript:void(0)" id="',$aleatorio,'"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_',$_SESSION['usuario_idestilo'],'/images/abm.png" title="',$i18n_funciones[38],'" /></a>';
    }

    echo '
                <select name="'.$nombre.'" class="entrada_select '.VALIDADOR_PREFIJO_CLASE.$nombre.'" '.$opciones.'>';
    foreach ($selector_opciones as $opcion) {
        $datasets = array();
        if (isset($opcion['datasets']) && count($opcion['datasets'])) {
            foreach ($opcion['datasets'] as $key => $value)
                $datasets[] = $key.'="'.$value.'"';
        }
        echo '
                    <option'.(($opcion['selected'] || $opcion['id'] == $valor) ? ' selected="selected" ' : '')
            .' value="'.$opcion['id'].'" '
            .implode(' ', $datasets)
            .'>'.$opcion['valor'].'</option>';
    }
    echo '
                </select>
            </div>';
}

function botones(array $botones = array(), $ayuda_puntual = false)
{
    echo '
        <div class="botones">';
    foreach ($botones as $boton) {
        if ($boton['tipo'] == 'modal' || $boton['tipo'] == 'modal-dialog') $tipo = 'button'; else $tipo = 'submit';
        if (!in_array($boton['tipo'], array('actualizar', 'nueva', 'ajax', 'modal', 'modal-dialog')))
            $boton['tipo'] = 'actualizar';

        if ($boton['tipo'] == 'modal-dialog') {
            $temp_modulo = $boton['modulo'];
            $aleatorio = rand();
        echo '
            <input type="'.$tipo.'" id="'.$aleatorio.'" name="'.$boton['tipo'].'" class="boton '.$boton['orientacion'].'" value="'.$boton['valor'].'" '.$boton['opciones'].' onclick="modal('."'".$temp_modulo."', '".$boton['url']."', '".$boton['id']."'".'); return false;"/>';
        } else {
        echo '
            <input type="'.$tipo.'" name="'.$boton['tipo'].'" class="boton" value="'.$boton['valor'].'" '.$boton['opciones'].'/>';
        }

    }
    if ($ayuda_puntual)
        ayuda_puntual($ayuda_puntual);
    echo '
        </div>';
}

function agrandar(array $botones = array())
{
    global $modulo;
    global $id;

    echo '
            <div class="linea_agrandar">';
    $cantidad = count($botones) - 1;
    foreach ($botones as $boton) {
        $aleatorio = rand();
        echo '
                <script>
                    $("#'.$aleatorio.'").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        var parent = $(this).parent();
                        $.ajax(
                        {
                            url: "cargadores/ajax.php",
                            type: "post",
                            data: (
                            {
                                t: window.t,
                                modulo : "'.$modulo.'",
                                ventana : "'.$boton['url'].'",
                                id : "'.$id.'",
                                boton : "'.$boton['opciones'].'_'.$boton['cantidad'].'",
                                id_boton : "'.$boton['id_boton'].'"
                            }),
                            beforeSend: function() {
                                parent.html('."'".'<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/ajax.gif" title="Comunicandose con el servidor..."/>'."'".');
                            },
                            success: function(data) {
                                var agregado = $(data);';  //var agregado = $(data).hide(); y al final agregado.slideDown(700);
        if ($boton['arriba'])
            echo '
                                parent.parent().children(".linea_titulo").after(agregado);';
        else
            echo '
                                parent.parent().append(agregado);';
        echo '
                                parent.remove();
                            }
                        });
                    });
                </script>

                <!-- Mostrar Mas -->
                <a href="javascript:void(0);" id="'.$aleatorio.'" class="enlace">'.$boton['valor'].'</a>';

        if ($cantidad) {
            echo '&nbsp;&nbsp;|&nbsp;&nbsp;';
            $cantidad--;
        }
    }
    echo '
            </div>';
}

function enlaces($titulo = '', array $botones = array(), $ancho = 'auto', $ayuda_puntual = false, $imagen = false, $opciones = [])
{
    if ($ancho > 0 && $ancho <= 100) {
        echo '
            <div class="texto" style="width: '.($ancho - 1).'%; padding-left: 0.5%; padding-right: 0.5%;">';
    } else {
        echo '
            <div class="texto" style="width: 99%; padding-left: 0.5%; padding-right: 0.5%;">';
    }
    if ($titulo) {
        echo '
                <span class="campo_nombre">'.$titulo.': </span>';
        ayuda_puntual($ayuda_puntual);
        echo '&nbsp;&nbsp;';
    }

    if (isset($opciones['title']) && $opciones['title']) {
        $title = $opciones['title'];
        $img = $imagen
            ? '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'
                .$imagen . ($imagen == 'ajax' ? '.gif' : '.png')
                .'" style="margin-top: -5px; margin-left: 5px; " title="'.$title.'">'
            : '';
        $img.= '&nbsp;';
    }
    $cantidad = count($botones) - 1;
    foreach ($botones as $boton) {
        switch ($boton['tipo']) {
            default:
            case 'enlace':
                echo '
                <a href="'.$boton['url'].'" class="enlace" '.$boton['opciones']
                .($boton['confirma']
                    ? 'onclick="return confirma(\''.$boton['confirma'].'\')"'
                    : '')
                .'>';
                if ($boton['imagen']) {
                    echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['imagen'].'.png" title="'.$boton['valor'].'"/>&nbsp;';
                }
                echo $boton['valor'];
                echo '</a>';
                echo '<span class="campo_texto" '.$opciones['string'].'>' . $img . '</span>';
                break;

            case 'imagen':
                echo '
                <a href="'.$boton['url'].'" class="enlace" '.$boton['opciones'].'>';
                if ($boton['imagen']) {
                    echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['imagen'].'.png" title="'.$boton['valor'].'"/>&nbsp;';
                }
                echo '</a>';
                break;

            case 'ajax':
                global $modulo;
                $aleatorio = rand();
                echo '
                <script>
                    $("#'.$aleatorio.'").live("'.($_SESSION['mobile'] ? 'touchstart' : 'click').'", function(e) {
                        e.preventDefault();
                        e.stopPropagation();'
                        .($boton['confirma']
                            ? 'if (!confirma("'.$boton['confirma'].'")) return false;'
                            : '')
                        .'$.ajax(
                        {
                            url: "cargadores/ajax.php",
                            type: "post",
                            data: (
                            {
                                t: window.t,
                                modulo : "'.$modulo.'",
                                ventana : "'.$boton['url'].'",
                                id: "'.$boton['id'].'"
                            }),
                            beforeSend: function() {
                                $("#'.$aleatorio.'").html('."'".'<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/ajax.gif" title="Comunicandose con el servidor..."/>'."'".');
                            },
                            success: function(data) {
                                $("#'.$aleatorio.'").html(data);
                            }
                        });
                    });
                </script>
                <a href="javascript:void(0)" class="enlace" id="'.$aleatorio.'" '.$boton['opciones'].'>';
                if ($boton['imagen']) {
                    echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['imagen'].'.png" title="'.$boton['valor'].'"/>&nbsp;';
                }
                echo $boton['valor'];
                echo '</a>';

                break;

            case 'javascript':
                if ($boton['imagen']) {
                    echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['imagen'].'.png" title="'.$boton['valor'].'" style="cursor: pointer;" '.$boton['opciones'].'/>&nbsp;';
                }
                echo '<a href="javascript:void(0)" class="enlace" '.$boton['opciones'].'/>'.$boton['valor'].'</a>';
                break;

            case 'flotante':
                global $modulo;
                $aleatorio = rand();
                echo '
                    <script>
                    $(function() {
                        $("#'.$aleatorio.'").bind("click", function(e) {
                            //e.preventDefault();
                            //e.stopPropagation();
                            marco_flotante();
                            $.ajax(
                            {
                                url: "cargadores/flotante.php",
                                type: "post",
                                data: (
                                {
                                    modulo: "'.$modulo.'",
                                    ventana: "'.$boton['url'].'",
                                    id: "'.$boton['id'].'"
                                }),
                                success: function(data) {
                                    $("#marco_flotante").html(data);
                                }
                            });
                        });
                    });
                    </script>
                    <a href="#" onclick="event.preventDefault()" class="enlace" id="'.$aleatorio.'" '.$boton['opciones'].'>';
                    if ($boton['imagen']) {
                        echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['imagen'].'.png" title="'.$boton['valor'].'"/>&nbsp;';
                    }
                    echo $boton['valor'];
                    echo '</a>';
                break;

            case 'modal':
                if ($boton['modulo']) {
                    $temp_modulo = $boton['modulo'];
                } else {
                    global $modulo;
                    $temp_modulo = $modulo;
                }

                $aleatorio = rand();
                // echo '
                //     <script>
                //     $(function() {
                //         $("#'.$aleatorio.'").live("click", function(e) {
                //             e.preventDefault();
                //             e.stopPropagation();
                //             marco_modal();
                //             var ser=$("form").serialize();
                //             $.ajax(
                //             {
                //                 url: "cargadores/modal.php",
                //                 type: "post",
                //                 data: (
                //                 {
                //                     modulo: "'.$temp_modulo.'",
                //                     ventana: "'.$boton['url'].'",
                //                     id: "'.$boton['id'].'",
                //                     serialize: ser
                //                 }),
                //                 success: function(data) {
                //                     $("#marco_flotante").html(data);
                //                 }
                //             });
                //         });
                //     });
                //     </script>
                //     <a href="#" class="enlace" id="'.$aleatorio.'" '.$boton['opciones'].'>';
                echo '
                    <a href="javascript:void(0)" class="enlace" id="'.$aleatorio.'" '.$boton['opciones'].'
                        onclick="modal('."'".$temp_modulo."', '".$boton['url']."', '".$boton['id']."'".'); return false;">';
                    if ($boton['imagen']) {
                        echo '<img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/'.$boton['imagen'].'.png" title="'.$boton['valor'].'"/>&nbsp;';
                    }
                    echo $boton['valor'];
                    echo '</a>';
                break;
        }
        if ($cantidad) {
            echo '&nbsp;&nbsp;|&nbsp;&nbsp;';
            $cantidad--;
        }
    }
    echo '
            </div>';
}

function video($valor, $lista = false)
{
    if ($_SESSION['mobile']) {
        $dimensiones = 'width="auto" height="auto"';
    } else {
        $dimensiones = 'width="640" height="360"';
    }

    if (!$lista)
        echo '
            <div class="video"><iframe '.$dimensiones.' src="https://www.youtube.com/embed/'.$valor.'?&hl=es_ES&rel=0&autoplay=1" frameborder="0" allowfullscreen></iframe></div>';
    else
        echo '
            <div class="video"><iframe '.$dimensiones.' src="https://www.youtube.com/embed/videoseries?list='.$valor.'&autoplay=1" frameborder="0" allowfullscreen></iframe></div>';
}

function imagen($valor, $opciones = '')
{
    if ($_SESSION['mobile'] && $opciones) {
        echo '
            <div><img src="'.$valor.'" class="imagen" '.$opciones.'/></div>';
    } else {
        echo '
            <div class="texto" style="width: 99%; padding-left: 0.5%; padding-right: 0.5%;"><img src="'.$valor.'" class="imagen" '.$opciones.'/></div>';
    }
}

function miniatura($valor, $opciones = '')
{
    echo '
            <div class="texto"><img style="margin-left: 5px; margin-right: 5px;" src="'.$valor.'" class="imagen" '.$opciones.'/></div>';
}

function icono($imagen, $opciones = '')
{
    echo '
            <div style="float: left;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/iconito_'.$imagen.'.png" class="ventana_iconito"/></div>';
}

function salto_linea()
{
    echo '
        <div class="salto_linea"></div>';
}

function bajo_linea()
{
    echo '
        <br />';
}

function extras()
{
    global $i18n_funciones;
    global $modulo;
    global $id;

    if ($id)
        $resultado_sql = consulta_sql("SELECT idextraxmodulo, tipo, nombre, (SELECT texto FROM datosxextras WHERE extrasxmodulos.idextraxmodulo=datosxextras.idextraxmodulo AND idrelacion='".$id."' LIMIT 1) AS texto, (SELECT idlistaxextra FROM datosxextras WHERE extrasxmodulos.idextraxmodulo=datosxextras.idextraxmodulo AND idrelacion='".$id."' LIMIT 1) AS idlistaxextra FROM extrasxmodulos WHERE modulo='".$modulo."' ORDER BY idextraxmodulo");
    else
        $resultado_sql = consulta_sql("SELECT * FROM extrasxmodulos WHERE modulo='".$modulo."' ORDER BY idextraxmodulo");
    if (contar_sql($resultado_sql)) {
        contenido_inicio($i18n_funciones[30]);
        {
            while ($extra = array_sql($resultado_sql)) {
                switch ($extra['tipo']) {
                    case 'lista':
                        $resultado_sql2 = consulta_sql("SELECT * FROM listasxextras WHERE idextraxmodulo='".$extra['idextraxmodulo']."'");
                        $listasxextras = array();
                        while ($temp_array = array_sql($resultado_sql2)) {
                            $listasxextras[] = array('id' => $temp_array['idlistaxextra'], 'valor' => $temp_array['nombre']);
                        }
                        selector_array('extra_'.$extra['idextraxmodulo'], $extra['nombre'], $extra['idlistaxextra'], '33', $listasxextras);
                        break;

                    case 'texto':
                        entrada('texto', 'extra_'.$extra['idextraxmodulo'], $extra['nombre'], $extra['texto'], '33', '100');
                        break;
                }
            }
        }
        contenido_fin();
    } elseif ($modulo != 'ventas') {
        contenido_inicio($i18n_funciones[30], '100', true, false);
        {
            texto('texto', $i18n_funciones[184], false, '21');
            enlaces('', array(array('tipo' => 'enlace', 'url' => 'configuraciones.php?a=datosextra', 'valor' => $i18n_funciones[185], 'modulo' => 'tickets')), '75');
        }
        contenido_fin();
    }
}

function extras_ver()
{
    global $i18n_funciones;
    global $modulo;
    global $id;

    $resultado_sql = consulta_sql("SELECT tipo, nombre, (SELECT texto FROM datosxextras WHERE extrasxmodulos.idextraxmodulo=datosxextras.idextraxmodulo AND idrelacion='".$id."' LIMIT 1) AS texto, (SELECT listasxextras.nombre FROM datosxextras INNER JOIN listasxextras ON datosxextras.idlistaxextra=listasxextras.idlistaxextra WHERE extrasxmodulos.idextraxmodulo=datosxextras.idextraxmodulo AND idrelacion='".$id."' LIMIT 1) AS listaxextra FROM extrasxmodulos WHERE modulo='".$modulo."'");
    if (contar_sql($resultado_sql)) {
        contenido_inicio($i18n_funciones[30]);
        {
            while ($extra = array_sql($resultado_sql)) {
                switch ($extra['tipo']) {
                    case 'lista':
                        texto('texto', $extra['nombre'], $extra['listaxextra'], '33');
                        break;

                    case 'texto':
                        texto('texto', $extra['nombre'], $extra['texto'], '33');
                        break;
                }
            }
        }
        contenido_fin();
    }
}

function cargando()
{
    echo '<div style="text-align: center;"><img src="'.$_SESSION['servidor_url'].'estilos/estilo_'.$_SESSION['usuario_idestilo'].'/images/ajax.gif" title="Comunicandose con el servidor..." /></div>';
}

function script($script)
{
    echo '<script>
    ' . $script .'
    </script>';
}
