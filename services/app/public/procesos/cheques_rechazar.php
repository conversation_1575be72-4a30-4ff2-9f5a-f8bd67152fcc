<?php
//TODO -> WIP
$datos = array_sql(consulta_sql(
    "SELECT c.*,
        mxcv.idmovimientoxcaja AS idmovimientoxcaja_v, mxcv.idconciliacion AS idconciliacion_v, mxcv.fechaconciliacion AS fechaconciliacion_v, mxcv.conciliacion AS conciliacion_v, mxcv.idconcepto AS idconcepto_v, mxcv.idcaja AS idcaja_v, mxcv.total AS total_v,
        mxcc.idmovimientoxcaja AS idmovimientoxcaja_c, mxcc.idconciliacion AS idconciliacion_c, mxcc.fechaconciliacion AS fechaconciliacion_c, mxcc.conciliacion AS conciliacion_c, mxcc.idconcepto AS idconcepto_c, mxcc.idcaja AS idcaja_c, mxcc.total AS total_c
    FROM cheques c
        LEFT JOIN compraspagos cp ON c.idcheque = cp.idrelacion AND cp.tiporelacion = 'cheque'
        LEFT JOIN ventaspagos vp ON c.idcheque = vp.idrelacion AND vp.tiporelacion = 'cheque'
        LEFT JOIN movimientosxcajas mxcv ON vp.idventapago = mxcv.idrelacion AND mxcv.tiporelacion = 'clientepago'
        LEFT JOIN movimientosxcajas mxcc ON cp.idcomprapago = mxcc.idrelacion AND mxcc.tiporelacion = 'proveedorpago'
        WHERE c.idcheque = '$id'
    LIMIT 1"));

//Conciliación?
//Saldos?
//Otras tablas?
    consulta_sql("INSERT INTO movimientosxcajas SET
        idcaja = '".$datos['idcaja_c']."',
        idusuario = '".$_SESSION['usuario_idusuario']."',
        idconcepto = '".$datos['idconcepto_c']."',
        fecha = NOW(),
        total = '".($datos['total_c'] * -1)."',
        detalle = 'Cheque rechazado',
        tiporelacion = 'proveedorpago',
        idrelacion = '".$datos['idmovimientoxcaja_c']."'
    ");

    if ($datos['tipo'] == 'tercero') {
        consulta_sql("INSERT INTO movimientosxcajas SET
            idcaja = '".$datos['idcaja_v']."',
            idusuario = '".$_SESSION['usuario_idusuario']."',
            idconcepto = '".$datos['idconcepto_v']."',
            fecha = NOW(),
            total = '".($datos['total_v'] * -1)."',
            detalle = 'Cheque rechazado',
            tiporelacion = 'clientepago',
            idrelacion = '".$datos['idmovimientoxcaja_v']."'
        ");
    }

consulta_sql("UPDATE cheques SET estado = 0 WHERE idcheque = $id");

$script = 'location.reload();';
modal_cerrar($script);
exit();