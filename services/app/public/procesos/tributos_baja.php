<?php

$comprobante = array_sql(consulta_sql("SELECT * FROM ".$GLOBALS['modulo']." WHERE ".$GLOBALS['idmodulo']." =
    (SELECT ".$GLOBALS['idmodulo']." FROM tributosx".$GLOBALS['modulo']." WHERE idtributox".$GLOBALS['modulo_singular']." = '$id')"));

if (!$comprobante) {
    script_flotante('alerta', $i18n[966]);
    exit();
}

$buscar_monedas = [
    $GLOBALS['modulo'] => $comprobante[$GLOBALS['idmodulo']],
    $GLOBALS['tercero'] => $comprobante[$GLOBALS['idtercero']]
];
// if ($comprobante['tiporelacion'] == 'servicio')
//     $buscar_monedas['servicios'] = $comprobante['idrelacion'];
$idmonedas = idmonedas($buscar_monedas);

consulta_sql(
    "DELETE FROM tributosx{$GLOBALS['modulo']}
    WHERE idtributox".substr($GLOBALS['modulo'], 0, -1)." = '$id'
    LIMIT 1");

// Recalculo el comprobante y actualizo saldo
$recalculada = comprobantes_recalculando($comprobante[$GLOBALS['idmodulo']]);
if ($comprobante['estado'] == 'cerrado'
    && $comprobante['muevesaldo']
    && $comprobante['total'] != $recalculada['total']) {
    $diferencia = ($comprobante['operacioninversa'] ? -1 : 1) * ($recalculada['total'] - $comprobante['total']);
    actualizar_saldo($GLOBALS['modulo'], $comprobante[$GLOBALS['idmodulo']], cotizacion($idmonedas[$GLOBALS['modulo']], $idmonedas[$GLOBALS['tercero']], $diferencia));
    actualizar_saldo($GLOBALS['tercero'], $comprobante[$GLOBALS['idtercero']], cotizacion($idmonedas[$GLOBALS['tercero']], $idmonedas[$GLOBALS['modulo']], $diferencia));
    if ($comprobante['tiporelacion'] == 'servicio')
        actualizar_saldo($GLOBALS['modulo'].'xservicios', $comprobante['idrelacion'], $diferencia); //No aplica aún
}
