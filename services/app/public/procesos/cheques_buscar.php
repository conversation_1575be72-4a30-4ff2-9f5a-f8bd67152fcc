<?php
$max_limit_cheques = 100;
$busqueda = recibir_variable('busqueda');

switch ($boton) {
    case 'todo':
        $resultado_sql = consulta_sql("SELECT c.*, tb.nombre AS banco,
                CASE
                    WHEN tipo = 'tercero' THEN vp.fecha
                    ELSE cp.fecha
                END AS fechacarga,
                CASE
                    WHEN tipo = 'tercero' THEN vp.total
                    ELSE cp.total
                END AS total
            FROM cheques c
            INNER JOIN tablas_bancos tb ON c.idbanco = tb.idbanco
            LEFT JOIN compraspagos cp ON c.idcheque = cp.idrelacion AND cp.tiporelacion = 'cheque'
            LEFT JOIN ventaspagos vp ON c.idcheque = vp.idrelacion AND vp.tiporelacion = 'cheque'
            ORDER BY c.idcheque DESC
            LIMIT ".$max_limit_cheques);
        break;
    case 'buscar':
    case 'ir':
        $resultado_sql = consulta_sql("SELECT c.*, tb.nombre AS banco,
                CASE
                    WHEN tipo = 'tercero' THEN vp.fecha
                    ELSE cp.fecha
                END AS fechacarga,
                CASE
                    WHEN tipo = 'tercero' THEN vp.total
                    ELSE cp.total
                END AS total
            FROM cheques c
            INNER JOIN tablas_bancos tb ON c.idbanco = tb.idbanco
            LEFT JOIN compraspagos cp ON c.idcheque = cp.idrelacion AND cp.tiporelacion = 'cheque'
            LEFT JOIN ventaspagos vp ON c.idcheque = vp.idrelacion AND vp.tiporelacion = 'cheque'
            WHERE c.numero LIKE '%".$busqueda."%'
            OR cp.total LIKE '%".$busqueda."%'
            OR vp.total LIKE '%".$busqueda."%'
            OR c.titular LIKE '%".$busqueda."%'
            OR tb.nombre LIKE '%".$busqueda."%'
            LIMIT ".$max_limit_cheques);

        break;
}

$i = 1;
if (!contar_sql($resultado_sql)) {
    linea_inicio();
    {
        texto('texto', false, $i18n[9]);
    }
    linea_fin();

} elseif ($boton == 'ir') {
    $cheque = campo_sql($resultado_sql, 0);
    echo '<script>document.location="cheques.php?a=ver&id='.campo_sql($resultado_sql, 0).'"</script>';

} else {
    while ($cheque = array_sql($resultado_sql)) {
        if ($boton == 'buscar') {
            $coincidencias = array();
            $busqueda_dividida = explode(" ", $busqueda);
            foreach ($busqueda_dividida as $palabra) {
                if ($palabra) {
                    if (!array_key_exists('numero', $coincidencias) && stripos($cheque['numero'], $palabra) !== false)
                        $coincidencias['numero'] = $i18n[4].': '.$cheque['numero'];
                    if (!array_key_exists('titular', $coincidencias) && stripos($cheque['titular'], $palabra) !== false)
                        $coincidencias['titular'] = $i18n[3].': '.$cheque['titular'];
                    if (!array_key_exists('total', $coincidencias) && stripos($cheque['total'], $palabra) !== false)
                        $coincidencias['total'] = $i18n[5].': '.$cliente['total'];
                    if (!array_key_exists('banco', $coincidencias) && stripos($cheque['banco'], $palabra) !== false)
                        $coincidencias['banco'] = $i18n[6].': '.$cheque['banco'];
                }
            }
            $coincidencia = implode(' | ', $coincidencias);

            foreach ($busqueda_dividida as $palabra) {
                if ($palabra) {
                    $cheque['titular'] = preg_replace('/' . $palabra . '/i', "<b class=match>\$0</b>", $cheque['titular']);
                    $coincidencia = preg_replace('/' . $palabra . '/i', "<b class=match>\$0</b>", $coincidencia);
                }
            }
        }

        linea_inicio('fila', 1);
        {
            entrada('hidden', 'id', false, $cheque['idcheque']);
            celda('largo', $i18n[4].': '.$cheque['numero'], '18');
            celda('largo', $i18n[3].': '.$cheque['titular'], '18', false, false, ' id="titular"');
            if ($coincidencia)
                celda('largo', $coincidencia, '18');
            celda('moneda', $cheque['total'], '10');
            celda('texto', $i18n[6].': '.$cheque['banco'], '22');
            celda('texto', $i18n[7].': '.date("d/m/Y", strtotime($cheque['fechacarga'])));
        }

        linea_fin(array(
            array('a' => 'ver', 'url' => 'cheques.php?a=ver&id='.$cheque['idcheque'], 'title' => $i18n[8]),
        ));
    }
    if (contar_sql($resultado_sql) >= $max_limit_cheques) {
        linea_inicio();
        {
            texto('texto', false, str_replace('{{cantidad}}', $max_limit_cheques, $i18n_funciones[318]), 'auto', false, 'alerta');
        }
        linea_fin();
    }
}
