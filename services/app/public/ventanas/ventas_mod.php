<?php

$datos = array();

// Si ya fue eliminada la venta o el cliente ya no existe
if (!array_sql(consulta_sql("SELECT idventa FROM ventas WHERE idventa = '$id'
    AND EXISTS(SELECT idcliente FROM clientes WHERE idcliente = ventas.idcliente)")))
    ir_inicio($i18n[230]);

// Si es electrónica y ya fue aprobada, no se puede modificar
if (contar_sql(consulta_sql("SELECT idventa FROM ventas WHERE idventa = '$id' AND estadocae = 'aprobado'")))
    ir_ahora('ventas.php?a=ver&id='.$id, $i18n[381]);

// Si mueve saldo y es una cotización anterior, no se puede modificar
if ($_SESSION['modulo_multimoneda'] && es_cotizacion_anterior($modulo, $id))
    ir_ahora('ventas.php?a=ver&id='.$id, $i18n[393]);

if (recibir_variable('act_precios', true)) {

    $venta = array_sql(consulta_sql(
        "SELECT estado, total, idcliente, tiporelacion, idrelacion, operacioninversa
        FROM ventas WHERE idventa = '$id'"));

    comprobantes_actualizar_precios_venta($id);
    comprobantes_recalculando($id);

    if ($venta['estado'] == 'cerrado') {
        $total_final = campo_sql(consulta_sql("SELECT total FROM ventas WHERE idventa = '$id'"));

        $buscar_monedas = [
            'clientes' => $venta['idcliente'],
            'ventas' => $id,
        ];
        $idmonedas = idmonedas($buscar_monedas);

        $diferencia = ($venta['operacioninversa'] ? -1 : 1) * ($total_final - $venta['total']);
        actualizar_saldo('ventas', $id, $diferencia);
        actualizar_saldo('clientes', $venta['idcliente'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia));
        if ($venta['tiporelacion'] == 'servicio') {
            actualizar_saldo('ventasxservicios', $venta['idrelacion'], cotizacion($idmonedas['clientes'], $idmonedas['ventas'], $diferencia));
        }
    }

} else {
    comprobantes_recalculando($id);
}


if ($boton) {
    $datos = recibir_matriz(array('idcaja', 'idconcepto', 'situacion', 'idformapago', 'condicionventa', 'numero', 'idusuario', 'observacion', 'fecha', 'muevesaldo', 'muevestock', 'agregarpago', 'vencimiento1', 'vencimiento2', 'recordatorios', 'concepto', 'fechainicio', 'fechafin', 'idlista', 'iddeposito', 'idlocalidad', 'idmoneda'));

    if ($boton == $i18n[16] || $boton == $i18n[14]) { //Dejar abierta o Cerrar únicamente, ya que sería solo para ventas abiertas
        consulta_sql("UPDATE controles SET ventas_agregarpago = '".$datos['agregarpago']."' WHERE idusuario = '".$_SESSION['usuario_idusuario']."'");
        $_SESSION['control_ventas_agregarpago'] = $datos['agregarpago'];
    }
}

$datos = inicializar($boton, $id, $datos);
afipsdk_lambda($_SESSION['empresa_idempresa'], $idventa);
switch ($boton) {
    case $i18n[16]: //Cerrar
        $venta = obtener_operacion($id);
        if ($venta['estado'] == 'cerrado') {
            mensajes_alta($i18n[385]);
            ir_ahora('ventas.php?a=ver&id='.$id);
        }
        actualizar_operacion($datos);
        extras_mod();
        logs(true, '', json_encode($venta).json_encode($datos));

        if (validar_cierre($venta, $datos) &&
            ($venta['tipofacturacion'] != 'electronico' || validar_fe($venta['idventa']))) {
            cerrar_venta($venta, $datos);
            ir_ahora('ventas.php?a=ver&id='.$id);
        }
    break;

    case $i18n[18]: //Anular
        $venta = inicializar_venta($id);
        $datos_validados = anular_venta($venta, $datos);
        if ($datos_validados) {
            $datos = $datos_validados;
            actualizar_operacion($datos);
            extras_mod();

            ir_ahora('ventas.php?a=ver&id='.$id);

        } else {
            actualizar_operacion($datos);
            extras_mod();
        }
    break;

    case $i18n[14]: //Dejar abierta
    case $i18n_funciones[22]: //Aceptar
        $venta = inicializar_venta($id);
        $datos_validados = aceptar_ventas($venta, $datos);
        if ($datos_validados) {
            $datos = $datos_validados;
            actualizar_operacion($datos);
            extras_mod();
            ir_atras();

        } else {
            actualizar_operacion($datos);
            extras_mod();
        }
    break;
}

$venta = inicializar_venta($id);
if (!$venta['idlista']) $venta['idlista'] = 1;
if (!$venta['iddeposito']) $venta['iddeposito'] = 1;
$cliente = inicializar_cliente($venta['idcliente']);

$numeroventa = $venta['letra'].completar_numero($venta['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8);

mensajes_efimeros();

// Alertas de modificación de ventas cerradas
if ($venta['estado'] == 'cerrado' && $venta['estadocae'] == 'sin_especificar' && $venta['muevestock'] && $venta['muevesaldo']) {
    script_flotante('informacion', $i18n[367], '10000');
} else if ($venta['estado'] == 'cerrado' && $venta['estadocae'] == 'sin_especificar') {
    if ($venta['muevestock'])
        script_flotante('informacion', $i18n[372], '10000');
    if ($venta['muevesaldo'])
        script_flotante('informacion', $i18n[373], '10000');
}

obsrecordatorio($cliente['obsrecordatorio']);
if ($cliente['pagacosto']) {
    script_flotante('informacion', $i18n[293], '10000'); // Alerta cliente pagacosto
}
if ($cliente['cuit'] && !$cliente['razonsocial']){
    script_flotante('alerta', $i18n[313], '10000');
}

$temp_confirma = $i18n[19].'\n';
if ($venta['estado'] == 'abierto') {
    if ($venta['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[132].'\n'.$i18n[137];
    if ($venta['muevestock'])
        $temp_confirma.= '\n'.$i18n[133];
} elseif ($venta['estado'] == 'cerrado') {
    if ($venta['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[134].'\n'.$i18n[137];
    if ($venta['muevestock'])
        $temp_confirma.= '\n'.$i18n[135];
}

if ($venta['estado'] == 'abierto')
    $temp_alerta_exportar = $i18n[210];
elseif ($venta['tipofacturacion'] == 'electronico' && $venta['estadocae'] != 'aprobados')
    $temp_alerta_exportar = $i18n[211];
else
    $temp_alerta_exportar = false;

if (!$boton && $datos['condicion'] && !$venta['condicionventa'] && !$venta['condicionventa'])
    $venta['condicionventa'] = $datos['condicion'];

ventana_inicio($i18n[84].$venta['nombre'].' '.$numeroventa, '100', array(
    array('tipo' => 'imagen', 'url' => 'ventas.php?a=ver&id='.$id, 'a' => 'ver', 'title' => $i18n[138]),
    array('tipo' => 'imagen', 'url' => 'ventas.php?a=baja&id='.$venta['idventa'], 'a' => 'baja', 'title' => $i18n[61], 'permiso' => 'ventas_baja', 'opciones' => 'onclick="return confirma('."'".$temp_confirma."'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[62],
        'opciones' => ($temp_alerta_exportar
            ? 'onclick="return alerta('."'".$temp_alerta_exportar."'".')"'
            : ''
            ))));
{
    // Datos básicos
    contenido_inicio($i18n[34], '50');
    {
        texto('texto', $i18n[106], $i18n[$venta['estado']]);
        texto('texto', $i18n[49], $venta['nombre']);
        if ($_SESSION['perfil_ventas_mod_todos']) {
            if ($venta['tipofacturacion'] == 'electronico') {
                if ($venta['estadocae'] != 'aprobado')
                    texto('texto', $i18n[50], $numeroventa . $i18n[380], 'auto', false, 'alerta');
                else
                    texto('texto', $i18n[50], $numeroventa);

            } else {
                entrada('letras', 'letra', $i18n[193], $venta['letra'], '20', '1', false, 'disabled="disabled"');
                entrada('numeros', 'puntodeventa', $i18n[213], completar_numero($venta['puntodeventa'], 5), '40', '5', false, 'disabled="disabled"');
                entrada('numeros', 'numero', $i18n[50], $venta['numero'], '40');
            }
            entrada('fechayhora', 'fecha', $i18n[52], $venta['fecha']);

        } else {
            texto('texto', $i18n[50], $venta['numero']);
            texto('fechayhora', $i18n[52], $venta['fecha']);
            entrada('hidden', 'fecha', false, mostrar_fecha('fechayhora', $venta['fecha']));
        }
        // Condición de venta
        selector_array('condicionventa', $i18n[65], $venta['condicionventa'], 'auto', array(
            array('id' => 'contado', 'valor' => $i18n['contado']),
            array('id' => 'cuentacorriente', 'valor' => $i18n['cuentacorriente']),
            array('id' => 'debito', 'valor' => $i18n['debito']),
            array('id' => 'credito', 'valor' => $i18n['credito']),
            array('id' => 'cheque', 'valor' => $i18n['cheque']),
            array('id' => 'ticket', 'valor' => $i18n['ticket']),
            array('id' => 'transferencia', 'valor' => $i18n['transferencia']),
            array('id' => 'otros_electronicos', 'valor' => $i18n['otros_electronicos']),
            array('id' => 'otra', 'valor' => $i18n['otra'])
        ), false, 'onchange="revisarAgregarPago()"');

        if ($venta['tipofacturacion'] == 'electronico' && $venta['estadocae'] != 'sin_especificar') {
            texto('texto', $i18n[140], ucfirst($venta['estadocae']), 'auto', false, 'alerta');
            if ($venta['obscae'])
                texto('texto', $i18n[72], $venta['obscae']);
        }

        // Situación
        if ($venta['tienesituacion']) {
            $situaciones = array(
                array('id' => 'sin_especificar', 'valor' => $i18n['sin_especificar']),
                array('id' => 'pendiente', 'valor' => $i18n['pendiente']),
                array('id' => 'aprobado', 'valor' => $i18n['aprobado']),
                array('id' => 'rechazado', 'valor' => $i18n['rechazado'])
            );
            selector_array('situacion', $i18n[155], $venta['situacion'], 'auto', $situaciones);
        }
    }
    contenido_fin();

    // Cliente
    if ($cliente['idcliente'] == 1) {
        // Si es Consumidor Final dejo cargar dni y nombre específico
        contenido_inicio($i18n[35], '50');
        {
            if ($venta['estado'] == 'cerrado')
                texto('texto', $i18n[39], $cliente['nombre'], 'auto', false);
            else
                texto('texto', $i18n[39], $cliente['nombre'], 'auto', false, 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_clientes', 'title' => $i18n[316]));
            texto('texto', $i18n[63], $venta['tipoiva']);
            bloque_inicio('tipodoc');
                texto('texto', $i18n[967], tipoDocs($venta['tipodoc']));
            bloque_fin();
            bloque_inicio('ventas_dni');
                texto('texto', $i18n[242],
                    ($venta['dni'] ? $venta['dni'] : $i18n_funciones[24]));
            bloque_fin();
            bloque_inicio('ventas_razonsocial');
                texto('texto', $i18n[39],
                    ($venta['razonsocial'] ? $venta['razonsocial'] : $i18n_funciones[24]));
            bloque_fin();
            entrada('hidden', 'idlocalidad', false, $venta['idlocalidad']);
            bloque_inicio('ventas_localidad');
                texto('texto', $i18n[145], $venta['localidad']);
            bloque_fin();
            bloque_inicio('ventas_dni');
                enlaces(false, array(
                    array('tipo' => 'modal', 'modulo' => 'ventas',
                        'url' => 'ventas_dni', 'id' => $id, 'valor' => $i18n[243])
                    ));
            bloque_fin();
        }
        contenido_fin();

    } else {
        // No es Consumidor final, solo muestro los datos
        contenido_inicio($i18n[35], '50');
        {
            if ($venta['estado'] == 'cerrado')
                texto('texto', $i18n[39], $cliente['nombre'], 'auto', false);
            else
                texto('texto', $i18n[39], $cliente['nombre'], 'auto', false, 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_clientes'));
            texto('texto', $i18n[36], $cliente['idcliente'], false, false, false, false, 'id="idcliente"');
            texto('texto', $i18n[43], $venta['domicilio']);
            entrada('hidden', 'idlocalidad', false, $cliente['idlocalidad']);
            texto('texto', $i18n[145], ($venta['idrelacion'] ? $cliente['localidad'] : $venta['localidad']));
            texto('texto', $i18n[63], $venta['tipoiva']);
            if ($venta['cuit']) {
                texto('texto', $i18n[37], $venta['razonsocial']);
                texto('texto', $i18n[38], $venta['cuit']);
            } else if ($venta['dni']) {
                texto('texto', $i18n[37], $venta['razonsocial']);
                texto('texto', $i18n[967], tipoDocs($venta['tipodoc']));
                texto('texto', $i18n[242], $venta['dni']);
            }
        }
        contenido_fin();
    }

    salto_linea();

    // Datos adicionales
    contenido_inicio($i18n[73], '50', true);
    {
        if ($_SESSION['perfil_ventas_mod_todos']) {
            selector('idusuario', $i18n[392], $venta['idusuario'], 'auto', 'usuarios', 'nombre', false, true, true);

        } else {
            texto('texto', $i18n[392], $venta['usuario']);
        }

        // Vencimientos
        entrada('fecha', 'vencimiento1', $i18n[85], $venta['vencimiento1'], '50');
        bloque_inicio('div_vencimiento2', ($venta['vencimiento1'] != '0000-00-00' ? '' : 'style="display: none;"'));
        {
            entrada('fecha', 'vencimiento2', $i18n[86], $venta['vencimiento2'], '50');
            marcas('', '100', array(array('titulo' => $i18n[30], 'nombre' => 'recordatorios', 'valor' => $datos['recordatorios'], 'ayuda_puntual' => $i18n[54], 'opciones' => ($venta['estado'] != 'abierto' ? 'disabled="disabled"' : ''))));
        }
        bloque_fin();

        if ($venta['tipofacturacion'] == 'electronico') {
            $conceptos = array(
                array('id' => '1', 'valor' => $i18n[216]),
                array('id' => '2', 'valor' => $i18n[217]),
                array('id' => '3', 'valor' => $i18n[218]),
                );
            selector_array('concepto', $i18n[219], $venta['concepto'], 'auto', $conceptos);
            bloque_inicio('periodo_facturado', ($venta['concepto'] > 1 ? '' : 'style="display: none;"'));
            {
                entrada('fecha', 'fechainicio', $i18n[239], $venta['fechainicio'], '50');
                entrada('fecha', 'fechafin', $i18n[240], $venta['fechafin'], '50');
            }
            bloque_fin();
        }

        if ($venta['idrelacion']) {
            switch ($venta['tiporelacion']) {
            case 'servicio':
                $servicio = $venta['tiporelacion'] == 'servicio' && $venta['idrelacion']
                    ? array_sql(consulta_sql(
                        "SELECT idservicio, titulo
                        FROM servicios
                        where idservicio = ".$venta['idrelacion']))
                    : null;
                texto('texto', $i18n[47], $servicio['titulo'].' (N° '.$venta['idrelacion'].')');
                break;

            case 'abono':
                texto('texto', $i18n[48], $venta['idrelacion']);
                break;
            }
        }

        if ($_SESSION['modulo_ML'] && $venta['ML_order_id'] && contar_sql(consulta_sql("SELECT ML_estado FROM tienda WHERE ML_estado = '1' LIMIT 1"))) {
            $resultado_sql = consulta_sql("SELECT ML_order_id FROM ventas_ml WHERE idventa = '".$id."'");

            $temp_enlaces = array();
            while ($temp_array = array_sql($resultado_sql)) {
                $temp_enlaces[] = array('valor' => $temp_array['ML_order_id'], 'url' => 'https://myaccount.mercadolibre.com.ar/sales/vop?orderId='.$temp_array['ML_order_id']);
            }
            enlaces($i18n[166], $temp_enlaces);
        }

    }
    contenido_fin();

    // Moneda
    contenido_inicio($i18n[390], '50', true);
    {
        entrada('hidden', 'idmoneda_origen', false, $venta['idmoneda']);
        if ($_SESSION['modulo_multimoneda']) {
            if ($venta['estado'] == 'abierto') {
                selector('idmoneda', $i18n[390], $venta['idmoneda'], 'auto', 'monedas', 'idmoneda', true, false, false, false, 'onchange="guardar_moneda('."'$modulo', ".$id.', $(this).val())"');
                if ($venta['idmoneda'] != 1)
                    texto('italica', '', $i18n_funciones[331].' $ '.cotizacion(1, $venta['idmoneda'], 1), 'auto', false, false, false, ['string' => 'id="cotizacion_actual"']);

            } else {
                texto('titulo', $i18n[390], $venta['moneda'].' ('.$venta['simbolo'].')', 'auto');
                entrada('hidden', 'idmoneda', '', $venta['idmoneda']);
            }
        } else {
            texto('titulo', $i18n[390], $i18n[391], 'auto');
            entrada('hidden', 'idmoneda', '', 1);
        }
    }
    contenido_fin();

    salto_linea();

    // Movimientos generados
    contenido_inicio($venta['estado'] == 'abierto' ? $i18n[244] : $i18n[220], '50', true, false, false, 'id="movimientos_generados"');
    {
        $venta['esfiscal'] = $venta['tipofacturacion'] == 'interno' ? false : true;
        comprobantes_movimientos($venta,
            $_SESSION['perfil_configuraciones_empresa'] && !$_SESSION['sistema_gratis']); // Permiso para modificar
    }
    contenido_fin();

    // Lista de precios y depósitos
    contenido_inicio($i18n[302], '50', true, false);
    {
        $listas = array();
        $listas_sql = consulta_sql(
            "SELECT idlista, nombre FROM listas
            WHERE idmoneda = '{$venta['idmoneda']}'
            ORDER BY idlista ASC");
        while ($lista = array_sql($listas_sql)) {
            $listas[] = array(
                'id' => $lista['idlista'],
                'valor' => $lista['nombre'],
            );
        }

        if ($_SESSION['perfil_ventas_mod_precios']  && $venta['estado'] == 'abierto')
            selector_array('idlista', $i18n[303], $venta['idlista'], '100', $listas, $i18n[306], 'onchange="guardar_sucursal('."'idlista', '$modulo', ".$id.', $(this).val())"');
        elseif (!$_SESSION['perfil_ventas_mod_precios'] || $venta['estado'] == 'cerrado')
            texto('texto', $i18n[303], $venta['lista']);

        entrada('hidden', 'idlista_origen', false, $venta['idlista']);
        entrada('hidden', 'iddeposito_origen', false, $venta['iddeposito']);
        if ($_SESSION['perfil_ventas_mod_stock'] && $venta['estado'] == 'abierto')
            selector('iddeposito', $i18n[304], $venta['iddeposito'], '100', 'depositos', 'iddeposito', false, '', false, $i18n[307], 'onchange="guardar_sucursal('."'iddeposito', '$modulo', ".$id.', $(this).val())"');
        elseif (!$_SESSION['perfil_ventas_mod_stock'] || $venta['estado'] == 'cerrado')
            texto('texto', $i18n[305], $venta['deposito']);

    }
    contenido_fin();

    extras();

    // Seleccionador de productos
    contenido_inicio($i18n[53], '100', false, false, false, ($_SESSION['mobile'] ? 'style="overflow-x: scroll;"' : ''));
    {
        $discrimina = $venta['discrimina'];
        if ($venta['estadocae'] != 'sin_especificar') {
            comprobantes_mostrar_titulo_productosxcomprobantes($discrimina);

            $resultado_sql = consulta_sql(
                "SELECT productosxventas.*,
                    tablas_unidades.nombre AS unidad,
                    tablas_ivas.nombre AS iva,
                    monedas.simbolo
                FROM productosxventas
                    LEFT JOIN tablas_unidades ON productosxventas.idunidad = tablas_unidades.idunidad
                    LEFT JOIN tablas_ivas ON productosxventas.idiva = tablas_ivas.idiva
                    LEFT JOIN productos ON productosxventas.idproducto = productos.idproducto
                    LEFT JOIN ventas ON productosxventas.idventa = ventas.idventa
                    LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda
                WHERE productosxventas.idventa = '".$id."'
                ORDER BY idproductoxventa");

            if (contar_sql($resultado_sql)) {
                while ($productoxventa = array_sql($resultado_sql)) {
                    comprobantes_ver_productoxcomprobante($productoxventa, $discrimina);
                }

            } else {
                linea_inicio();
                {
                    texto('texto', false, $i18n[192]);
                }
                linea_fin();
            }

        } else {
            seleccionador_productos('ventas', $venta['discrimina'], $venta['idlista'], $venta['iddeposito']);
        }
    }
    contenido_fin();

    if ($venta['discrimina'] == 'R') {
        // Observaciones
        contenido_inicio($i18n[46], '100');
        {
            area('observacion', false, $venta['observacion']);
        }
        contenido_fin();
    }
    //Condición de venta y totales si no es remito
    else {
        // Observaciones
        contenido_inicio($i18n[46], '66');
        {
            area('observacion', false, $venta['observacion']);
        }
        contenido_fin();

        // Totales
        contenido_inicio(false, '33', false, false, false, ['id' => "totales", 'class' => "desglose"]);
        {
            if ($venta['estadocae'] == 'sin_especificar') {
                $enlaces = array(
                    array('tipo' => 'modal', 'modulo' => $modulo, 'url' => 'comprobantes_mod_descuento', 'id' => $id, 'valor' => $i18n[196]),
                    array('tipo' => 'enlace', 'modulo' => $modulo,
                        'url' => 'ventas.php?a=mod&id='.$id."&act_precios=1",
                        'id' => $id, 'valor' => $i18n[212],
                        'opciones' => 'onclick="return confirma('."'".$i18n[215]."'".')"'),
                );
                enlaces(false, $enlaces);
            }

            if ($venta['discrimina'] == 'A' || $venta['discrimina'] == 'B') {
                $enlaces = array();
                if (array_sql(consulta_sql("SELECT idtributo FROM categorias_tributos")) && $venta['estadocae'] == 'sin_especificar')
                    $enlaces[] = array('tipo' => 'modal', 'modulo' => $modulo, 'url' => 'tributos', 'id' => $id, 'valor' => $i18n[249]);
                $enlaces[] = array('tipo' => 'flotante', 'modulo' => $modulo, 'url' => 'comprobantes_ver_totales', 'id' => $id, 'valor' => $i18n[195]);
                enlaces(false, $enlaces);
            }

            comprobantes_mostrar_totales($venta, $venta['discrimina']);
        }
        contenido_fin();

        salto_linea();

        // Recibo de pago
        if (!$_SESSION['sistema_gratis']
            && $venta['estado'] == 'abierto'
            && $_SESSION['perfil_cajas_ver']
            && $_SESSION['perfil_ventaspagos_alta']
            && !$venta['operacioninversa']) {

            contenido_inicio($i18n[111], '100', false, false, false, 'id="altapago"');
            {
                marcas('', 'auto', array(array('nombre' => 'agregarpago', 'titulo' => $i18n[113], 'valor' => $datos['agregarpago'], 'opciones' => 'id="agregarpago" onclick="revisarAgregarPago()"', 'ayuda_puntual' => $i18n[147])));
                bloque_inicio('detallepago');
                {
                    // Tiene permiso para agregar recibos de pagos
                    if ($_SESSION['perfil_ventaspagos_alta'] && $_SESSION['perfil_cajas_ver']) {

                        //elimino retenciones y cheques de las cajas
                        if ($_SESSION['perfil_idperfil'] == '1')
                            $cajas_sql = consulta_sql("SELECT * FROM categorias_cajas
                                INNER JOIN cajas ON categorias_cajas.idcaja = cajas.idcaja
                                WHERE estado = '1'
                                AND cajas.fechacierre = '0000-00-00 00:00:00'
                            ");
                        else
                            $cajas_sql = consulta_sql(
                                "SELECT * FROM categorias_cajas
                                    LEFT JOIN perfilesxcajas ON categorias_cajas.idtipocaja = perfilesxcajas.idtipocaja
                                    INNER JOIN cajas ON categorias_cajas.idcaja = cajas.idcaja
                                WHERE estado = '1'
                                    AND cajas.fechacierre = '0000-00-00 00:00:00'
                                    AND (categorias_cajas.compartida = '1' OR (idperfil = '{$_SESSION['perfil_idperfil']}' AND alta='1'))
                                GROUP BY categorias_cajas.idtipocaja");
                        $cajas = array();
                        $formasdepago_disponibles = array();
                        while ($array_tipo_caja = array_sql($cajas_sql)) {
                            $cajas[] = array(
                                'id' => $array_tipo_caja['idcaja'],
                                'valor' => $array_tipo_caja['nombre'],
                                'datasets' => array(
                                    'data-tipoformapago' => $array_tipo_caja['tipo'],
                                    )
                            );
                            if (!in_array($array_tipo_caja['tipo'], $formasdepago_disponibles))
                                $formasdepago_disponibles[] = "'".$array_tipo_caja['tipo']."'";
                        }

                        $formasdepago = array();
                        if (count($formasdepago_disponibles)) {
                            $formasdepago_sql = consulta_sql("SELECT *
                                FROM tablas_formasdepago
                                WHERE tipo IN (".implode(',', $formasdepago_disponibles).")");
                            while ($formadepago = array_sql($formasdepago_sql)) {
                                $formasdepago[] = array(
                                    'id' => $formadepago['idformapago'],
                                    'valor' => $formadepago['nombre'],
                                    'datasets' => array(
                                        'data-tipoformapago' => $formadepago['tipo'])
                                    );
                            }
                            ?>
                                <script type="text/javascript" charset="utf-8">
                                    $(function() {
                                        $("select[name='idformapago'] option[data-tipoformapago='cheque']").remove();
                                        $("select[name='idformapago'] option[data-tipoformapago='retencion']").remove();
                                        $("select[name='idcaja'] option[data-tipoformapago='cheque']").remove();
                                        $("select[name='idcaja'] option[data-tipoformapago='retencion']").remove();
                                    });
                                </script>
                            <?php

                            $ayuda_puntual = contar_sql($cajas_sql) ? $i18n[24] : $i18n[105];
                            selector_array('idformapago', $i18n[81], $datos['idformapago'], '18', $formasdepago, false, 'onchange="seleccionar_tipo_caja()"');
                            selector_array('idcaja', $i18n[23], $datos['idcaja'], '33', $cajas, $ayuda_puntual);
                            selector_familiar('idconcepto', $i18n[25], $datos['idconcepto'], '33', 'categorias_conceptos', true, true, true);

                        } else {
                            texto('italica', false, $i18n[121], 'auto', false, 'alerta');
                        }

                    } else {
                        $texto = $i18n[116] // El cliente no tiene CC
                            .(!$_SESSION['perfil_ventaspagos_alta']
                            ? $i18n[109] // No tiene recibos
                            : $i18n[110]); // No tiene cajas
                        texto('italica', false, $texto, 'auto', false, 'alerta');
                    }

              }
                bloque_fin();
            }
            contenido_fin();
        }
    }
    $temp_botones = array();
    if ($venta['estado'] == 'abierto') {
        $temp_botones[] = array('valor' => $i18n[16]);
        $temp_botones[] = array('valor' => $i18n[14]);
    } else {
        $temp_botones[] = array('valor' => $i18n_funciones[22]);
    }
    if ($venta['estado'] != 'anulado')
        $temp_botones[] = array('valor' => $i18n[18]);
    $temp_ayuda_puntual = $i18n[126].$venta['nombre'].'<br><ul>';
    if ($venta['muevestock'])
        $temp_ayuda_puntual.= $i18n[128];
    if ($venta['muevesaldo'])
        $temp_ayuda_puntual.= $i18n[129];
    if ($venta['tipofacturacion'] != 'interno')
        $temp_ayuda_puntual.= $i18n[130];
    $temp_ayuda_puntual.= '</ul>';
    botones($temp_botones, $temp_ayuda_puntual);

}
ventana_fin();

?>
<script charset="utf-8">
    // Necesito la tabla condiciones en JS
    var tablas_condiciones = <?php echo tabla_json('tablas_condiciones'); ?>;

    if (typeof window.timeoutAFIPDemora !== "undefined")
        clearTimeout(window.timeoutAFIPDemora);

    function validacion_ventas_mod(boton)
    {
<?php

// TODO: Pasarlo a comprobantes.js/
if ($cliente['idcliente'] == 1
    && $venta['tipofacturacion'] != 'interno'
    && $venta['discrimina'] == 'B') {

    echo '
        var total = Number($("#total").text().replace("$ ", ""));
        var dni = $("#ventas_dni .campo_texto").text();
        var razonsocial = $("#ventas_razonsocial .campo_texto").text();
        if (total >= '.TOPE_AFIP_DNI.' && dni == "Sin especificar" && razonsocial == "Sin especificar") {
            modal("ventas", "ventas_dni", "'.$id.'");
            return false;
        }
';
}

if ($venta['estado'] == 'abierto') {
    //Cerrar
    if ($venta['tipofacturacion'] == 'electronico') {
        echo '
        var input = $("input[name=fecha]").val().slice(0,10).split("-").reverse().join("-");
        var fecha = new Date(input + " 00:00");

        var hoy = new Date();
        hoy.setHours(0);
        hoy.setMinutes(0);
        hoy.setSeconds(0);

        if (boton == "'.$i18n[16].'" && fecha > hoy) {
            return confirma("'.$i18n[209].'");
        }

        if (boton == "'.$i18n[16].'") {
            // settimeout para que se ejecute despues de 10 segundos
            window.timeoutAFIPDemora = setTimeout(function() {
                alerta("'.$i18n[388].'", "informacion")
            }, 10000);
        }';

    }

    //Aceptar
    $temp_confirma = $i18n[22].'\n';
    if ($venta['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[132];
    if ($venta['muevestock'])
        $temp_confirma.= '\n'.$i18n[133];
    echo '
        if (boton == "'.$i18n[14].'") {
            return confirma("'.$temp_confirma.'");
        }';

    //Anular
    $temp_confirma = $i18n[20].'\n';
    if ($venta['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[132];
    if ($venta['muevestock'])
        $temp_confirma.= '\n'.$i18n[133];
    echo '
        if (boton == "'.$i18n[18].'") {
            return confirma("'.$temp_confirma.'");
        }';
} elseif ($venta['estado'] == 'cerrado') {
    //Aceptar
    // qué sucede con el mensaje de "NO se ajustarán los recibos de pagos relacionados"?
    /*$temp_confirma = $i18n[21].'\n';
    if ($venta['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[134].'\n'.$i18n[136];
    if ($venta['muevestock'])
        $temp_confirma.= '\n'.$i18n[135];
    echo '
        if (boton == "'.$i18n_funciones[22].'") {
            return confirma("'.$temp_confirma.'");
        }';
    */
    //Anular
    $temp_confirma = $i18n[20].'\n';
    if ($venta['muevesaldo'])
        $temp_confirma.= '\n'.$i18n[134].'\n'.$i18n[136];
    if ($venta['muevestock'])
        $temp_confirma.= '\n'.$i18n[131];
    echo '
        if (boton == "'.$i18n[18].'") {
            return confirma("'.$temp_confirma.'");
        }';
}
?>

        return true;
    }
    $(function() {
        revisarAgregarPago();
    });
</script>
