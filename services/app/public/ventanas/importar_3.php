<?php
// Preparo algunas variables
$random = recibir_variable("random");
$campo_clave = recibir_variable("campo_clave");
$separador = recibir_variable("separador");
$csv_fuente = fopen(herramientas_nombre_archivo($modulo.'_fuente', $random), 'r');
$csv_procesado = fopen(herramientas_nombre_archivo($modulo.'_procesado', $random), 'w');
$json_opciones = fopen(herramientas_nombre_archivo($modulo.'_opciones', $random,".json"), 'w');
$columnas_fuente = array(); // Para agregar que campo tiene cada columna
$columnas_fuente_total = count($columnas);
$columnas_procesado = array();
$prefijo_select = 'columna_'; // Prefijo de los selects del paso importar_2
$tablas_relacionadas = array(); // Para cargar las tablas relacionadas
$valores_posibles = array(); // Para indicar si el campo tiene valores aceptados
$array_precios_stock = ['stockactual', 'stockminimo', 'stockideal', 'precio', 'preciofinal', 'utilidad'];
$tabla_preciostock = array();
$tabla_preciostock_final = array();
$tablas_categorias_cantidades_array = array();
define('CANTIDAD_MAXIMA_RUBROS', 800);

// Armo el array con el nº de columna en el que está cada campo
// $columnas viene de la configuración de la importación según el módulo
$opciones_json = array("campo_clave"=>"", "opciones"=>array(), "campos"=>array());
$opciones_json["campo_clave"] = $campo_clave ;

// Recibo datos desde post
$recibir = array();
$random = recibir_variable('random');
for ($i = 0; $i <= $columnas_fuente_total; $i++) {
    $recibir[] = $prefijo_select.$i;
}

//cargo los valores de las columnas
$datos = recibir_matriz($recibir);

for ($i = 0; $i <= $columnas_fuente_total; $i++) {
    $opciones_json["campos"][$prefijo_select.$i] = $datos[$prefijo_select.$i];
}

//verifico campos duplicados
foreach ($datos as $campo => $value) {
    if ($value !=""){
        $tmp = array($value);
        $result = array_intersect($datos, $tmp);
        if (count($result)> 1){
            mensajes_alta($i18n_funciones[119].$campo." - ".$value.'.'.$i18n_funciones[120].count($result));
            ir_ahora($modulo.'.php?a=importar');
        }
    }
}

foreach ($opciones as $key => $value) {
    $recibir[] = $value['nombre'];
}

$datos = recibir_matriz($recibir);

$sep_decimal = '.';
foreach ($opciones as $opcion) {
    if ($opcion["tipo"] == "sep_decimal"){
        $sep_decimal = $datos["sep_decimal"];
        $opciones_json["opciones"]["sep_decimal"] = $datos["sep_decimal"] ;
    } else {
        $opciones_json["opciones"][$opcion["nombre"]] = $datos[$opcion["nombre"]] ;
    }
}

//armo el array precios y stock y los limito x idlista y iddeposito
if ($modulo == 'productos') $datos = opciones_sucursales($opciones_json['opciones'], $datos);

$clave_primaria = true;
if ($campo_clave != $idmodulo){
    if ($modulo != 'productos'){
        $clave_primaria = false;
    } elseif ($campo_clave != 'codigo' && $campo_clave != 'codigoproveedor'){
        $clave_primaria = false;
    }
}

$monedas = obtener_monedas_importacion();
$extras = array();
$resultado_sql = consulta_sql("SELECT * FROM extrasxmodulos WHERE modulo = '$modulo'");
while ($temp_array = array_sql($resultado_sql)) {
    $extras[] = array('idextraxmodulo' => array('sql' => 'extrasxmodulos.nombre AS datosextra', 'nombre'=>$temp_array['nombre']));
}

if ($extras){
    foreach ($extras as $key => $extra) {
        $columnas['idextraxmodulo'][$key] = array('sql' => "extrasxmodulos.nombre AS datosextra", 'nombre' => $extra["idextraxmodulo"]['nombre']);
    }
}

foreach ($columnas as $campo => $value) {
    if (array_search($campo, $datos) !== false) {
        // El campo está seleccionado para una columna en el paso importar_2
        //chequeo si se selecciono otro campo como clave que no es pk omito el pk en el archivo
        if (!(!$clave_primaria && $campo == $idmodulo)){
            $columnas_fuente[$campo] = str_replace($prefijo_select, '', array_search($campo, $datos));

            if ($campo == 'idextraxmodulo'){ //datos extras
                foreach ($extras as $key => $extra) {
                    $columnas_procesado[] = $extra["idextraxmodulo"]['nombre'];
                }
            } else {
                $columnas_procesado[$columnas_fuente[$campo]] = $campo;
            }
        }
    }
}

fwrite($json_opciones, json_encode($opciones_json));
fclose($json_opciones);

// Preparo las tablas relacionadas de los campos que se van a migrar y tambien el set de valores posibles para cada campo
foreach ($procesos as $campo => $proceso) {
    if (($proceso['tipo'] == 'categoria_relacionada' || $proceso['tipo'] == 'tabla_relacionada' || $proceso['tipo'] == 'sucursales')
        && in_array($campo, $columnas_procesado)) {
        $tabla_relacionada = array();

        if (array_search($campo, $array_precios_stock) === false){
            $resultado_sql = consulta_sql("SELECT $campo, nombre FROM ".$proceso['tabla']);
        }

        while ($temp_array = array_sql($resultado_sql)) {
            if (array_search($campo, $array_precios_stock) === false){
                $tabla_relacionada[$temp_array[$campo]] = $temp_array['nombre'];
            }
        }
        $tablas_relacionadas[$campo] = $tabla_relacionada;

    } else {
        $tablas_relacionadas[$campo] = false;
    }

    if ($proceso['tipo'] == 'valores_aceptados'
        && in_array($campo, $columnas_procesado)) {
        $valores_posibles[$campo] = true;
    } else {
        $valores_posibles[$campo] = false;
    }
}

// Excluyo la primera línea del archivo fuente
$linea = fgetcsv($csv_fuente, 0, $separador);

//Ejecuto la función para procesar las opciones y ver si tengo qu eliminar o no algunas columnas (esta función es particular de la entidad que estamos importando)
if ($funcion_procesa_opciones != ""){
    $columnas_procesado = $funcion_procesa_opciones($columnas_procesado, $columnas_procesado, $datos, true);
}

//Si no tengo la columna idproducto, y estoy agregando productos, necesito la columna para asignar idproducto
if ($modulo == 'productos' && !in_array('idproducto', $columnas_procesado) && $campo_clave == 'idproducto'){
    array_unshift($columnas_procesado, 'idproducto');
    $columnas_fuente_total++;
    $agregar_idproducto = true;
}

// Guardo la primera línea del archivo procesado
fputcsv($csv_procesado, $columnas_procesado, SEPARADOR_CSV);

// While linea del archivo fuente -> cargo el fuente y lo proceso para guardar en _procesado.csv
while ($linea = fgetcsv($csv_fuente, 0, $separador)) {
    if (count($linea) > 1){
        $linea_procesada = array();
        foreach ($columnas_procesado as $key => $campo) {
            if ($campo == $campo_clave && !isset($key_id)) //obtengo el id del campo clave, para matchear junto a nombre, para evitar líneas en blanco
                $key_id = $key;
            if ($campo == 'nombre' && !isset($key_nombre))
                $key_nombre = $key;

            //if($modulo == 'productos' && !in_array('idproducto', $columnas_procesado) && array_search($campo, $array_precios_stock) !== false){
            if (($agregar_idproducto) && (array_search($campo, $array_precios_stock) !== false || $campo == 'idproducto')){
                if ($campo == 'idproducto'){
                    if ($ultimo_id){
                        $ultimo_id++;
                    } else {
                        $ultimo_id = (campo_sql(consulta_sql("SELECT idproducto FROM ".$modulo." ORDER BY idproducto DESC LIMIT 1"))) + 10;
                    }
                }
                $linea[$columnas_fuente['idproducto']] = $ultimo_id;
            }

            if (array_key_exists($campo, $procesos)) {
                if ($tablas_relacionadas[$campo]) $temp_campo = $tablas_relacionadas[$campo]; else $temp_campo = $campo;
                // Si tengo un proceso con conversiones especiales lo ejecuto
                $linea_procesada[$key] = importar_procesar_campo(
                    $linea[$columnas_fuente[$campo]],
                    $procesos[$campo],
                    $temp_campo, //no le gustó el otro approach
                    $sep_decimal
                );

                //Validador para rubros
                if ($campo == 'idrubro' && !in_array($linea[$columnas_fuente[$campo]], $tablas_categorias_cantidades_array[$campo])) {
                    $tablas_categorias_cantidades_array[$campo][] = $linea[$columnas_fuente[$campo]];
                    $tablas_categorias_cantidades_array[$campo]['cantidad']++;
                }
                if ($campo == 'idrubro' && $tablas_categorias_cantidades_array[$campo]['cantidad'] > CANTIDAD_MAXIMA_RUBROS) {
                    mensajes_alta($i18n_funciones[302] . CANTIDAD_MAXIMA_RUBROS);
                    ir_ahora($modulo.'.php?a=importar');
                }
                if ($campo == 'idmoneda') {
                    $moneda_key = strtolower($linea[$columnas_fuente[$campo]]);
                    $moneda_id = $monedas[$moneda_key] ? $monedas[$moneda_key] : 1;
                    $linea_procesada[$key] = $moneda_id;
                }

                if ($procesos[$campo]["tipo"] == 'categoria_relacionada'){
                    reset($tablas_relacionadas);
                    //Parche para tablas relacionadas
                    foreach ($tablas_relacionadas as $temp_key_tabla => $temp_tabla) {
                        if ($temp_key_tabla == $campo &&
                            !$tablas_relacionadas[$campo][$linea_procesada[$key]]) {
                            $clave = array_search($campo, $columnas_procesado); //Obtengo el maldito key!
                            $tablas_relacionadas[$campo][$linea_procesada[$key]] = $linea[$clave];
                        }
                    }

                }

                if ($_SESSION['configuracion_discrimina'] && $campo == 'idiva') {
                    if ($linea_procesada[$key] <= 0 || $linea_procesada[$key] > 9) {
                        mensajes_alta($i18n_funciones[272]);
                        ir_ahora($modulo.'.php?a=importar');
                    }
                }
            } else {
                //Verifico que id no tenga otra cosa que no sea enteros
                if (($campo == 'idproducto' || $campo == 'idcliente' || $campo == 'idproveedor') && ($linea[$columnas_fuente[$campo]] && !ctype_digit($linea[$columnas_fuente[$campo]]))){
                    mensajes_alta($i18n_funciones[164].($modulo == 'proveedores' ? trim($modulo, 'es') : trim($modulo, 's')).$i18n_funciones[165]);
                    ir_ahora($modulo.'.php?a=importar');

                } elseif ($campo == 'codigo' && !$linea[$columnas_fuente[$campo]]) {
                    $proximoid = campo_sql(consulta_sql("SELECT AUTO_INCREMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'productos'"), 0);
                    $linea[$columnas_fuente[$campo]] = $proximoid < 10000
                        ? completar_numero($proximoid, 5)
                        : $proximoid;
                }

                //Si viene sin id, invento un nuevo id (+10 ids x las dudas)
                if ((!$linea[$columnas_fuente[$campo]]) && ($campo == 'idproducto' || $campo == 'idcliente' || $campo == 'idproveedor')){

                    //parche para que traiga idproducto si matchea con codigo o codigoproducto y tengo el campo idproducto vacío (sino, me crea uno)
                    $temp_idproducto = campo_sql(consulta_sql("SELECT * FROM ".$modulo." WHERE TRIM(".$campo_clave.") = TRIM('".escape_sql($linea[$columnas_fuente[$campo_clave]])."')"));
                    if ($temp_idproducto){
                        $linea[$columnas_fuente[$campo]] = $temp_idproducto;
                    } else {
                        if ($ultimo_id){
                            $ultimo_id++;
                        } else {
                            $ultimo_id = (campo_sql(consulta_sql("SELECT ".$campo." FROM ".$modulo." ORDER BY ".$campo." DESC LIMIT 1"))) + 10;
                        }
                        $linea[$columnas_fuente[$campo]] = $ultimo_id;
                    }
                }
                // Agrego la celda tal cual vino espaciado
                $linea_procesada[$key] = escape_sql($linea[$columnas_fuente[$campo]]);
                if (!$linea_procesada[$key]){
                    if ($linea_procesada[$key] === NULL){ //acá procesamos los datos extras
                        $linea_procesada[$key] = $linea[$key];
                    } else {
                        $linea_procesada[$key] = ''; //era acá la cosa!!!
                    }
                }
            }

            //Reemplazo coma x punto en decimales:
            if (1 === preg_match('~[0-9]~', $linea_procesada[$key])){
                $linea_procesada[$key] = str_replace(',', '.', $linea_procesada[$key]);
            }
            $linea_procesada[$key] = str_replace("\\", "", $linea_procesada[$key]); //el temita del código con ceros adelante
            if (array_search($campo, $array_precios_stock) !== false){
                //Cambio campo clave
                //$tabla_preciostock['idproducto'] = $linea[$columnas_fuente['idproducto']];
                $tabla_preciostock[$campo_clave] = $linea[$columnas_fuente[$campo_clave]];
                $tabla_preciostock[$campo] = $linea[$columnas_fuente[$campo]];
                $tabla_preciostock_final[$campo][] = $tabla_preciostock;
            }
        }
        //ejecuto la funcion para procesar las opciones (esta funcion es particular de la entidad que estamos importando)
        if ($funcion_procesa_opciones != ""){
            $linea_procesada = $funcion_procesa_opciones($columnas_procesado, $linea_procesada, $datos, false);
        }
        // Guardo la fila en el csv_procesado
        if ((!$linea_procesada[$key_id] && !$linea_procesada[$key_nombre]) || ($campo_clave == 'codigoproveedor' && !$linea[$columnas_fuente['codigoproveedor']])){
            //busco que haya campo clave y nombre para evitar líneas en blanco
            //no grabo nada --- ver cómo invertir este if
        } else {
            fputcsv($csv_procesado, $linea_procesada, SEPARADOR_CSV);
        }
    }
}

// Cierro los punteros a los archivos
fclose($csv_fuente);
fclose($csv_procesado);

//Proceso las tablas relacionadas para la vista previa y la generacion del sql de nuevos registros en dichas tablas
foreach ($tablas_relacionadas as $campo => $tabla) {
    if ($tabla !== false){
        $csv_tabla_relacionada = fopen(herramientas_nombre_archivo($campo.'_tabla_procesada', $random), 'w');
        if (array_search($campo, $array_precios_stock) === false)
            fputcsv($csv_tabla_relacionada, array($campo, "nombre"), SEPARADOR_CSV);
        else
            fputcsv($csv_tabla_relacionada, array('id', $campo), SEPARADOR_CSV);

        foreach ($tabla as $id => $nombre) {
            if (array_search($campo, $array_precios_stock) === false){
                fputcsv($csv_tabla_relacionada, array($id, $nombre), SEPARADOR_CSV);
            }
        }

        if (array_search($campo, $array_precios_stock) !== false){
            foreach ($tabla_preciostock_final[$campo] as $key => $value) {
                if (!$value[$campo])
                    $value[$campo] = 0;
                //Vuelo idproducto y pongo campo_clave (obvio, hardcodeado andaba solo con idproducto)
                //fputcsv($csv_tabla_relacionada, array($value['idproducto'], $value[$campo]), SEPARADOR_CSV);
                fputcsv($csv_tabla_relacionada, array($value[$campo_clave], $value[$campo]), SEPARADOR_CSV);
            }
            fclose($csv_tabla_relacionada);
        }
    }
}

// Muestro la ventana con lista previa
ventana_inicio($i18n_funciones[73].' '.strtolower($i18n_funciones[$modulo]).' - '.$paso.'º paso');
{
    contenido_inicio($i18n_herramientas[17]);
    {
        entrada('hidden', 'random', 'random', $random);
        entrada('hidden', 'paso', 'paso', $paso);
        entrada('hidden', 'campo_clave', 'campo_clave', $campo_clave);
        entrada('hidden', 'separador', 'separador', $separador);
        if ($modulo == 'productos'){
            entrada('hidden', 'idlista', 'idlista', $opciones_json['opciones']['idlista']);
            entrada('hidden', 'iddeposito', 'iddeposito', $opciones_json['opciones']['iddeposito']);
        }

        // Div para mostar la vista previa (Puse el css ahi solo para visualizacion mia Andiaco debe hacer su magia)
        echo '<div id="div_vista_previa" style="overflow-y: hidden; overflow-x: scroll; width:100%"></div>';

        // Botones de anterior y siguiente como enlace, para cargar por ajax
        botones_ajax(array(
            array("titulo" => $i18n_funciones[96], "ventana" => "importar_obtener_registro", "id" => "0", "div_destino" => "div_vista_previa"),
            array("titulo" => $i18n_funciones[97], "ventana" => "importar_obtener_registro", "id" => "1", "div_destino" => "div_vista_previa")
        ));

        bajo_linea();
        texto('italica', false, $i18n_funciones[254], false, false, 'info');
    }
    contenido_fin();

    botones(array(
        array('tipo' => 'nueva', 'valor' => $i18n_funciones[26]),
        array('tipo' => 'nueva', 'valor' => $i18n_funciones[80], 'opciones' => 'id="continuar"'),
    ));
}
ventana_fin();

?>
<script type="text/javascript">
    $(function (){
        $( "#<?php echo str_replace(' ','_',$i18n_funciones[96])?>_boton_ajax" ).trigger( "click" );
    });

    $("#continuar").click(function() {
        if (!confirma("<?php echo $i18n_funciones[138] . $modulo . $i18n_funciones[139];?>"))
            return false;

        setTimeout(function () {
            document.location="<?php echo $_SESSION['servidor_url']; ?>";
        }, 5000);
    });
</script>


