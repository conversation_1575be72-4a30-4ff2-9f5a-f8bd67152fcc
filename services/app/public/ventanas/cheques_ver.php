<?php

$datos = array_sql(consulta_sql(
    "SELECT c.*, tb.nombre AS banco,
        CASE
            WHEN c.tipo = 'tercero' THEN vp.fecha
            ELSE cp.fecha
        END AS fechacarga,
        CASE
            WHEN c.tipo = 'tercero' THEN vp.total
            ELSE cp.total
        END AS total,
        vp.idventapago, vp.idnumeroventapago, cp.idcomprapago, cp.idnumerocomprapago,
        mo.simbolo, mo.nombre AS moneda,
        me.fecha AS fecharecordatorio, me.texto,
        cl.idcliente, cl.nombre AS cliente, cl.contacto, cl.telefonos, cl.mail, cl.domicilio,
        l.nombre AS localidad, tp.nombre AS provincia,
        p.idproveedor, p.nombre AS proveedor, p.contacto AS contacto_proveedor, p.telefonos AS telefonos_proveedor, p.mail AS mail_proveedor, p.domicilio AS domicilio_proveedor,
        lp.nombre AS localidad_proveedor, tp2.nombre AS provincia_proveedor,
        categorias_ventas.puntodeventa, categorias_ventas.letra,
        v.idventa, v.numero AS venta_numero, v.total AS venta_total, v.fecha AS venta_fecha,
        cc.nombre AS caja,
        u.nombre AS usuario,
        uc.nombre AS usuario_conciliacion,
        co.idcompra, co.numerocompleto AS compra_numero, co.total AS compra_total, co.fecha AS compra_fecha,
        mxc.idmovimientoxcaja, mxc.idconciliacion, mxc.fechaconciliacion, mxc.conciliacion, mxc.idcaja
    FROM cheques c
        LEFT JOIN tablas_bancos tb ON c.idbanco = tb.idbanco
        LEFT JOIN compraspagos cp ON c.idcheque = cp.idrelacion AND cp.tiporelacion = 'cheque'
        LEFT JOIN ventaspagos vp ON c.idcheque = vp.idrelacion AND vp.tiporelacion = 'cheque'
        LEFT JOIN movimientosxcajas mxc ON (vp.idventapago = mxc.idrelacion AND mxc.tiporelacion = 'clientepago') OR (cp.idcomprapago = mxc.idrelacion AND mxc.tiporelacion = 'proveedorpago')
        LEFT JOIN cajas ca ON mxc.idcaja = ca.idcaja
        LEFT JOIN categorias_cajas cc ON ca.idtipocaja = cc.idtipocaja
        LEFT JOIN ventas v ON vp.idventa = v.idventa
        LEFT JOIN compras co ON cp.idcompra = co.idcompra
        LEFT JOIN categorias_ventas ON categorias_ventas.idtipoventa = v.idtipoventa
        LEFT JOIN usuarios u ON (v.idusuario = u.idusuario) OR (co.idusuario = u.idusuario)
        LEFT JOIN usuarios uc ON mxc.idusuario = uc.idusuario
        LEFT JOIN monedas mo ON (c.tipo = 'tercero' AND vp.idmoneda = mo.idmoneda) OR (c.tipo = 'propio' AND cp.idmoneda = mo.idmoneda)
        LEFT JOIN mensajes me ON c.idmensaje = me.idmensaje
        LEFT JOIN clientes cl ON vp.idcliente = cl.idcliente
        LEFT JOIN proveedores p ON cp.idproveedor = p.idproveedor
        LEFT JOIN categorias_localidades l ON cl.idlocalidad = l.idlocalidad
        LEFT JOIN tablas_provincias tp ON l.idprovincia = tp.idprovincia
        LEFT JOIN categorias_localidades lp ON p.idlocalidad = lp.idlocalidad
        LEFT JOIN tablas_provincias tp2 ON lp.idprovincia = tp2.idprovincia
        WHERE c.idcheque = '$id'
    LIMIT 1"));

ventana_inicio($i18n[131].$datos['numero'], '100');
{
    // Datos del cheques
    contenido_inicio($i18n[132], '50');
    {
        texto('texto', $i18n[136], ($datos['tipo'] == 'propio' ? $i18n['137'] : $i18n['138']));
        texto('texto', $i18n[143], $datos['titular']);
        texto('fecha', $i18n[139], $datos['fechacobro']);
        texto('texto', $i18n[140], $datos['banco']);
        texto('moneda', $i18n[141], $datos['total'], false, false, false, false, false, $datos['simbolo']);
        texto('texto', $i18n[142], $datos['moneda']);
        texto('texto', $i18n[144], $datos['numero']);
        texto('fecha', $i18n[145], $datos['fecharecordatorio']);
        observacion($i18n[69], $datos['texto']);
    }
    contenido_fin();

    //Datos de la compra/conciliación
    contenido_inicio($i18n[135], '50');
    {
        if ($_SESSION['perfil_cajas_herramientas'] && !$datos['estado']) {
            entrada('hidden', 'idmovimientoxcaja', false, $datos['idmovimientoxcaja']);
            entrada('hidden', 'total', false, $datos['total']);
            entrada('hidden', 'idcaja_origen', false, $datos['idcaja']);
            enlaces($i18n['163'], array(
                array(
                    'tipo' => 'modal',
                    'url' => 'cajas_transferencia',
                    'valor' => $i18n['cheque_cobro_pago'],
                    'modulo' => 'cajas',
                    'imagen' => 'caja_cheque',
                    'id' => $datos['idcaja'],
                ))
            );
        } else {
            texto('fecha', $i18n[156], $datos['fechaconciliacion']);
            texto('texto', $i18n[157], $datos['caja']);
            texto('texto', $i18n[158], $datos['usuario_conciliacion']);
            texto('texto', $i18n[163], ($datos['tipo'] == 'propio' ? $i18n[162] : $i18n[161]));
        }
    }
    contenido_fin();

    salto_linea();

    // Datos de cliente
    contenido_inicio($i18n[133], '50');
    {
        if ($datos['idcliente']) {
            texto('texto', $i18n[27], $datos['cliente'], 'auto', 'clientes.php?a=ver&id='.$datos['idcliente']);
            texto('texto', $i18n[146], $datos['idcliente']);
            texto('texto', $i18n[149], $datos['contacto']);
            texto('mail', $i18n[150], $datos['mail']);
            texto('texto', $i18n[151], $datos['telefonos']);
            texto('texto', $i18n[152], $datos['domicilio']);
            texto('texto', $i18n[147], $datos['localidad']);
            texto('texto', $i18n[148], $datos['provincia']);
        } else {
            celda('texto', $i18n[166]);
        }
    }
    contenido_fin();

    // Datos de la venta
    contenido_inicio($i18n[153], '50');
    {
        if ($datos['idventa']) {
            texto('texto', $i18n[124], 'RP'.completar_numero($datos['idnumeroventapago'], 8), 'auto', 'ventas.php?a=verpago&id='.$datos['idventapago']);
            texto('texto', $i18n[154], $datos['idventa'] ? $datos['letra'].completar_numero($datos['puntodeventa'], 5).'-'.completar_numero($datos['venta_numero'], 8) : '', 'auto', 'ventas.php?a=ver&id='.$datos['idventa']);
            texto('moneda', $i18n[155], $datos['venta_total'], false, false, false, false, false, $datos['simbolo']);
            texto('fechayhora', $i18n[156], $datos['venta_fecha']);
            texto('texto', $i18n[157], $datos['idventa'] ? $datos['caja'] : '');
            texto('texto', $i18n[158], $datos['idventa'] ? $datos['usuario'] : '');
        } else {
            celda('texto', $i18n[167]);
        }
    }
    contenido_fin();

    salto_linea();

    // Datos del proveedor
    contenido_inicio($i18n[165], '50');
    {
        if ($datos['idproveedor']) {
            texto('texto', $i18n[27], $datos['proveedor'], 'auto', 'proveedores.php?a=ver&id='.$datos['idproveedor']);
            texto('texto', $i18n[170], $datos['idproveedor']);
            texto('texto', $i18n[149], $datos['contacto_proveedor']);
            texto('mail', $i18n[150], $datos['mail_proveedor']);
            texto('texto', $i18n[151], $datos['telefonos_proveedor']);
            texto('texto', $i18n[152], $datos['domicilio_proveedor']);
            texto('texto', $i18n[147], $datos['localidad_proveedor']);
            texto('texto', $i18n[148], $datos['provincia_proveedor']);
        } else {
            celda('texto', $i18n[168]);
        }
    }
    contenido_fin();

    //Datos de la compra
    contenido_inicio($i18n[134], '50');
    {
        if ($datos['idcompra']) {
            texto('texto', $i18n[125], 'OP'.completar_numero($datos['idnumerocomprapago'], 8), 'auto', 'compras.php?a=verpago&id='.$datos['idcomprapago']);
            texto('texto', $i18n[159], $datos['compra_numero'], 'auto', 'compras.php?a=ver&id='.$datos['idcompra']);
            texto('moneda', $i18n[155], $datos['compra_total'], false, false, false, false, false, $datos['simbolo']);
            texto('fechayhora', $i18n[156], $datos['compra_fecha']);
            texto('texto', $i18n[157], $datos['caja']);
            texto('texto', $i18n[158], $datos['usuario']);
        } else {
            celda('texto', $i18n[169]);
        }
    }
    contenido_fin();

    salto_linea();
}
ventana_fin();
