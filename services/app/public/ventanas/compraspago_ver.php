<?php
$datos = array_sql(consulta_sql(
    "SELECT compraspagos.*,
        usuarios.nombre AS usuario,
        tablas_formasdepago.nombre AS formapago, tablas_formasdepago.tipo,
        movimientosxcajas.idcaja,
        categorias_conceptos.padres, categorias_conceptos.idconceptopadre, categorias_conceptos.nombre AS concepto,
        categorias_cajas.nombre AS caja,
        compras.numerocompleto,
        monedas.simbolo,
        (SELECT cotizaciones.cotizacion FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND cotizaciones.fechayhora = (SELECT MAX(fechayhora) FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND fechayhora < compraspagos.fecha)) AS cotizacion_anterior
    FROM compraspagos
        LEFT JOIN usuarios ON compraspagos.idusuario = usuarios.idusuario
        LEFT JOIN tablas_formasdepago ON compraspagos.idformapago = tablas_formasdepago.idformapago
        LEFT JOIN movimientosxcajas ON (compraspagos.idcomprapago = movimientosxcajas.idrelacion AND movimientosxcajas.tiporelacion = 'proveedorpago')
        LEFT JOIN categorias_conceptos ON movimientosxcajas.idconcepto = categorias_conceptos.idconcepto
        LEFT JOIN cajas ON movimientosxcajas.idcaja = cajas.idcaja
        LEFT JOIN categorias_cajas ON cajas.idtipocaja = categorias_cajas.idtipocaja
        LEFT JOIN compras ON compraspagos.idcompra = compras.idcompra
        LEFT JOIN monedas ON compraspagos.idmoneda = monedas.idmoneda
    WHERE idcomprapago = '$id' AND movimientosxcajas.tiporelacion = 'proveedorpago'
    LIMIT 1"));
$proveedor = array_sql(consulta_sql(
    "SELECT proveedores.*,
        categorias_localidades.nombre AS localidad
    FROM proveedores
        LEFT JOIN categorias_localidades ON proveedores.idlocalidad = categorias_localidades.idlocalidad
    WHERE idproveedor = '".$datos['idproveedor']."'
    LIMIT 1"));
if ($datos['total']<0) $devolucion = true;
ventana_inicio(($devolucion ? $i18n[240] : $i18n[96]).$nombre, '100', array(

    array('url' => 'compras.php?a=modpago&id='.$id, 'a' => 'mod', 'title' => ($devolucion ? $i18n[241] : $i18n[98]), 'permiso' => 'compraspagos_mod'),
    array('url' => 'compras.php?a=bajapago&id='.$id, 'a' => 'baja', 'title' => ($devolucion ? $i18n[242] : $i18n[99]), 'permiso' => 'compraspagos_baja', 'opciones' => 'onclick="return confirma('."'$i18n[102]'".')"'),
    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => ($devolucion ? $i18n[243] : $i18n[103]))),
    ['tipo_formapago' => $datos['tipo']]);
{
    // Datos básicos
    contenido_inicio($i18n[34], '50');
    {
        if ($datos['idcompra'])
            texto('texto', $i18n[104], $datos['numerocompleto'], 'auto', 'compras.php?a=ver&id='.$datos['idcompra']);
        else
            texto('texto', $i18n[104], $i18n[144]);
        texto('moneda', $i18n[74], $datos['total'], false, false, false, false, false, $datos['simbolo']);
        if ($datos['idmoneda'] != 1)
            texto('moneda', $i18n_funciones[330], $datos['cotizacion_anterior'], 'auto');
        $title_fechas =
            ($datos['closed_at'] != '0000-00-00 00:00:00'
                ? $i18n[383] . ':<br>' . mostrar_fecha('fechayhora', $datos['closed_at']) . '<br>' : '')
            . (($datos['updated_at'] != '0000-00-00 00:00:00' && $datos['updated_at'] != $datos['closed_at'])
                ? $i18n[381] . ':<br>' . mostrar_fecha('fechayhora', $datos['updated_at']) : '');
        texto('fechayhora', $i18n[52], $datos['fecha'], 'auto', false, 'info', false, ['title' => $title_fechas]);
        texto('texto', $i18n[81], $datos['formapago']);
        texto('texto', $i18n[24], $datos['caja'], 'auto', 'cajas.php?a=ver&id='.$datos['idcaja']);
        if ($datos['padres']) {
            while ($datos['padres']) {
                $resultado_sql = consulta_sql("SELECT nombre, idconceptopadre, padres FROM categorias_conceptos WHERE idconcepto = '".$datos['idconceptopadre']."' LIMIT 1");
                $datos['concepto'] = campo_sql($resultado_sql, 0, 'nombre').' > '.$datos['concepto'];
                $datos['idconceptopadre'] = campo_sql($resultado_sql, 0, 'idconceptopadre');
                $datos['padres'] = campo_sql($resultado_sql, 0, 'padres');
            }
        }
        texto('texto', $i18n[143], $datos['concepto']);
        texto('texto', $i18n[51], $datos['usuario']);
    }
    contenido_fin();

    // Proveedor
    contenido_inicio($i18n[35], '50');
    {
        texto('texto', $i18n[39], $proveedor['nombre'], '100', 'proveedores.php?a=ver&id='.$proveedor['idproveedor']);
        texto('texto', $i18n[40], $proveedor['contacto']);
        texto('texto', $i18n[41], $proveedor['telefonos']);
        texto('mail', $i18n[42], $proveedor['mail']);
        texto('texto', $i18n[43], $proveedor['domicilio']);
        texto('texto', $i18n[142], $proveedor['localidad']);
    }
    contenido_fin();

    salto_linea();

    // Cheques
    if ($datos['tiporelacion'] == 'cheque')
    {
        $cheques = array_sql(consulta_sql("SELECT cheques.*, tablas_bancos.nombre FROM cheques LEFT JOIN tablas_bancos ON tablas_bancos.idbanco = cheques.idbanco WHERE idcheque = '".$datos['idrelacion']."' LIMIT 1"));
        contenido_inicio($i18n[249], '100');
        {
            texto('texto', $i18n[258], ($cheques['tipo'] == 'tercero' ? $i18n[281] : $i18n['propio']));
            texto('fecha', $i18n[259], $cheques['fechacobro']);
            texto('texto', $i18n[242], $cheques['nombre']);
            texto('texto', $i18n[244], $cheques['titular']);
            texto('texto', $i18n[243], $cheques['numero'], false, 'cheques.php?a=ver&id='.$cheques['idcheque']);
            $fecha = campo_sql(consulta_sql("SELECT fecha FROM mensajes WHERE idmensaje = '".$cheques['idmensaje']."'"));
            if ($fecha)
                texto('fecha', $i18n[245], $fecha);
            else
                texto('texto', $i18n[245], $i18n[282]);
        }
        contenido_fin();
    }

    // Retenciones
    if ($datos['tiporelacion'] == 'retencion')
    {
        $retencion = array_sql(consulta_sql(
            "SELECT r.observacion, r.puntodeventa, r.numero,
                categorias_tributos.nombre AS tributo
            FROM retenciones AS r
                LEFT JOIN categorias_tributos ON categorias_tributos.idtributo = r.idtributo
            WHERE idretencion = '".$datos['idrelacion']."' LIMIT 1"));
        contenido_inicio($i18n[250], '100');
        {
            texto('texto', $i18n[247], $retencion['tributo']);
            texto('texto', $i18n[191], numero_comprobante('', $retencion['puntodeventa'], $retencion['numero']));
            texto('texto', $i18n[248], $retencion['observacion']);
        }
        contenido_fin();
    }

    // Observaciones
    if ($datos['observacion']) {
        contenido_inicio($i18n[46]);
        {
            observacion('', $datos['observacion']);
        }
        contenido_fin();
    }
}
ventana_fin();
