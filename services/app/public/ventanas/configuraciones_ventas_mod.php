<?php

switch ($boton) {

    case $i18n[65]: //Aceptar
        $datos = recibir_matriz(array('idcomportamiento', 'nombre', 'estado', 'letra', 'letra_hidden', 'puntodeventa', 'puntodeventa_hidden', 'ultimonumero', 'muevestock', 'muevesaldo', 'unificar_productos', 'discrimina', 'tipofacturacion', 'tipoimpresion', 'tienesituacion', 'situacion', 'auto_aprobar', 'telefonos', 'mail', 'domicilio', 'idlocalidad', 'observacion', 'obsinterna', 'iddeposito', 'idusuario', 'deposito_venta_relacionada', 'idmoneda'));
        if (!$datos['letra']) {
            $datos['letra'] = $datos['letra_hidden'];
        }
        if (!$datos['puntodeventa']) {
            $datos['puntodeventa'] = $datos['puntodeventa_hidden'];
        }
        $datos['letra'] = ucwords($datos['letra']);

        if ($datos['estado'] && $datos['tipofacturacion'] != 'interno' && $datos['idcomportamiento']
            && contar_sql(consulta_sql(
            "SELECT idtipoventa
            FROM categorias_ventas
            WHERE idtipoventa != '$id'
                AND estado = '1'
                AND tipofacturacion != 'interno'
                AND idcomportamiento = '".$datos['idcomportamiento']."'
                AND puntodeventa = '".$datos['puntodeventa']."'
            LIMIT 1"))) {
            // Controlo que no exista otro punto de venta fiscal con el mismo número
            mensajes_alta($i18n[437]);
        } elseif (in_array($datos['tipofacturacion'], ['electronico', 'enlinea']) && $datos['estado'] && $datos['idcomportamiento']
            && contar_sql(consulta_sql(
            "SELECT idtipoventa
            FROM categorias_ventas
            WHERE idtipoventa != '$id'
                AND tipofacturacion = '".($datos['tipofacturacion'] == 'electronico' ? 'enlinea' : 'electronico')."'
                AND puntodeventa = '".$datos['puntodeventa']."'
                AND idcomportamiento = '".$datos['idcomportamiento']."'
            LIMIT 1"))) {
            // Controlo que RECE no tenga mismo punto de venta
            mensajes_alta($i18n[615]);

        } elseif ($datos['estado'] && contar_sql(consulta_sql(
            "SELECT idtipoventa
            FROM categorias_ventas
            WHERE idtipoventa != '$id'
                AND estado = 1
                AND nombre = '".$datos['nombre']."'
            LIMIT 1"))) {
            // Controlo que no exista otro punto de venta habilitado con el mismo nombre
            mensajes_alta($i18n[458]);

        } else {
            consulta_sql(
                "UPDATE categorias_ventas SET
                    nombre = '".$datos['nombre']."',
                    estado = '".$datos['estado']."',
                    idmoneda = '".$datos['idmoneda']."',
                    ".($datos['idcomportamiento'] ? "idcomportamiento = '".$datos['idcomportamiento']."'," : "")."
                    ".($datos['letra'] ? "letra = '".$datos['letra']."'," : "")."
                    ".((is_numeric($datos['puntodeventa'])
                        && $datos['puntodeventa'] > 0) ? "puntodeventa = '".$datos['puntodeventa']."'," : "")."
                    ".($datos['ultimonumero'] ? "ultimonumero = '".$datos['ultimonumero']."'," : "")."
                    ".($datos['tipofacturacion'] ? "tipofacturacion = '".$datos['tipofacturacion']."'," : "")."
                    ".($datos['idcomportamiento'] ? "operacioninversa =
                        (SELECT operacioninversa FROM tablas_comportamientos WHERE idcomportamiento = '".$datos['idcomportamiento']."' LIMIT 1)," : "")."
                    ".($datos['idusuario'] ? "idusuario = '".$datos['idusuario']."'," : "idusuario = 0,")."
                    muevestock = '".$datos['muevestock']."',
                    muevesaldo = '".$datos['muevesaldo']."',
                    unificar_productos = '".$datos['unificar_productos']."',
                    tienesituacion = '".$datos['tienesituacion']."',
                    situacion = '".$datos['situacion']."',
                    auto_aprobar = '".$datos['auto_aprobar']."',
                    tipoimpresion = '".$datos['tipoimpresion']."',
                    iddeposito = '".$datos['iddeposito']."',
                    deposito_venta_relacionada = '".!$datos['deposito_venta_relacionada']."',
                    discrimina = '".$datos['discrimina']."',
                    telefonos = '".$datos['telefonos']."',
                    mail = '".$datos['mail']."',
                    domicilio = '".$datos['domicilio']."',
                    idlocalidad = '".$datos['idlocalidad']."',
                    observacion = '".escape_sql($datos['observacion'])."',
                    obsinterna = '".escape_sql($datos['obsinterna'])."'
                WHERE idtipoventa = '".$id."'
                LIMIT 1");
            ir_atras();
        }
        break;

    case $i18n[66]: //Cancelar
        ir_atras();
        break;

    default:
        break;

}

//Muevo esto acá porque on submit y validar con errores, me quedan los $datos del form
$datos = array_sql(consulta_sql(
    "SELECT categorias_ventas.*,
        tablas_comportamientos.nombre AS comportamiento, monedas.nombre AS moneda,
        (SELECT 1 FROM ventas
            WHERE estado = 'cerrado'
                AND ventas.idtipoventa = categorias_ventas.idtipoventa LIMIT 1) AS usado,
        (SELECT 1 FROM tienda WHERE ML_idtipoventa = categorias_ventas.idtipoventa LIMIT 1) AS usado_ML,
        (SELECT 1 FROM tienda WHERE tienda_idtipoventa = categorias_ventas.idtipoventa LIMIT 1) AS usado_tienda,
        (SELECT 1 FROM tienda WHERE tienda_idtipoventa_a = categorias_ventas.idtipoventa LIMIT 1) AS usado_tienda_a,
        (SELECT 1 FROM tienda WHERE tienda_idtipoventa_b = categorias_ventas.idtipoventa LIMIT 1) AS usado_tienda_b
    FROM categorias_ventas
        LEFT JOIN tablas_comportamientos USING (idcomportamiento)
        LEFT JOIN monedas ON categorias_ventas.idmoneda = monedas.idmoneda
    WHERE idtipoventa = '$id'
    LIMIT 1"));

mensajes_efimeros();

ventana_inicio($i18n[191].$datos['nombre']);
{
    // Datos básicos
    contenido_inicio($i18n[89], '33');
    {
        entrada('texto', 'nombre', $i18n[180], $datos['nombre'], '70', '60');
        marcas($i18n[272], '30', array(array('nombre' => 'estado', 'titulo' => $i18n['habilitado'], 'valor' => $datos['estado'])));
        entrada('hidden', 'letra_hidden', false, $datos['letra']);
        entrada('letras', 'letra', $i18n[185], $datos['letra'], '33', '1', false,
            (($datos['usado'] || $datos['idcomportamiento'] < 99 || $datos['idcomportamiento'] > 200) ? 'disabled="disabled"' : ''));
        entrada('numeros', 'puntodeventa', $i18n[193], $datos['puntodeventa'], '33', '5', false,
            ($datos['usado'] ? 'disabled="disabled"' : ''));
        entrada('hidden', 'puntodeventa_hidden', false, $datos['puntodeventa']);
        entrada('numeros', 'ultimonumero', $i18n[186], $datos['ultimonumero'], '33', '8', false,
            ($datos['usado'] && $datos['tipofacturacion'] == 'electronico' ? 'disabled="disabled"' : ''));

        if ($datos['usado'])
            texto('italica', false, $i18n[440], 'auto', false, 'info');
        elseif ($datos['usado_ML'])
            texto('italica', false, $i18n[438]);
        elseif ($datos['usado_tienda'] || $datos['usado_tienda_a'] || $datos['usado_tienda_b'])
            texto('italica', false, $i18n[439]);
    }
    contenido_fin();

    // Depósitos
    contenido_inicio($i18n[543], '33');
    {
        selector('iddeposito', $i18n[337], $datos['iddeposito'], 'auto', 'depositos', false, false, false);
        marcas('', 'auto', array(array(
            'nombre' => 'deposito_venta_relacionada',
            'titulo' => $i18n[644],
            'valor' => !$datos['deposito_venta_relacionada'],
            'ayuda_puntual' => $i18n[645]
        )));
    }
    contenido_fin();

    // Usuario
    contenido_inicio($i18n[630], '33');
    {
        selector('idusuario', $i18n[627], $datos['idusuario'], 'auto', 'usuarios', 'nombre', false, true, true, false, false, array('-1' => $i18n[628]));
    }
    contenido_fin();
    salto_linea();

    // Comportamiento
    contenido_inicio($i18n[226], '33');
    {
        // El select de idcomportamiento y de modo de facturación los convierto en una entrada disabled porque está usado el tipodeventa
        if ($datos['usado'] || $datos['usado_ML'] || $datos['usado_tienda']) {
            entrada('texto', 'comportamiento', $i18n[213], $datos['comportamiento'], 'auto', '20', false, 'disabled="disabled"');
            $modosdefacturacion = array(
                'interno' => $i18n[278],
                'manual' => $i18n[279],
                'electronico' => $i18n[280],
                'enlinea' => $i18n[498],
            );
            entrada('hidden', 'idcomportamiento', false, $datos['idcomportamiento']);
        } else {
            $resultado_sql = consulta_sql("SELECT * FROM tablas_comportamientos WHERE quienusa IN ('".(($_SESSION['configuracion_discrimina']) ? 'responsablesincriptos' : 'monotributistas')."', 'todos')");
            $tiposdecomportamientos = array();

            echo '
    <script charset="UTF-8">
        $(document).ready(function() {
            $("select[name=idcomportamiento]").change(function() {
                switch ($("select[name='."'idcomportamiento'".'] option:selected").val()) {';

            while ($temp_array = array_sql($resultado_sql)) {
                $tiposdecomportamientos[] = array('id' => $temp_array['idcomportamiento'], 'valor' => $temp_array['nombre']);
                echo '
                case "'.$temp_array['idcomportamiento'].'":
                    $("#discrimina").val("'.$temp_array['discrimina'].'");
                    $("input[name=letra], input[name=letra_hidden]").val("'.(50 < $temp_array['idcomportamiento'] && $temp_array['idcomportamiento'] < 99 ? 'M' : $temp_array['discrimina'] ).'");
                    $("input[name=letra]").'.(($temp_array['idcomportamiento'] > 99 && $temp_array['idcomportamiento'] < 200) ? 'removeAttr("disabled")' : 'attr("disabled", "disabled")').';
                    $("#muevestock").prop("checked", '.($temp_array['muevestock'] ? 'true' : 'false').');
                    $("#muevesaldo").prop("checked", '.($temp_array['muevesaldo'] ? 'true' : 'false').');
                    $("#operacioninversa").css("display", "'.($temp_array['operacioninversa'] ? 'block' : 'none').'");
                    break;';
            }
            echo '
                    }
                });
            });
        </script>';

            selector_array('idcomportamiento', $i18n[213], $datos['idcomportamiento'],'auto', $tiposdecomportamientos, $i18n[225]);

        }

        entrada('hidden', 'discrimina', '', $datos['discrimina'], '', false, false, 'id="discrimina"');
        entrada('hidden', 'id', '', $id, '');
        if (!$_SESSION['sistema_gratis']) {
            marcas('', 'auto', array(
                array('nombre' => 'muevestock', 'titulo' => $i18n[194], 'valor' => $datos['muevestock'], 'opciones' => 'id="muevestock"'),
                array('nombre' => 'muevesaldo', 'titulo' => $i18n[195], 'valor' => $datos['muevesaldo'], 'opciones' => 'id="muevesaldo"'),
                array('nombre' => 'unificar_productos', 'titulo' => $i18n[694], 'valor' => $datos['unificar_productos'], 'ayuda_puntual' => $i18n[695]),
            ));
            texto('italica', false, $i18n[196], 'auto', false, false, false,
                'id="operacioninversa"'. (!$datos['operacioninversa'] ? 'style="display: none;"' : ''));
        }
    }
    contenido_fin();

    // Facturación
    contenido_inicio($i18n[254], '33');
    {
        // El select de idcomportamiento y de modo de facturación los convierto en una entrada disabled porque está usado el tipodeventa
        if ($datos['usado'] || $datos['usado_ML'] || $datos['usado_tienda']) {
            entrada('texto', 'tipofacturacion-no', $i18n[243], $modosdefacturacion[$datos['tipofacturacion']], 'auto', '20', false, 'disabled="disabled"');
            entrada('hidden', 'tipofacturacion', false, $datos['tipofacturacion']);

        } else {
            $modosdefacturacion = array(
                array('id' => 'interno', 'valor' => $i18n[278]),
                array('id' => 'electronico', 'valor' => $i18n[280]),
                array('id' => 'manual', 'valor' => $i18n[279]),
                array('id' => 'enlinea', 'valor' => $i18n[498]),
            );
            selector_array('tipofacturacion', $i18n[243], $datos['tipofacturacion'],'auto', $modosdefacturacion, $i18n[277]);
        }

        texto('italica', false, $i18n[445], 'auto', false, false, false,
            'id="esfiscal"' . ($datos['tipofacturacion'] == 'interno' ? ' style="display: none;"' : '')); // Opciones para poder mostrar aclaración cuando un tipo de venta es fiscal
        texto('italica', false, $i18n[499], 'auto', false, false, false,
            'id="enlinea"' . ($datos['tipofacturacion'] == 'enlinea' ? '' : ' style="display: none;"')); // Opciones para poder mostrar aclaración cuando un tipo de venta es RECEL
    }
    contenido_fin();

if (!$_SESSION['sistema_gratis']) {
    // Situación
    contenido_inicio($i18n[460], '33');
    {
        marcas($i18n[149], 'auto', array(
            array('nombre' => 'tienesituacion', 'titulo' => $i18n[97], 'valor' => $datos['tienesituacion'], 'opciones' => 'id="tienesituacion"'),
        ));

        bloque_inicio('situacion', ($datos['tienesituacion'] ? '' : 'style="display: none;"'));
        {
            marcas(false, 'auto', array(
                array('nombre' => 'auto_aprobar', 'titulo' => $i18n[98], 'valor' => $datos['auto_aprobar'], 'ayuda_puntual' => $i18n[99]),
            ));
            $situaciones = array(
                array('id' => 'sin_especificar', 'valor' => $i18n['sin_especificar']),
                array('id' => 'pendiente', 'valor' => $i18n['pendiente']),
                array('id' => 'aprobado', 'valor' => $i18n['aprobado']),
                array('id' => 'rechazado', 'valor' => $i18n['rechazado'])
            );
            selector_array('situacion', $i18n[146], $datos['situacion'], 'auto', $situaciones);
        }
        bloque_fin();
    }
    contenido_fin();
}

    contenido_inicio($i18n[681]);
    {
        if ($_SESSION['modulo_multimoneda']) {
            selector('idmoneda', $i18n[681], $datos['idmoneda'], '25', 'monedas', 'idmoneda', false, false);
            texto('italica', false, $i18n[684], false, false, 'info');
        } else {
            texto('titulo', $i18n[681], $i18n[686], '30');
            entrada('hidden', 'idmoneda', '', 1);
        }
    }
    contenido_fin();

    // Diseño y personalización
    contenido_inicio($i18n[227]);
    {
        texto('italica', false, $i18n[497], 'auto', false, false, $i18n[500]);

        entrada('texto', 'domicilio', $i18n[28], $datos['domicilio'], '25', '150');
        entrada('texto', 'mail', $i18n[27], $datos['mail'], '25', '320');
        entrada('texto', 'telefonos', $i18n[26], $datos['telefonos'], '25', '150');
        selector('idlocalidad', $i18n[29], $datos['idlocalidad'], '25', 'categorias_localidades', 'nombre', true, true, true);

        area('observacion', $i18n[342], $datos['observacion'], 'auto', $i18n[343]);

        salto_linea();
        bajo_linea();

        $selector_opciones = array(
            array('id' => 'predeterminado', 'valor' => $i18n[199]),
            array('id' => 'ticket', 'valor' => $i18n[200]),
            array('id' => 'viejo', 'valor' => $i18n[201])
        );
        selector_array('tipoimpresion', $i18n[197], $datos['tipoimpresion'], '20', $selector_opciones);
        bloque_inicio('configuraciones_tipoimpresion_avanzado');
        {
            texto('texto', $i18n[347], ($datos['estilo_venta'] ? $i18n[284] : $i18n[283]), '40', false, false, $i18n[349]);
            texto('texto', $i18n[348], ($datos['estilo_venta_pdf'] ? $i18n[284] : $i18n[283]), '40', false, false, $i18n[350]);

            if ($datos['estilo_venta']) {
                enlaces('', array(array('url' => URL_S3.$datos['estilo_venta'].'/estilo_venta.css', 'valor' => $i18n[121], 'opciones' => 'target="_blank"'), array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_venta-'.$id, 'valor' => $i18n[111]), array('tipo' => 'ajax', 'url' => 'configuraciones_estilos_baja', 'id' => 'estilo_venta-'.$id, 'valor' => $i18n[112])), '40');
            } else {
                enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_venta-'.$id, 'valor' => $i18n[110])), '40');
            }

            if ($datos['estilo_venta_pdf']) {
                enlaces('', array(array('url' => URL_S3.$datos['estilo_venta_pdf'].'/estilo_venta_pdf.css', 'valor' => $i18n[121], 'opciones' => 'target="_blank"'), array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_venta_pdf-'.$id, 'valor' => $i18n[111]), array('tipo' => 'ajax', 'url' => 'configuraciones_estilos_baja', 'id' => 'estilo_venta_pdf-'.$id, 'valor' => $i18n[112])), '40');
            } else {
                enlaces('', array(array('tipo' => 'modal', 'url' => 'configuraciones_estilos_alta', 'id' => 'estilo_venta_pdf-'.$id, 'valor' => $i18n[110])), '40');
            }
        }
        bloque_fin();

    }
    contenido_fin();

    // Observaciones
    contenido_inicio($i18n[135], '100', true, ($datos['obsinterna'] ? true : false));
    {
        area('obsinterna', $i18n[344], $datos['obsinterna']);
    }
    contenido_fin();

    botones(array(array('valor' => $i18n[65]), array('valor' => $i18n[66])));
}
ventana_fin();

?>
<script type="text/javascript" charset="utf-8">
function validacion_configuraciones_ventas_mod(boton)
{
    if (boton == '<?php echo $i18n[65]; ?>') {
        if ($("#marco_configuraciones_ventas_mod input[name='nombre']").val() == '') {
<?php script_validacion_flotante("alerta", $i18n[270], "input[name='nombre']"); ?>
            return false;

        } else if ($("#marco_configuraciones_ventas_mod input[name='puntodeventa']").val() == '') {
<?php script_validacion_flotante("alerta", $i18n[271], "input[name='puntodeventa']"); ?>

            return false;
        } else if ($("#marco_configuraciones_ventas_mod input[name='ultimonumero']").val() == '') {
<?php script_validacion_flotante("alerta", $i18n[271], "input[name='ultimonumero']"); ?>

            return false;
        } else if ($("#marco_configuraciones_ventas_mod select[name='idcomportamiento'] option:selected").val() > 99 &&
            $("#marco_configuraciones_ventas_mod select[name='idcomportamiento'] option:selected").val() < 200 && $("#marco_configuraciones_ventas_mod select[name='tipofacturacion'] option:selected").val() != 'interno') {
                $("#esfiscal").hide();
                $("#enlinea").hide();
<?php script_validacion_flotante("alerta", $i18n[281], "select[name='tipofacturacion']"); ?>

            $("#marco_configuraciones_ventas_mod select[name='tipofacturacion'] option[value='interno']").prop("selected", "selected");
            return false;
        //Validación para FCE
        } else if (["201", "202", "203"].includes($("#marco_configuraciones_ventas_mod select[name='idcomportamiento'] option:selected").val())
            && $("#marco_configuraciones_ventas_mod select[name='tipofacturacion'] option:selected").val() == 'electronico') {
<?php script_validacion_flotante("alerta", $i18n[643], "select[name='tipofacturacion']"); ?>

            return false;
        } else
            return true;
    } else
        return true;
};

$(function() {

    $("select[name='idcomportamiento']").change(function() {
        if (["201", "202", "203"].includes($("select[name='idcomportamiento'] option:selected").val())) {
            if ($("select[name='tipofacturacion']").val() == 'electronico') {
                $("select[name='tipofacturacion']").val('interno');
            }
            $("select[name='tipofacturacion'] option[value='electronico']").prop('disabled', true);
        } else {
            $("select[name='tipofacturacion'] option[value='electronico']").prop('disabled', false);
        }
    });

    $("select[name='tipofacturacion']").change(function() {

        if ($("select[name='tipofacturacion'] option:selected").val() == 'interno') {
            $("#esfiscal").fadeOut();
        } else {
            $("#esfiscal").fadeIn();
        }
        if ($("select[name='tipofacturacion'] option:selected").val() == 'enlinea') {
            $("#enlinea").fadeIn();
        } else {
            $("#enlinea").fadeOut();
        }

<?php if (!existe_wsfe($_SESSION['configuracion_cuit'], 'crt') || !existe_wsfe($_SESSION['configuracion_cuit'], 'ini')): ?>
        if ($("select[name='tipofacturacion'] option:selected").val() == 'electronico') {
            $("#esfiscal").hide();
            $("#enlinea").hide();
            $("select[name=tipofacturacion]").val("interno");
            <?php ventana_flotante('modal', 'configuraciones_tipofacturacion_electronico'); ?>
        }
<?php endif ?>

    });

    $("#tienesituacion").live("click", function(e) {
        if ($("#tienesituacion").is(":checked"))
            $("#situacion").fadeIn();
        else
            $("#situacion").fadeOut();
    });

});

</script>
