<?php
$rubros = array_all_sql(consulta_sql("SELECT idrubro, idrubropadre, padres FROM categorias_rubros WHERE estado = 1"));

ventana_informe($i18n_informes[200]);
{
    //Filtro por datos
    contenido_inicio($i18n_informes[10], '100');
    {
        //Cliente
        selector_array('filtro_cliente', $i18n_informes[238], $i18n_informes[240], '50',
            array(
            array('id' => '', 'valor' => $i18n_informes[240]),
            array('id' => 'cliente', 'valor' => $i18n_informes[238]),
            array('id' => 'categoria', 'valor' => $i18n_informes[241])
            ), false, 'onchange="cambioTipoCliente()"');
        selector('idtipocliente', $i18n_informes[160], false, '50', 'categorias_clientes', 'nombre', false, false, false);
        salto_linea();
        seleccionador('clientes');
        entrada('hidden', 'idcliente');

        //Producto
        selector_array('filtro_producto', $i18n_informes[278], $i18n_informes[69], '50',
            array(
            array('id' => '', 'valor' => $i18n_informes[69]),
            array('id' => 'producto', 'valor' => $i18n_informes[278]),
            array('id' => 'rubro', 'valor' => $i18n_informes[279])
        ), false, 'onchange="cambioProducto()"');
        entrada('hidden', 'idrubrohijos');
        selector_familiar('idrubro', $i18n_informes[64], $datos['idrubro'], '50', 'categorias_rubros', false, false, true, false, 'onchange="cambioRubro()"', false, array('' => $i18n_informes[65]));
        marcas(false, '50', array(array('nombre' => 'incluir_subrubros', 'titulo' => $i18n_informes[381], 'valor' => $datos['incluir_subrubros'])));
        salto_linea();
        seleccionador('productos');
        entrada('hidden', 'idproducto');

        //Proveedor
        selector_array('filtro_proveedor', $i18n_informes[244], $i18n_informes[67], '50',
            array(
            array('id' => '', 'valor' => $i18n_informes[67]),
            array('id' => 'proveedor', 'valor' => $i18n_informes[244])
            ), false, 'onchange="cambioTipoProveedor()"');
        salto_linea();
        seleccionador('proveedores');
        entrada('hidden', 'idproveedor');
    }
    contenido_fin();

    //Filtro por tipo de venta
    contenido_inicio($i18n_informes[2], '100');
    {
        selector('idtipoventa', $i18n_informes[3], false, '25', 'categorias_ventas', 'nombre', false, $i18n_informes[4], false, false, 'onchange="cambioTipoVenta()"');
        selector_array('tipofacturacion', $i18n_informes[5], false, '25',
            array(
            array('id' => '', 'valor' => $i18n_informes[6]),
            array('id' => 'interno', 'valor' => $i18n_informes[27]),
            array('id' => 'electronico', 'valor' => $i18n_informes[28]),
            array('id' => 'manual', 'valor' => $i18n_informes[29]),
            array('id' => 'enlinea', 'valor' => $i18n_informes[30]),
            array('id' => 'fiscal', 'valor' => $i18n_informes[231])
        ), false, 'onchange="cambioTipoFacturacion()"');
        selector_array('estado', $i18n_informes[13], 'cerrado', '25',
            array(
            array('id' => '', 'valor' => $i18n_informes[14]),
            array('id' => 'abierto', 'valor' => $i18n_informes[31]),
            array('id' => 'cerrado', 'valor' => $i18n_informes[32]),
            array('id' => 'anulado', 'valor' => $i18n_informes[33])
        ));
        selector_array('situacion', $i18n_informes[15], '', '25',
        array(
            array('id' => '', 'valor' => $i18n_informes[16]),
            array('id' => 'sin_especificar', 'valor' => $i18n_informes[40]),
            array('id' => 'pendiente', 'valor' => $i18n_informes[41]),
            array('id' => 'aprobado', 'valor' => $i18n_informes[42])
        ));
    }
    contenido_fin();

    //Filtro por movimientos del comprobante
    contenido_inicio($i18n_informes[201], '100');
    {
        selector_array('comportamiento', $i18n_informes[8], false, '50',
            array(
            array('id' => '', 'valor' => $i18n_informes[9]),
            array('id' => 'muevetodo', 'valor' => $i18n_informes[34]),
            array('id' => 'muevesaldo', 'valor' => $i18n_informes[35]),
            array('id' => 'muevestock', 'valor' => $i18n_informes[36]),
            array('id' => 'muevenada', 'valor' => $i18n_informes[37]),
            array('id' => 'muevesaldosolo', 'valor' => $i18n_informes[38]),
            array('id' => 'muevestocksolo', 'valor' => $i18n_informes[39])
        ));
        marcas($i18n_informes[202], '33',
            array(
            array('titulo' => $i18n_informes[203], 'valor' => 1, 'nombre' => 'no_mostrar_iva'),
            array('titulo' => $i18n_informes[270], 'valor' => false, 'nombre' => 'sumar_valores', 'ayuda_puntual' => $i18n_informes[271])
        ));
    }
    contenido_fin();

    //Filtros por fecha
    contenido_inicio($i18n_informes[17], '50');
    {
        entrada('fechayhora', 'desde', $i18n_informes[18], false, '50');
        entrada('fechayhora', 'hasta', $i18n_informes[19], false, '50');
    }
    contenido_fin();

    //Filtro por texto
    contenido_inicio($i18n_informes[70], '50');
    {
        entrada('texto', 'texto_filtrar', $i18n_informes[71], false, '50', '100');
        selector_array('campo_filtrar', $i18n_informes[72], false, '50',
            array(
            array('id' => 'todos', 'valor' => $i18n_informes[73]),
            array('id' => 'productosxventas.nombre', 'valor' => $i18n_informes[74]),
            array('id' => 'productosxventas.codigo', 'valor' => $i18n_informes[75]),
            array('id' => 'productosxventas.observacion', 'valor' => $i18n_informes[76])
        ));
    }
    contenido_fin();

    // Campos para mostrar
    contenido_inicio($i18n_informes[20], '100');
    {
        selector_array('agrupar', $i18n_informes[78], false, '33',
            array(
            array('id' => '', 'valor' => $i18n_informes[79]),
            array('id' => 'clientes', 'valor' => $i18n_informes[272]),
            array('id' => 'productos', 'valor' => $i18n_informes[273]),
            array('id' => 'tipoventa', 'valor' => $i18n_informes[274]),
            array('id' => 'rubros', 'valor' => $i18n_informes[275])
        ));
        marcas($i18n_informes[22], '33',
            array(
            array('nombre' => 'mostrar_codigo', 'titulo' => $i18n_informes[75], 'valor' => 1),
            array('nombre' => 'mostrar_cliente', 'titulo' => $i18n_informes[141], 'valor' => false),
            array('nombre' => 'mostrar_email', 'titulo' => $i18n_informes[228], 'valor' => false),
            array('nombre' => 'mostrar_categoria_cliente', 'titulo' => $i18n_informes[181], 'valor' => false),
            array('nombre' => 'mostrar_cuit', 'titulo' => $i18n_informes[25], 'valor' => false),
            array('nombre' => 'mostrar_rubro', 'titulo' => $i18n_informes[267], 'valor' => false),
            array('nombre' => 'mostrar_datosextra_venta', 'titulo' => $i18n_informes[261].$i18n_informes[378], 'opciones' => 'onclick="checkDatosExtra(`ventas`)"'),
            array('nombre' => 'mostrar_datosextra_producto', 'titulo' => $i18n_informes[277], 'opciones' => 'onclick="checkDatosExtra(`productos`)"'),
            array('nombre' => 'mostrar_usuario', 'titulo' => $i18n_informes[23], 'valor' => false),
            array('nombre' => 'mostrar_deposito', 'titulo' => $i18n_informes[336], 'valor' => false)
        ));

    }
    contenido_fin();

    if ($_SESSION['modulo_multimoneda']) {
        contenido_inicio($i18n_informes[404], '100');
        {
            selector('idmoneda', $i18n_informes[403], false, '33', 'monedas', false, false, false, false);
            texto('italica', false, $i18n_informes[405], 'auto', false, 'info');
        }
        contenido_fin();
    } else {
        entrada('hidden', 'idmoneda', '', 1);
    }

    botones(array(array('tipo' => 'nueva', 'valor' => $i18n_informes[26], 'opciones' => 'onclick="noDescargarInforme()"'), array('tipo' => 'nueva', 'valor' => $i18n_informes[376], 'opciones' => 'onclick="descargarInforme()"')));

}
ventana_fin();
?>
<script>
    var rubros = '<?php echo json_encode($rubros); ?>';
    $(function() {
        cambioTipoCliente();
        cambioTipoProveedor();
        cambioProducto();
        $("#proveedores_id").change(function () {
            $("input[name=idproveedor]").val($(this).val());
        });
        $("#clientes_id").change(function () {
            $("input[name=idcliente]").val($(this).val());
        });
        $("#productos_id").change(function () {
            $("input[name=idproducto]").val($(this).val());
        });
        $("input[name='incluir_subrubros']").parent().hide();
    });
</script>
