<?php

$tipo_informes = [
    ["id" => '1_1', 'valor' => $i18n_informes[421]],
    ["id" => '1_0', 'valor' => $i18n_informes[422]],
    ["id" => '0_0', 'valor' => $i18n_informes[423]]
];

$monedas = [
    ["id" => 0, 'valor' => $i18n_informes[426]]
];

$monedas_array = obtener_cotizaciones();

foreach ($monedas_array as $moneda) {
    $monedas[] = ["id" => $moneda['id'], 'valor' => $moneda['valor']];
}

ventana_informe($i18n_informes[309]);
{
    // Filtro tipo de informe
    contenido_inicio($i18n_informes[420]);
    {
        selector_array('tipo_informe', $i18n_informes[425], '1_1', '50', $tipo_informes, false, 'onchange="cambioInformeClientes()"');
        bloque_inicio('bloque_importe_minimo');
        {
            entrada('moneda', 'importe_minimo', $i18n_informes[427], 1, '50', '10');
        }
        bloque_fin();
	}
    contenido_fin();


    // Moneda
    contenido_inicio($i18n_informes[403]);
    {
        selector_array('idmoneda', $i18n_informes[424], 0, '50', $monedas);
	}
    contenido_fin();

    botones(array(array('tipo' => 'nueva', 'valor' => $i18n_informes[26], 'opciones' => 'onclick="noDescargarInforme()"'), array('tipo' => 'nueva', 'valor' => $i18n_informes[376], 'opciones' => 'onclick="descargarInforme()"')));
}
ventana_fin();
?>
