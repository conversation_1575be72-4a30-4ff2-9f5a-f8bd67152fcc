 function cambioTipoProveedor() {
    if ($("select[name='filtro_proveedor'] option:selected").val() == 'proveedor') {
        $("#proveedores_input, #proveedores_id, input[name=idproveedor]").prop('disabled',false);
        $("#select_idtipoproveedor").prop('disabled',true);
        $("#proveedores_seleccionador, #proveedores_seleccionado").show();
        $(".idtipoproveedor").hide();
    } else if ($("select[name='filtro_proveedor'] option:selected").val() == 'categoria') {
        $("#proveedores_input, #proveedores_id, input[name=idproveedor]").prop('disabled',true);
        $("#select_idtipoproveedor").prop('disabled', false);
        $(".idtipoproveedor").show();
        $("#proveedores_resultados").hide();
        $("#proveedores_seleccionador, #proveedores_seleccionado").hide();
    } else {
        $("#proveedores_input, #proveedores_id, input[name=idproveedor]").prop('disabled',true);
        $("#select_idtipoproveedor").prop('disabled',true);
        $("#proveedores_resultados").hide();
        $("#proveedores_seleccionador, #proveedores_seleccionado").hide();
        $(".idtipoproveedor").hide();
    }
}

/* Chequear */
/*function cambioTipoProveedor() {
    if ($("select[name='filtro_proveedor'] option:selected").val() == 'proveedor') {
        $("#proveedores_input, #proveedores_id, input[name=idproveedor]").prop('disabled',false);
        $("#proveedores_seleccionador, #proveedores_seleccionado").show();
    } else {
        $("#proveedores_input, #proveedores_id, input[name=idproveedor]").prop('disabled',true);
        $("#proveedores_resultados").hide();
        $("#proveedores_seleccionador, #proveedores_seleccionado").hide();
    }
}*/

function cambioTipoVenta () {
    if ($("select[name=idtipoventa] option:selected").val()) {
        $("select[name=tipofacturacion]").attr("disabled", "disabled");
        $("select[name=tipofacturacion] option:selected").removeAttr("selected");
    } else {
        $("select[name=tipofacturacion]").removeAttr("disabled");
    }
}
function cambioTipoFacturacion () {
    if ($("select[name=tipofacturacion] option:selected").val()) {
        $("select[name=idtipoventa]").attr("disabled", "disabled");
        $("select[name=idtipoventa] option:selected").removeAttr("selected");
    } else {
        $("select[name=idtipoventa]").removeAttr("disabled");
    }
}

function cambioTipoCliente() {
    if ($("select[name='filtro_cliente'] option:selected").val() == 'cliente') {
        $("#clientes_input, #clientes_id, input[name=idcliente]").prop('disabled',false);
        $("#select_idtipocliente").prop('disabled', true);
        $("#clientes_seleccionador, #clientes_seleccionado").show();
        $(".idtipocliente").hide();
    } else if ($("select[name='filtro_cliente'] option:selected").val() == 'categoria') {
        $("#clientes_input, #clientes_id, input[name=idcliente]").prop('disabled',true);
        $("#select_idtipocliente").prop('disabled', false);
        $(".idtipocliente").show();
        $("#clientes_resultados").hide();
        $("#clientes_seleccionador, #clientes_seleccionado").hide();
    } else {
        $("#clientes_input, #clientes_id, input[name=idcliente]").prop('disabled',true);
        $("#select_idtipocliente").prop('disabled',true);
        $("#clientes_resultados").hide();
        $("#clientes_seleccionador, #clientes_seleccionado").hide();
        $(".idtipocliente").hide();
    }
}

function cambioProducto() {
    if ($("select[name='filtro_producto'] option:selected").val() == 'producto') {
        $("#productos_input, #productos_id, input[name=idproducto]").prop('disabled',false);
        $("#select_idrubro").prop('disabled', true);
        $("#productos_seleccionador, #productos_seleccionado").show();
        $(".idrubro").hide();
    } else if ($("select[name='filtro_producto'] option:selected").val() == 'rubro') {
        $("#productos_input, #productos_id, input[name=idproducto]").prop('disabled',true);
        $("#select_idrubro").prop('disabled', false);
        $(".idrubro").show();
        $("#productos_resultados").hide();
        $("#productos_seleccionador, #productos_seleccionado").hide();
    } else {
        $("#productos_input, #productos_id, input[name=idproducto]").prop('disabled',true);
        $("#select_idrubro").prop('disabled',true);
        $("#productos_resultados").hide();
        $("#productos_seleccionador, #productos_seleccionado").hide();
        $(".idrubro").hide();
    }
}

function validar_fechas(desde, hasta, dias) {

    if (typeof desde === 'undefined' || !desde
        || typeof hasta === 'undefined' || !hasta) {
        $("#flotante").append('<div class="alerta" id="validar_fecha"><span class="campo_texto">Por favor complete los rangos de fecha.</span></div>'); //ver con Andiaco
        $("#validar_fecha").delay(5000).fadeOut(1000);
        return false;
    }

    desde = parsearFechasJs(desde);
    hasta = parsearFechasJs(hasta);

    if (desde.setDate(desde.getDate() + dias) < hasta.setDate(hasta.getDate())) {
        $("#flotante").append('<div class="alerta" id="validar_fecha"><span class="campo_texto">Por favor, elegir rango de hasta ' + dias + ' días.</span></div>'); //ver con Andiaco
        $("#validar_fecha").delay(5000).fadeOut(1000);
        return false;
    } else {
        return true;
    }
}

// Convierte fecha_sql Y-m-d H:i:s a js Date object
function parsearFechasJs(fecha)
{
    let fechaTemp = fecha.split(' ');
    let fechaTemp2 = fechaTemp[0].split('-');

    return new Date(fechaTemp2[1] + '/' + fechaTemp2[0] + '/' + fechaTemp2[2] + ' ' + fechaTemp[1]);
}

function cambioConcepto()
{
    $("#idconceptohijos").val("");
    let idconcepto = $("select[name=idconcepto] option:selected").val();
    if (idconcepto > 0) {

        let idconceptoArrayList = [];
        let conceptosArr = JSON.parse(conceptos);

        idconceptoArrayList.push(idconcepto); //el seleccionado

        let hijos = getHijos(conceptosArr, idconcepto);
        idconceptoArrayList = [...hijos];

        $("#idconceptohijos").val([...new Set(idconceptoArrayList)].toString());
    }
}

function getHijos(arr, idPadre, tipo = 'concepto')
{
    let idList = [];
    arr.map((item) => {
        if (item['id'+tipo+'padre'] == idPadre) {
            if (!idList.includes(item['id'+tipo])) {
                idList.push(item['id'+tipo]);
            }
        }
    });

    for (let i = 0; i < idList.length; i++) {
        let hijos = getHijos(arr, idList[i], tipo);
        idList = [...idList, ...hijos];
    }

    return idList;
}

function descargarInforme() {
    $("input[name='descarga_informe']").val(1);
}

function noDescargarInforme() {
    $("input[name='descarga_informe']").val("");
}

function validarTipoformapago() {
    let tipoformapago = $("select[name='idformapago'] option:selected").attr('data-tipoformapago');
    if (tipoformapago != 'cheque' &&  tipoformapago != 'retencion') {
        tipoformapago = '';
    }
    $("input[name=tipoformapago]").val(tipoformapago);
}

function checkInformesStock() {
    let deposito = $("select[name='iddeposito'] option:selected").val();
    if (deposito != '') {
        $('[name="mostrar_stocktotal"]').prop('checked', false);
        $('[name="mostrar_stockvalorizado"]').prop('checked', false);
        $('[name="mostrar_stocktotal"]').prop('disabled', true);
        $('[name="mostrar_stockvalorizado"]').prop('disabled', true);
    } else {
        $('[name="mostrar_stocktotal"]').prop('disabled', false);
        $('[name="mostrar_stockvalorizado"]').prop('disabled', false);
        $('[name="mostrar_stocktotal"]').prop('checked', true);
    }
}

function cambioRubro() {
    let idrubro = $("select[name=idrubro] option:selected").val();
    if (idrubro > 0) {
        $("input[name='incluir_subrubros']").parent().show();
    } else {
        $("input[name='incluir_subrubros']").parent().hide();
    }

    $("#idrubrohijos").val("");
    if (idrubro > 0) {

        let idrubroArrayList = [];
        let rubrosArr = JSON.parse(rubros);

        idrubroArrayList.push(idrubro); //el seleccionado

        let hijos = getHijos(rubrosArr, idrubro, 'rubro');
        idrubroArrayList = [...hijos];

        $("#idrubrohijos").val([...new Set(idrubroArrayList)].toString());
    }
}

function checkDatosExtra(modulo)
{
    let datosExtraVenta = $("input[name='mostrar_datosextra_venta']");
    let datosExtraCliente = $("input[name='mostrar_datosextra_cliente']");
    let datosExtraCompra = $("input[name='mostrar_datosextra_compra']");
    let datosExtraProveedor = $("input[name='mostrar_datosextra_proveedor']");
    let datosExtraProducto = $("input[name='mostrar_datosextra_producto']");

    if (modulo === 'clientes' && datosExtraVenta.is(':checked')) {
        datosExtraVenta.prop('checked', false);
    }

    if (modulo === 'ventas' && datosExtraCliente.is(':checked')) {
        datosExtraCliente.prop('checked', false);
    }

    if (modulo === 'ventas' && datosExtraProducto.is(':checked')) {
        datosExtraProducto.prop('checked', false);
    }

    if (modulo === 'proveedores' && datosExtraCompra.is(':checked')) {
        datosExtraCompra.prop('checked', false);
    }

    if (modulo === 'compras' && datosExtraProveedor.is(':checked')) {
        datosExtraProveedor.prop('checked', false);
    }

    if (modulo === 'productos' && datosExtraVenta.is(':checked')) {
        datosExtraVenta.prop('checked', false);
    }
}

function cambiar_fechas_informes() {
    let idSelectorFecha = parseInt($("select[name='fecha_informes'] option:selected").val());

    if (idSelectorFecha === 13) {
        $("#bloque_fecha_informes_personalizado").show();
    } else {
        $("#bloque_fecha_informes_personalizado").hide();
    }

    const now = new Date();
    let startOfToday = new Date();
    startOfToday.setHours(0, 0, 0, 0);
    let endOfToday = new Date();
    endOfToday.setHours(23, 59, 59, 59);
    let desde = "", hasta = "";

    switch (idSelectorFecha) {
        case 2: // Hoy
            desde = formatDate(startOfToday);
            hasta = formatDate(endOfToday);
            break;
        case 3: // Ayer
            let startYesterday = startOfToday;
            let endYesterday = endOfToday;
            startYesterday.setDate(startOfToday.getDate() - 1)
            endYesterday.setDate(endOfToday.getDate() - 1)
            desde = formatDate(startYesterday);
            hasta = formatDate(endYesterday);
            break;
        case 4: // Esta semana
            const startOfWeek = new Date(now);
            startOfWeek.setHours(0, 0, 0, 0);
            startOfWeek.setDate(now.getDate() - now.getDay() + 1);
            desde = formatDate(startOfWeek);
            hasta = formatDate(endOfToday);
            break;
        case 5: // Semana anterior
            const lastWeekStart = new Date(now);
            lastWeekStart.setHours(0, 0, 0, 0);
            lastWeekStart.setDate(now.getDate() - now.getDay() - 6);
            const lastWeekEnd = new Date(lastWeekStart);
            lastWeekEnd.setHours(23, 59, 59, 59);
            lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
            desde = formatDate(lastWeekStart);
            hasta = formatDate(lastWeekEnd);
            break;
        case 6: // Últimos 7 días
            const sevenDaysAgo = new Date(now);
            sevenDaysAgo.setHours(0, 0, 0, 0);
            sevenDaysAgo.setDate(now.getDate() - 6);
            desde = formatDate(sevenDaysAgo);
            hasta = formatDate(endOfToday);
            break;
        case 7: // Este mes
            const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
            startOfMonth.setHours(0, 0, 0, 0);
            desde = formatDate(startOfMonth);
            hasta = formatDate(endOfToday);
            break;
        case 8: // Mes anterior
            const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            startOfLastMonth.setHours(0, 0, 0, 0);
            const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
            endOfLastMonth.setHours(23, 59, 59, 59);
            desde = formatDate(startOfLastMonth);
            hasta = formatDate(endOfLastMonth);
            break;
        case 9: // Últimos 30 días
            const thirtyDaysAgo = new Date(now);
            thirtyDaysAgo.setHours(0, 0, 0, 0);
            thirtyDaysAgo.setDate(now.getDate() - 29);
            desde = formatDate(thirtyDaysAgo);
            hasta = formatDate(endOfToday);
            break;
        case 10: // Este año
            const startOfYear = new Date(now.getFullYear(), 0, 1);
            startOfYear.setHours(0, 0, 0, 0);
            desde = formatDate(startOfYear);
            hasta = formatDate(endOfToday);
            break;
        case 11: // Año anterior
            const startOfLastYear = new Date(now.getFullYear() - 1, 0, 1);
            startOfLastYear.setHours(0, 0, 0, 0);
            const endOfLastYear = new Date(now.getFullYear() - 1, 11, 31);
            endOfLastYear.setHours(23, 59, 59, 59);
            desde = formatDate(startOfLastYear);
            hasta = formatDate(endOfLastYear);
            break;
        case 12: // Últimos 12 meses
            const twelveMonthsAgo = new Date(now);
            twelveMonthsAgo.setHours(0, 0, 0, 0);
            twelveMonthsAgo.setFullYear(now.getFullYear() - 1);
            twelveMonthsAgo.setDate(twelveMonthsAgo.getDate() + 1);
            desde = formatDate(twelveMonthsAgo);
            hasta = formatDate(endOfToday);
            break;
        default:
            desde = "";
            hasta = "";
    }

    if (desde && hasta) {
        $("input[name='desde']").val(desde);
        $("input[name='hasta']").val(hasta);
    }
}

function formatDate(date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${day}-${month}-${year} ${hours}:${minutes}`;
}

function cambioInformeClientes() {
    if ($("select[name='tipo_informe'] option:selected").val() == '1_1') {
        $("#bloque_importe_minimo").show();
    } else {
        $("#bloque_importe_minimo").hide();
    }
}
