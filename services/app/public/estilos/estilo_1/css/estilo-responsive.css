/* Nuevo estilo tipo responsive etapas DEV */
/* Últimas modificaciones realizadas por QA el día 12-12-2019 */

/* VARIOS PREDETERMINADOS */
body {
    padding:0px 0px 0px 0px;
    margin:0px 0px 0px 0px;
    background: #6D7B81;
    /* Lo centro para IE */
    * text-align: center !important;
    /*width: 98%;*/
    /* Sigo para los navegadores buenos */
    /*margin-left: 1%;*/
    font-family: tahoma;
}

img { border: none; vertical-align: middle; }
form { margin-bottom: 0; }
a { color: #2D5CA3; }
select { margin: 0px; }

p {
    text-align: left;
    font-family: Tahoma;
    font-weight: normal;
    font-size: 16px;
}

/* ENCABEZADO */
#contenedor {
    padding:0px 0px 0px 0px;
    margin:0px 0px 0px 0px;
    margin-top: -16px;
    text-align: left;
    height: auto;
    overflow: hidden;
}

#encabezado {
    padding: 0px 0px 0px 0px;
    margin: px 0px 0px 0px;
    background: #FFFFFF;
    height: auto;
    border-radius: 0px 0px 5px 5px;
    border-bottom: 2px solid #CCCCCC;
}

.encabezado_empresa {
    font-family: Arial, georgia;
    padding-top: 18px;
    padding-left: 20px;
    margin-bottom: 0px;
    font-weight: bold;
    font-style: italic;
    font-size: 16px;
    line-height: 13px;
    * padding-top: 23px !important;
}

.encabezado_version,
.encabezado_usuario,
.encabezado_fecha {
    font-size: 12px;
    color: #666666;
    margin-top:2px;
    margin-bottom:0px;
    padding-top:0px;
    padding-left: 20px;
}

#botones_encabezado {
    /*float:right;*/
    position: relative;
    text-align: right;
    margin-top: -70px;
    height: 70px;
    margin-right: 10px;
    z-index: 301;
}

/* CUERPO */
#cuerpo {
    margin-top: 5px;
    margin-left: 0px;
    min-height: 350px;
    height: expression( this.scrollHeight < 351 ? "350px" : "auto" );
    padding: 0 0 0 0;
}

/* PIE */
#fin {
    /*width: 100%;*/
}

.fin {
    color: #CCCCCC;
    /*border-top: solid;*/
    border-top-width: 1px;
    border-top-color: #CCCCCC;
    padding-top: 5px;
    text-align: right;
}

.subir {
    float: left;
    pointer: cursor;
}

.roll {
    width: 30px;
}

/* MENU PRINCIPAL */
#menu {
    /* float: left;*/
    /*width: 1000px;*/
    height: auto;
    min-height: 60px;
    padding: 5px 0px 15px 0px;
    text-align: center;
    margin-top: 10px;
    font-size: 12px;
    color: #999;
    overflow:hidden !important;
    border-top:1px solid #DCDCDC;
    background-color: #F1F1F1;
    border-radius: 0px 0px 5px 5px;
    /*
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    box-shadow: 0px 0px 3px #000;
    -webkit-box-shadow: 0px 0px 3px #000;
    -moz-box-shadow: 0px 0px 3px #000;
    background: #E9ECF3;
    border-color: #FFF;
    */
}

/* LISTA DE OPCIONES DEL MENU */
#menu ul {
    list-style: none;
    line-height: 0px;
    text-align: left;
    margin-left: 15px;
    margin-top: 12px;
    padding-left: 0px;
}

#menu li {
    float: left;
    width: 50px;
    list-style: none;
    line-height: 20px;
    margin-left: 15px;
    padding-top: 3px;
    display: inline-block;
    text-decoration: none;
    cursor: default;
    text-align: center;
}

.li_menu_activo {
    text-align: center;
    background-color: #CCCCCC;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    text-decoration: none;
    color: #333333;
    font-weight: bold;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
}

.li_menu_hover,
.li_menu_hover {
    text-align: center;
    background-color: #CCCCCC;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    /*
    border-right: #FFF solid 1px;
    * border-left: #000 solid 5px;
    */
    text-decoration: none;
    color: #333333;
    font-weight: bold;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
}

.gratis a{
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
}

.gratis {
    opacity: 0.5;
}

#menu .submenu ul{
    list-style: none;
    margin-left: -40px;
    position: absolute;
    padding: 10px 10px 10px 10px;
    padding-left: 0px;
    width: 130px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
    background-color: #FFF;
    z-index: 100;
    text-indent: 0px;
    margin-top: 0px;
    border: #CCC solid 1px;
}

#menu .submenu ul li a,
#menu .submenu ul li a:hover {
    background: none;
    display: block;
    height: 26px;
    border-bottom: #CCC dotted 1px;
    width: 110px;
    text-align: center;
    vertical-align: middle;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    box-shadow: 0px 0px 0px;
    -webkit-box-shadow: 0px 0px 0px;
    -moz-box-shadow: 0px 0px 0px;
    padding: 0px 0px 0px 0px;
    color: #555;
    font-weight: normal;
    text-indent: 0px;
    margin-left: -5px;
    text-decoration: none;
    padding-top: 5px;
    margin-top: -3px;
}


#menu .submenu .submenu2 ul {
    margin-top: -30px;
    margin-left: 100px;
    width: auto;
    border-radius: 0px 15px 15px 15px;
}

#menu .submenu .submenu2 ul li {
    margin-left: 0px;
    width: auto;
}

#menu .submenu .submenu2 ul li a {
    margin-left: 10px;
    width: 200px;
    display: table;
}

.menu_titulo {
    display: block;
    height: 8px;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: normal;
    padding: 10px 10px 10px 20px;
    color: #C90A3B;
}

#menu .submenu ul li a:hover,
#menu .li_menu_activo .submenu ul li a:hover { background-color: #CCCCCC; color: #333333;cursor: pointer; }

ul li ul {
    display: none;
}

/* Aca hago que el menú no se mueva en IE8+ */
.li_menu_hover { margin-top: 0px\9; border-top: #666 solid 2px\9; padding-bottom: 3px\9; }

/* MARCOS Y VENTANAS */
.marco {
    /*width: 1000px;*/
    background: none;
    color: #2A2A2A;
    font-size: 14px;
}

.ventana_100,
.ventana_66,
.ventana_50,
.ventana_33 {
    * float: left !important;
    margin-top: 15px;
}

.ventana_inicio {
    height: 24px;
    padding-top: 5px;
    padding-bottom:5px;
}

.ventana_contenido {
    width: 100%;
    overflow: hidden;
    /*padding: 10px 10px 10px 10px;*/
}

.ventana_fin {
    height: 15px;
    border-bottom:1px solid #CCCCCC;
}

.ventana_titulo {
    font-family: Tahoma;
    font-size: 17px;
    padding-left: 5px;
    color: #656565;
    letter-spacing: -0.6px;
}

.titulo_iconito {
    padding-right: 3px;
    padding-bottom: 3px;
}

.ventana_datos {
    float: left;
    margin-right: 15px;
    margin-top: -5px;
}

.ventana_botones {
    float: right;
    margin-right: 5px;
}

.ventana_iconito {
    width: 21px;
    padding-right: 3px;
}

.ventana_100 { width: 100%; }
.ventana_100 .ventana_inicio { background-color:#F1F1F1; border-radius: 5px 5px 0px 0px; height: 32px;}
.ventana_100 .ventana_contenido { background-color:#FFFFFF; }
.ventana_100 .ventana_fin { background-color:#FFFFFF; border-radius:0px 0px 5px 5px;}

.ventana_66 { width: 100%; }
.ventana_66 .ventana_inicio { background-color:#F1F1F1; /*border-radius: 5px 5px 0px 0px;*/}
.ventana_66 .ventana_contenido { background-color:#FFFFFF; }
.ventana_66 .ventana_fin { background-color:#FFFFFF; /*border-radius:0px 0px 5px 5px;*/}

.ventana_50 { width: 100%; }
.ventana_50 .ventana_inicio { background-color:#F1F1F1; /*border-radius: 5px 5px 0px 0px;*/}
.ventana_50 .ventana_contenido { background-color:#FFFFFF; }
.ventana_50 .ventana_fin { background-color:#FFFFFF; /*border-radius:0px 0px 5px 5px;*/}

.ventana_33 { width: 100%; }
.ventana_33 .ventana_inicio { background-color:#F1F1F1; /*border-radius: 5px 5px 0px 0px;*/}
.ventana_33 .ventana_contenido { background-color:#FFFFFF; }
.ventana_33 .ventana_fin { background-color:#FFFFFF; /*border-radius:0px 0px 5px 5px;*/}

/* VENTANAS FLOTANTES */
#bloquea {
    display: none;
    width: 100%;
    height: 110%;
    position: fixed;
    z-index: 100;
    background-color: black;
    zoom: 1;
    filter: alpha(opacity=60);
    opacity: 0.6;
}

#actualizando {
    text-align: center;
    display: none;
    z-index: 101;
    zoom: 1;
    filter: alpha(opacity=50);
    opacity: 0.5;
}

#marco_flotante {
    position: fixed;
    display: none;
    z-index: 200;
    font-size: 13px;
    text-align: left;
}

/* CONTENIDOS */
.contenido_100,
.contenido_66,
.contenido_50,
.contenido_33 {
    float: left;
    margin: 10px 0px 5px 2px;
    /*width: 98%;*/
    /* clear: both !important; */
    padding: 5px 15px 5px 15px;
    line-height: 20px;
    border-bottom: dotted 1px #B6B6B6;
}
.contenido_100 { /*width: 96%;*/ }
.contenido_66 { width: 62%; }
.contenido_50 { width: 46%; }
.contenido_33 { width: 30%; }

/* Le quito la linea si esta al final del ventana_contenido */
div.ventana_contenido > div.contenido_100:last-child,
div.ventana_contenido > div.contenido_66:last-child,
div.ventana_contenido > div.contenido_50:last-child,
div.ventana_contenido > div.contenido_33:last-child {border-bottom:none; width: 100%;}


.contenido_titulo {
    /* align-content: normal; */
    font-size: 14px;
    background-color: #CCDAE9;
    display: inline-block;
    margin-bottom: 3px;
    /* text-decoration: underline; */
    margin-top: 0px;
    color: #3D75A5;
    padding: 5px 15px 5px 13px;
    border-radius: 0px 3px 0px 0px;
    letter-spacing: 0.3px;
}

/* BOTONES */
.botones {
    clear: both;
    float: left;
    width: 100%;
    text-align: center;
    padding-top: 15px;
    padding-top: 15px;
}

.boton {
    background:#E1E1E1;
    padding: 10px 18px 10px 18px;
    font-size: 16px;
    border-radius: 3px;
    border:none;
}
.boton:hover {background:#445766; color:#FFFFFF;}


/* LÍNEAS */
.linea_titulo,
.linea_impar,
.linea_par,
.linea_subtotal,
.linea_total,
.linea_division,
.linea_agrandar,
.linea_temp,
.linea_contenido {
    width: 100%;
    min-height: 35px;
    height: expression( this.scrollHeight < 36 ? "35px" : "auto" );
    margin: 0px;
}

.linea_titulo,
.linea_impar,
.linea_par {min-width: 990px;}

.linea_titulo {
    border-bottom: #CCC solid 2px;
    font-weight: bold;
}

.linea_impar,
.linea_impar:hover,
.linea_par,
.linea_par:hover,
.linea_temp:hover {
    border-bottom: #CCC dotted 1px;
}

.linea_impar:hover,
.linea_par:hover,
.linea_temp:hover,
.linea_hover {
    background: #E2E2E2;
    transition-duration: .4s;
}

.linea_impar,
.linea_par,
.linea_temp{
    background: none;
    transition-duration: .4s;
}

.linea_subtotal {
    border-bottom: #CCC dotted 1px;
}

.linea_total {
    border-bottom: #CCC dotted 2px;
}

.linea_division {
    border-bottom: #CCC dotted 1px;
}

.linea_agrandar {
    border-bottom: #CCC dotted 1px;
    text-align: center;
    min-height: 24px;
    padding: 6px 0 0 0;
}

.linea_temp {

}

.linea_contenido {
    float: left;
}

.linea_contenido_0 { width: 95.7%; }
.linea_contenido_1 { width: 93.1%; }
.linea_contenido_2 { width: 90.5%; }
.linea_contenido_3 { width: 87.9%; }
.linea_contenido_4 { width: 85.2%; }
.linea_contenido_5 { width: 82.2%; }
.linea_contenido_6 { width: 80.0%; }

.linea_contenido_0_33 { width: 31.9%; }
.linea_contenido_1_33 { width: 29.1%; }
.linea_contenido_2_33 { width: 26.5%; }
.linea_contenido_3_33 { width: 23.9%; }
.linea_contenido_4_33 { width: 21.2%; }
.linea_contenido_5_33 { width: 18.6%; }
.linea_contenido_6_33 { width: 16.0%; }

.linea_contenido_0_50 { width: 47.8%; }
.linea_contenido_1_50 { width: 45.1%; }
.linea_contenido_2_50 { width: 42.5%; }
.linea_contenido_3_50 { width: 39.9%; }
.linea_contenido_4_50 { width: 37.2%; }
.linea_contenido_5_50 { width: 34.6%; }
.linea_contenido_6_50 { width: 32.0%; }

.linea_contenido_0_66 { width: 63.8%; }
.linea_contenido_1_66 { width: 61.1%; }
.linea_contenido_2_66 { width: 58.5%; }
.linea_contenido_3_66 { width: 55.9%; }
.linea_contenido_4_66 { width: 53.2%; }
.linea_contenido_5_66 { width: 50.6%; }
.linea_contenido_6_66 { width: 48.0%; }

.linea_botones {
    float: right;
    margin: 0px;
    padding: 5px 0 0 0px;
    text-align: right;
    /* display: inline; */
    /* position: absolute; */
    margin-right: 12px;
}

.linea_botones_0 { width: 0px; }
.linea_botones_1 { width: 28px; }
.linea_botones_2 { width: 54px; }
.linea_botones_3 { width: 81px; }
.linea_botones_4 { width: 110px; }
.linea_botones_5 { width: 138px; }
.linea_botones_6 { width: 162px; }

.salto_linea {
    clear: both;
}

.linea_seleccionada {
    background-color: #E2E2E2;
}

.linea_mod {
    background-color: yellow;
}

.linea_baja {
    background-color: #fb6c6c;
}

/* CELDAS */
.celda {
    float: left;
    height: 24px;
    margin: 0px;
    padding: 5px 2px 0px 2px;
    overflow: hidden;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    white-space: nowrap;
}

.celda_auto {
    height: 24px;
    width: auto;
    margin: 0px;
    padding: 5px 2px 0px 2px;
    overflow: hidden;
}

.celda_imagen {
    float: left;
    height: 24px;
    width: 26px;
    margin: 0px;
    padding: 2px 0 0;
}

/* CAMPOS DE FORMULARIOS: INPUTS, CHECKBOXS, TEXTAREAS, SELECTS */
.entrada {
    float: left;
    height: auto;
    margin: 0px;
    display: inline;
    overflow: hidden;
    margin-top:3px;
}

.entrada_text,
.entrada_password,
.entrada_file,
.entrada_textarea,
.entrada_select {
    /*margin-top: 4px;*/
    width: 98%;
    overflow: hidden;
    /*box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;*/
    background-color: #EDEDED;
    border:none;
    border-radius: 3px 3px 3px 3px;
    padding: 7px 5px 7px 5px;
}

.entrada_checkbox {
    width: 13px;
    /* margin: 4px !important; */
    vertical-align: bottom !important;
    margin-top: 7px;
}
.celda .entrada_checkbox { margin-top: 0; }
.entrada_image { clear: both; float: left; }
.entrada_textarea { height: 60px; overflow: visible; }

.resaltado {
    font-size: 16px;
    font-weight: bold;
}

.italica {
    font-style:italic;
    color: #666666;
    font-size: 13px;
}

.alerta {
    font-size: 16px;
    font-weight: bold;
    color: red;
}

.desactivado {
    color: grey;
    text-decoration: line-through;
}

/* PLACEHOLDERS */
::-webkit-input-placeholder { color:#CCC; }
::-moz-placeholder { color:#CCC; } /* firefox 19+ */
:-ms-input-placeholder { color:#CCC; } /* ie */
input:-moz-placeholder { color:#CCC; }


/* CAMPOS DE DATOS: TEXTOS OBSERVACIONES, VIDEOS */
.texto {
    float: left;
    min-height: 18px;
    height: expression( this.scrollHeight < 19 ? "18px" : "auto" );
    padding: 3px 0px;
    display: inline;
    overflow: hidden;
}

.observacion {
    float: left;
    height: auto;
    padding: 3px 0px;
    display: inline;
}

.video {

}

.imagen {
    text-align: center;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
}

.campo_nombre,
.campo_texto,
.campo_observacion {
    font-family: Tahoma;
    font-weight: normal;
    color: #333333;
    font-size: 14px;
}

.campo_nombre {
    color: #656565;
    font-weight: normal;
}

.campo_texto {
    margin-top: 0px;
    /*margin-bottom: 5px;*/
    color: #000000;
}

.enlace {
    text-decoration: none;
}

.enlace:hover {
    text-decoration: underline;
}

/* BUSCADORES */
.buscar_input {
    /*width: 90.0%;*/
    width: 100%;
    height: 35px;
    border: #777 1px solid;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    font-size: 15px;
    text-indent: 10px;
    margin-bottom: 10px;
}

.seleccionar_input,
.seleccionar_input_gratis {
    width: 35.0%;
    height: 20px;
    margin-top: 3px;
    border: #777 1px solid;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    padding-inline: 5px;
}

.buscar_botones {
    /*display: inline;*/
    width: 53px !important;
    /*float: right;*/
}

.buscar_botones img {padding: 0px 0px 0px 0px; margin:0px 0px 0px 0px};

/* SOLAPAS */
ul.solapas {
    padding: 0;
    float: left;
    margin-left:10px;
    padding-left:4px;
    padding-right: 4px;
    min-width: 990px !important;
}

ul.solapas li {
    float: left;
    margin: 0;
    padding: 0;
    height: 31px; /*--Sustrae 1px de la altura de la lista desordenada--*/
    line-height: 31px; /*--Alineamiento vertical del texto dentro de la tabla--*/
    border: 1px solid #999;
    border-left: none;
    margin-bottom: -1px; /*--Desplaza los item de la lista abajo 1px--*/
    overflow: hidden;
    position: relative;
    background: #CCCCCC;
}

ul.solapas li:first-child {border-left: 1px solid #999;}

ul.solapas li a {
    text-decoration: none;
    color: #000;
    display: block;
    padding: 0 20px;
}

ul.solapas li a:hover {
    background: #cccccc;
}

ul.solapas li.active, ul.solapas li.active a:hover  { /*--Estate seguro de que a la tab activa no se le aplicarán estas propiedades hover--*/
    font-weight: bold;
    background: #FFFFFF;
    border-bottom: 1px solid #FFFFFF; /*--Esto hace que la tab activa esté conectada con respecto a su contenido--*/
}

.solapa {
    width: 100%;
}

/* MENSAJES Y ALERTAS FLOTANTES */
#flotante {
    text-align: center;
    float: left;
    position: fixed;
    padding: 0 0 0 0;
    /*padding-top: 15px;
    padding-bottom: 5px;*/
    width: 450px;
    height: auto;
    z-index: 300;
    left: 35%;
    padding-top: 20px;
}

.alerta,
.confirmacion,
.informacion,
.alerta_especifica,
.confirmacion_especifica,
.informacion_especifica {
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
    padding: 4px 4px 4px 4px;
    margin-top: 10px;
    height: auto;
    border: #FFF 1px solid;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
}

.alerta_especifica,
.confirmacion_especifica,
.informacion_especifica { width: 25.0%; }
.alerta,
.alerta_especifica { background-color: #FF2222; }
.confirmacion,
.confirmacion_especifica { background-color: #E3F7DD; }
.informacion,
.informacion_especifica { background-color: #fcf7bd; }

.opciones_flotantes {
    background-color: white;
    text-align: right;
    font-weight: normal;
    position: absolute;
    z-index: 301;
    left: 35%;
    margin-top: 30px;
    padding: 10px;
    line-height: 20px;
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
    border: #FFF 1px solid;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
}

.opciones_flotantes a img {
    padding-bottom: 10px;
}

.cuadro_alerta {
    background-color: #FF2222;
}

.actualizar {
    display: none;
}

.ordenar {
    text-decoration: none;
    color: #000;
}

.realizar {
    display: none;
}

/* TAGS INCLUIDOS EN LAS OBSERVACIONES */

table {
    border-collapse: collapse;
    font-size: 1em;
}

table td {
    /*border: 1px solid #ddd; TODO Sacar este comentario cuando esté terminado el módulo de exportación */
    padding: 5px;
}

table thead td {
    border-bottom: 2px solid #000 !important;
    font-weight: bold;
}
/*Copiado de css estilo viejo de informes */
.t_ventana_cuerpo, .t_ventana_lista {
    width: 98%;
    /*margin-left: 8px;*/
    margin-top: 10px;
    margin-bottom: 0px;
    padding-bottom: 0px;
}

.tr_titulo_lista {
    font-family: Tahoma;
    text-align: left;
    color: #333333;
    font-size: 13px;
    width:160px;
}

.importar{
    border-collapse: collapse;
    white-space: nowrap;
    border: solid 1px;
    padding: 5px;
}

/*Fin de copia de estilo viejo de informes */

code, pre {
    font-family: sans-serif;
}

code {
    background-color: #d8d7d7;
}

pre {
    padding: 1em;
    border: 1px dashed #ccc;
    background: #f5f5f5;
    overflow: auto;
}

hr {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid #ccc;
}

h1, h2, h3, h4,h5 {
    font-weight: bold;
}

h1 {
    font-size: 36px;
    line-height: 40px;
    margin-bottom: 10px;
}

h2 {
    font-size: 30px;
    line-height: 38px;
    margin-bottom: 15px;
}

h3 {
    font-size: 24px;
    line-height: 30px;
    margin-bottom: 10px;
}

h4 {
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 10px;
}

h5 {
    font-size: 1em;
    margin-bottom: 10px;
}

/* BOTONES LATERALES FIJOS SOLO PARA USUARIO DEMO */

#botoneslaterales {
    position: fixed; top: 200px; right: 0px; height: 270px; width: 50px; z-index: 9999;
}

.nuevo-menu, .nuevo-menu:hover {background: url(../images/background_nuevo.png) top left no-repeat !important;}
li .nuevo-menu:hover {background-color: #DDD !important ;}

.nuevo-bg, .nuevo-boton {background: url(../images/nuevo_bg.png) left center no-repeat; padding-left: 50px !important;}
.nuevo-boton {padding-left: 45px !important;}

p.encabezado_empresa small {
    font-weight: normal;
    color: #666666;
    font-size: 12px;
}


/* CONTENIDO DE TOTALES EN LOS COMPROBANTES */

#totales .campo_nombre, #totales .campo_texto {
    font-size: 14px;
}

#totales .texto:last-child {
    padding-top: 20px;
    border-top: solid 2px #ccc;
}

#totales .texto:last-child .campo_nombre, #totales .texto:last-child .campo_texto {
    font-size: 25px;
    font-weight: bold;
    color: #445766;
}

div.modal {overflow: hidden;}
.match {background-color: #CCCCCC}

/* Resoluciones menores a 1050 */
@media (max-width:1050px) {
    .ventana_contenido {overflow-x: scroll;}
    .ventana_contenido::-webkit-scrollbar {height: 6px;}
    .ventana_contenido::-webkit-scrollbar-track {background: none;}
    .ventana_contenido::-webkit-scrollbar-thumb {background: #CCDAE9; border-radius: 2px 2px 2px 2px;}
}

/* Resoluciones menores a 767 */
@media (max-width:767px) {

    #botones_encabezado { margin-top: -35px !important; height: 35px !important;}
    #botones_encabezado img { height: 30px; }
    .encabezado_usuario, .encabezado_fecha {visibility: hidden; display: none;}

    .ventana_inicio {height: auto; padding-left:5px; overflow: hidden;}

    .contenido_100,
    .contenido_66,
    .contenido_50,
    .contenido_33 { clear: both !important; width: 90% !important; }

    .linea_contenido_0 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_1 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_2 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_3 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_4 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_5 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_6 { width: 95.7% !important; clear:both !important; }

    .linea_contenido_0_33 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_1_33 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_2_33 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_3_33 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_4_33 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_5_33 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_6_33 { width: 95.7% !important; clear:both !important; }

    .linea_contenido_0_50 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_1_50 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_2_50 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_3_50 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_4_50 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_5_50 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_6_50 { width: 95.7% !important; clear:both !important; }

    .linea_contenido_0_66 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_1_66 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_2_66 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_3_66 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_4_66 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_5_66 { width: 95.7% !important; clear:both !important; }
    .linea_contenido_6_66 { width: 95.7% !important; clear:both !important; }

    .linea_contenido_3_100 { width: 84.7% !important;}

    /*.celda {clear:both !important;}*/
    .entrada {width: 95.7% !important; clear:both !important;}
    #marco_ventas_mod div.celda, #marco_ventas_mod div.entrada {float:left !important;}

    #clientes_todo, #productos_todo, #proveedores_todo {visibility:hidden; display: none;}

    .linea_titulo, .linea_impar, .linea_par {max-height: 35px; overflow: hidden; position:relative;}
    .linea_titulo {font-size: 12px; font-style: normal;}

/*    .linea_botones_0, .linea_botones_1, .linea_botones_2, .linea_botones_3, .linea_botones_4, .linea_botones_5, .linea_botones_6 {display: none;}*/

    .linea_botones {
        /*position: fixed;*/
        background-color: #fff;
        float: right;
        right: 0;
        margin-right: 15px;
    }

    .buscar_botones {
        /*display: inline;*/
        width: 53px !important;
        margin-top: -40px !important;
        margin-left: 290px;
    }

    #fin p {
        font-size: 14px !important;
    }

    .linea_contenido_3 {
        padding-top: 10px;
    }

    .linea_contenido_3 .campo_texto {
        font-size: 12px !important;
    }

    #flotante {
        width: 250px !important;
        padding-top: 60px !important;
        left: 20% !important;
    }

    #marco_ventas_mod div.entrada {
        height: 80px;
    }

    #ventas_seleccionador .seleccionar_input{
        margin-top: 0px;
    }
}