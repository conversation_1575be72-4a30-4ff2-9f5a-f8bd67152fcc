/* <PERSON>st<PERSON> predeterminado para todos los sistemas de SaaS Argentina */
/* Últimas modificaciones realizadas por desarrollo el día 26-08-2015 */

/* VARIOS PREDETERMINADOS */
body {
    padding: 0 0 0 0;
    background: #7d9fbb;
    background-image: url(../images/fondo.jpg);
    background-attachment: fixed;
    background-repeat: no-repeat;
    /* Lo centro para IE */
    * text-align: center !important;
    width: 100%;
    /* Sigo para los navegadores buenos */
    margin-left: 0px;
    margin-bottom: 0px;
    margin-top: -20px;
    margin-right: 0px;
}

img { border: none; vertical-align: middle; }
form { margin-bottom: 0; }
a { color: #3B5998; }
select { margin: 0px; }

p {
    text-align: left;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: normal;
    font-size: 12px;
}

/* ENCABEZADO */
#contenedor {
    text-align: left;
    margin: auto;
    margin-top: 0px;
    width: 1000px;
    height: auto;
    overflow: hidden;
}

#encabezado {
    background: url(../images/encabezadosaas.png) left no-repeat;
    width: 1000px;
    height: 165px;
}

.encabezado_empresa {
    font-family: Arial, georgia;
    padding-top: 18px;
    padding-left: 20px;
    font-weight: bold;
    font-style: italic;
    font-size: 16px;
    line-height: 13px;
    * padding-top: 23px !important;
}

.encabezado_version,
.encabezado_usuario,
.encabezado_fecha {
    font-size: 12px;
    color: #666666;
    margin-top: -12px;
    padding-left: 20px;
    * margin-top: -15px !important;
}

#botones_encabezado {
    float: right;
    text-align: right;
    margin-top: -75px;
    * margin-top: -90px !important;
    margin-right: 30px;
    width: 400px;
    height: 40px;
}

/* CUERPO */
#cuerpo {
    width: 1000px;
    margin-top: 5px;
    margin-left: 0px;
    min-height: 350px;
    height: expression( this.scrollHeight < 351 ? "350px" : "auto" );
    padding: 0 0 0 0;
}

/* PIE */
#fin {
    width: 100%;
}

.fin {
    color: #CCCCCC;
    border-top: solid;
    border-top-width: 1px;
    border-top-color: #CCCCCC;
    padding-top: 5px;
    text-align: right;
}

.img_rs {
    width: 30px;
}

.subir {
    float: left;
    pointer: cursor;
}

.roll {
    width: 30px;
}

.rrss {
    width: 30px;
}

/* MENU PRINCIPAL */
#menu {
    float: left;
    width: 1000px;
    padding: 0 0 0 0;
    text-align: center;
    * margin-top: 10px !important;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12px;
    color: #999;
    /*
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    box-shadow: 0px 0px 3px #000;
    -webkit-box-shadow: 0px 0px 3px #000;
    -moz-box-shadow: 0px 0px 3px #000;
    background: #E9ECF3;
    border-color: #FFF;
    */
}

/* LISTA DE OPCIONES DEL MENU */
#menu ul {
    list-style: none;
    line-height: 0px;
    text-align: left;
    margin-left: 15px;
    margin-top: 12px;
    padding-left: 0px;
}

#menu li {
    float: left;
    width: 50px;
    list-style: none;
    line-height: 20px;
    margin-left: 15px;
    padding-top: 3px;
    display: inline-block;
    text-decoration: none;
    cursor: default;
    text-align: center;
}

.li_menu_activo {
    text-align: center;
    background-color: #CCC;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    text-decoration: none;
    color: #000;
    font-weight: bold;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
}

.li_menu_hover,
.li_menu_hover {
    text-align: center;
    background-color: #CCC;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    /*
    border-right: #FFF solid 1px;
    * border-left: #000 solid 5px;
    */
    text-decoration: none;
    color: #000;
    font-weight: bold;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
}

.gratis a{
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
}

.gratis {
    opacity: 0.5;
}

#menu .submenu ul{
    list-style: none;
    margin-left: -40px;
    position: absolute;
    padding: 10px 10px 10px 10px;
    padding-left: 0px;
    width: 120px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
    background-color: #FFF;
    z-index: 100;
    text-indent: 0px;
    margin-top: 0px;
    border: #CCC solid 1px;
}

#menu .submenu ul li a,
#menu .submenu ul li a:hover {
    background: none;
    display: block;
    height: 26px;
    border-bottom: #CCC dotted 1px;
    width: 110px;
    text-align: center;
    vertical-align: middle;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    box-shadow: 0px 0px 0px;
    -webkit-box-shadow: 0px 0px 0px;
    -moz-box-shadow: 0px 0px 0px;
    padding: 0px 0px 0px 0px;
    color: #555;
    font-weight: normal;
    text-indent: 0px;
    margin-left: -5px;
    text-decoration: none;
    padding-top: 5px;
    margin-top: -3px;
    overflow:overlay;
}


#menu .submenu .submenu2 ul {
    margin-top: -30px;
    margin-left: 100px;
    width: auto;
    border-radius: 0px 15px 15px 15px;
}

#menu .submenu .submenu2 ul li {
    margin-left: 0px;
    width: auto;
}

#menu .submenu .submenu2 ul li a {
    margin-left: 10px;
    width: 200px;
    display: table;
}

#menu .submenu ul li a:hover,
#menu .li_menu_activo .submenu ul li a:hover {
    background-color: #E5E5E5;
    color: #000;
    cursor: pointer;
}

.menu_titulo {
    display: block;
    height: 8px;
    width: 110px;
    text-align: center;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 13px;
    font-weight: bold;
    padding: 10px;
    color: #000;
}

#menu .submenu ul li a:hover,
#menu .li_menu_activo .submenu ul li a:hover { background-color: #E5E5E5; color: #000;cursor: pointer; }

ul li ul {
    display: none;
}

/* Aca hago que el menú no se mueva en IE8+ */
.li_menu_hover { margin-top: 0px\9; border-top: #666 solid 2px\9; padding-bottom: 3px\9; }

/* MARCOS Y VENTANAS */
.marco {
    width: 1000px;
    background: none;
    font-family: Arial, Helvetica, sans-serif;
    color: #000;
    font-size: 12px;
}

.ventana_100,
.ventana_66,
.ventana_50,
.ventana_33 {
    * float: left !important;
    margin-top: 5px;
}

.ventana_inicio {
    height: 24px;
    padding-top: 10px;
    background-position: top;
    background-repeat: no-repeat;
}

.ventana_contenido {
    width: 100%;
    overflow: hidden;
    padding: 0px 0px 0px 0px;
}

.ventana_fin {
    height: 35px;
}

.ventana_titulo {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 13px;
    font-weight: bold;
    padding-left: 15px;
    color: #333;
}

.titulo_iconito {
    padding-right: 3px;
    padding-bottom: 3px;
}

.ventana_datos {
    float: left;
    margin-right: 15px;
    margin-top: -5px;
    * margin-top: -20px !important;
}

.ventana_botones {
    float: right;
    margin-right: 15px;
    margin-top: -5px;
    * margin-top: -20px !important;
}

.ventana_iconito {
    width: 21px;
    padding-right: 3px;
}

.ventana_100 { width: 1000px; }
.ventana_100 .ventana_inicio { background-image: url(../images/ventana_inicio.png); }
.ventana_100 .ventana_contenido { background: url(../images/ventana_centro.png) top repeat-y; }
.ventana_100 .ventana_fin { background-image: url(../images/ventana_fin.png); }

.ventana_66 { width: 666px; }
.ventana_66 .ventana_inicio { background-image: url(../images/ventana_inicio_666.png); }
.ventana_66 .ventana_contenido { background: url(../images/ventana_centro_666.png) top repeat-y; }
.ventana_66 .ventana_fin { background-image: url(../images/ventana_fin_666.png); }

.ventana_50 { width: 500px; }
.ventana_50 .ventana_inicio { background-image: url(../images/ventana_inicio_50.png); }
.ventana_50 .ventana_contenido { background: url(../images/ventana_centro_50.png) top repeat-y; }
.ventana_50 .ventana_fin { background-image: url(../images/ventana_fin_50.png); }

.ventana_33 { width: 333px; }
.ventana_33 .ventana_inicio { background-image: url(../images/ventana_inicio_33.png); }
.ventana_33 .ventana_contenido { background: url(../images/ventana_centro_33.png) top repeat-y; }
.ventana_33 .ventana_fin { background-image: url(../images/ventana_fin_33.png); }

/* VENTANAS FLOTANTES */
#bloquea {
    display: none;
    width: 100%;
    height: 110%;
    position: fixed;
    z-index: 100;
    background-color: black;
    zoom: 1;
    filter: alpha(opacity=60);
    opacity: 0.6;
}

#actualizando {
    text-align: center;
    display: none;
    z-index: 101;
    zoom: 1;
    filter: alpha(opacity=50);
    opacity: 0.5;
}

#marco_flotante {
    position: fixed;
    display: none;
    z-index: 200;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 12px;
    text-align: left;
}

/* CONTENIDOS */
.contenido_100,
.contenido_66,
.contenido_50,
.contenido_33 {
    float: left;
    margin-left: 5px;
    width: 96%;
    /* clear: both !important; */
    padding: 10px 10px 10px 10px;
    line-height: 20px;
    border-top: dotted 1px #999;
}
.contenido_100 { width: 96%; }
.contenido_66 { width: 62%; }
.contenido_50 { width: 46%; }
.contenido_33 { width: 30%; }

.contenido_titulo {
    * float: left !important;
    * width: 98% !important;
    font-weight: bold;
    font-size: 13px;
    color: #069;
    padding-top: 7px;
    * padding-top: 0px !important;
}

/* BOTONES */
.botones {
    clear: both;
    float: left;
    width: 100%;
    text-align: center;
    padding-top: 15px;
    padding-top: 15px;
}

.boton {
}

/* LÍNEAS */
.linea_titulo,
.linea_impar,
.linea_par,
.linea_subtotal,
.linea_total,
.linea_division,
.linea_agrandar,
.linea_temp,
.linea_contenido {
    width: 100%;
    min-height: 30px;
    height: expression( this.scrollHeight < 31 ? "30px" : "auto" );
    margin: 0px;
    overflow: hidden;
}

.boton-izquierda {
    float: left;
    margin-left: 5px;
}

.linea_titulo {
    border-bottom: #CCC solid 2px;
    font-weight: bold;
}

.linea_impar,
.linea_impar:hover,
.linea_par,
.linea_par:hover,
.linea_temp:hover {
    border-bottom: #CCC dotted 1px;
}

.linea_impar:hover,
.linea_par:hover,
.linea_temp:hover,
.linea_hover {
    background: #E2E2E2;
}

.linea_subtotal {
    border-bottom: #CCC dotted 1px;
}

.linea_total {
    border-bottom: #CCC dotted 2px;
}

.linea_division {
    border-bottom: #CCC dotted 1px;
}

.linea_agrandar {
    border-bottom: #CCC dotted 1px;
    text-align: center;
    min-height: 24px;
    padding: 6px 0 0 0;
}

.linea_temp {

}

.linea_contenido {
    float: left;
}

.linea_contenido_0 { width: 957px; }
.linea_contenido_1 { width: 931px; }
.linea_contenido_2 { width: 905px; }
.linea_contenido_3 { width: 879px; }
.linea_contenido_4 { width: 852px; }
.linea_contenido_5 { width: 822px; }
.linea_contenido_6 { width: 800px; }

.linea_contenido_0_33 { width: 319px; }
.linea_contenido_1_33 { width: 291px; }
.linea_contenido_2_33 { width: 265px; }
.linea_contenido_3_33 { width: 239px; }
.linea_contenido_4_33 { width: 212px; }
.linea_contenido_5_33 { width: 186px; }
.linea_contenido_6_33 { width: 160px; }

.linea_contenido_0_50 { width: 478px; }
.linea_contenido_1_50 { width: 451px; }
.linea_contenido_2_50 { width: 425px; }
.linea_contenido_3_50 { width: 399px; }
.linea_contenido_4_50 { width: 372px; }
.linea_contenido_5_50 { width: 346px; }
.linea_contenido_6_50 { width: 320px; }

.linea_contenido_0_66 { width: 638px; }
.linea_contenido_1_66 { width: 611px; }
.linea_contenido_2_66 { width: 585px; }
.linea_contenido_3_66 { width: 559px; }
.linea_contenido_4_66 { width: 532px; }
.linea_contenido_5_66 { width: 506px; }
.linea_contenido_6_66 { width: 480px; }

.linea_botones {
    float: right;
    margin: 0px;
    padding-top: 3px;
    text-align: right;
    display: inline;
}

.linea_botones_0 { width: 0px; }
.linea_botones_1 { width: 28px; }
.linea_botones_2 { width: 54px; }
.linea_botones_3 { width: 80px; }
.linea_botones_4 { width: 107px; }
.linea_botones_5 { width: 135px; }
.linea_botones_6 { width: 159px; }

.salto_linea {
    clear: both;
}

.linea_seleccionada {
    background-color: #E2E2E2;
}

.linea_mod {
    background-color: yellow;
}

.linea_baja {
    background-color: #fb6c6c;
}

.linea_destacada {
    background: #eee;
    color: #000;
    font-weight: bold;
}

/* CELDAS */
.celda {
    float: left;
    height: 24px;
    margin: 0px;
    padding: 5px 2px 0px 2px;
    overflow: hidden;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    white-space: nowrap;
}

.celda_auto {
    height: 24px;
    width: auto;
    margin: 0px;
    padding: 5px 2px 0px 2px;
    overflow: hidden;
}

.celda_imagen {
    float: left;
    height: 24px;
    width: 26px;
    margin: 0px;
    padding: 2px 0 0;
}

.alineacion-derecha {
    text-align: right;
}

/* CAMPOS DE FORMULARIOS: INPUTS, CHECKBOXS, TEXTAREAS, SELECTS */
.entrada {
    float: left;
    height: auto;
    margin: 0px;
    display: inline;
    overflow: hidden;
}

.entrada_text,
.entrada_password,
.entrada_file,
.entrada_textarea,
.entrada_select {
    margin-top: 4px;
    width: 100%;
    overflow: hidden;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.entrada_checkbox {
    width: auto;
    margin: 4px !important;
    vertical-align: bottom !important;
}
.celda .entrada_checkbox { margin-top: 0; }
.entrada_image { clear: both; float: left; }
.entrada_textarea { height: 60px; overflow: visible; }

.resaltado {
    font-size: 16px;
    font-weight: bold;
}

.italica {
    font-style: italic;
    color: grey;
}

.alerta {
    font-size: 16px;
    font-weight: bold;
    color: red;
}

.desactivado {
    color: grey;
    text-decoration: line-through;
}

/* PLACEHOLDERS */
::-webkit-input-placeholder { color:#CCC; }
::-moz-placeholder { color:#CCC; } /* firefox 19+ */
:-ms-input-placeholder { color:#CCC; } /* ie */
input:-moz-placeholder { color:#CCC; }


/* CAMPOS DE DATOS: TEXTOS OBSERVACIONES, VIDEOS */
.texto {
    float: left;
    min-height: 18px;
    height: expression( this.scrollHeight < 19 ? "18px" : "auto" );
    padding: 3px 0px;
    display: inline;
    overflow: hidden;
}

.observacion {
    float: left;
    height: auto;
    padding: 3px 0px;
    display: inline;
}

.video {

}

.imagen {
    text-align: center;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
}

.campo_nombre,
.campo_nombre_unico,
.campo_texto,
.campo_observacion {
    font-family: Arial, Helvetica, sans-serif;
    font-weight: normal;
    color: #000;
    font-size: 12px;
}

.campo_nombre,
.campo_nombre_unico {
    color: #333333;
    font-weight: bold;
}

.campo_nombre_unico {
    float: left;
    margin-top: 4px;
}

.campo_texto {
    margin-top: 0px;
    margin-bottom: 5px;
}

.enlace {
    text-decoration: none;
}

.linea_completa {
    margin-top: 5px;

}

.linea_completa a:link {
    text-decoration: none;
}

.enlace:hover {
    text-decoration: underline;
}

/* BUSCADORES */
.buscar_input {
    width: 900px;
    height: 20px;
    border: #777 1px solid;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
}

.seleccionar_input,
.seleccionar_input_gratis {
    width: 350px;
    height: 20px;
    margin-top: 3px;
    border: #777 1px solid;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    padding-inline: 5px;
}

.buscar_botones {
    display: inline;
}

/* SOLAPAS */
ul.solapas {
    margin: 2px 0 0 15px;
    padding: 0;
    float: left;
    list-style: none;
    height: 32px; /*--Define el ancho de las tabs--*/
    border-left: 1px solid #999;
    border-right: none;
    width: 990px;
}

ul.solapas li {
    float: left;
    margin: 0;
    padding: 0;
    height: 31px; /*--Sustrae 1px de la altura de la lista desordenada--*/
    line-height: 31px; /*--Alineamiento vertical del texto dentro de la tabla--*/
    border: 1px solid #999;
    border-left: none;
    margin-bottom: -1px; /*--Desplaza los item de la lista abajo 1px--*/
    overflow: hidden;
    position: relative;
    background: #e0e0e0;
}

ul.solapas li a {
    text-decoration: none;
    color: #000;
    display: block;
    padding: 0 20px;
    border: 1px solid #fff;
}

ul.solapas li a:hover {
    background: #ccc;
}

ul.solapas li.active, ul.solapas li.active a:hover  { /*--Estate seguro de que a la tab activa no se le aplicarán estas propiedades hover--*/
    font-weight: bold;
    background: #F7F8FA;
    border-bottom: 1px solid #F7F8FA; /*--Esto hace que la tab activa esté conectada con respecto a su contenido--*/
}

.solapa {
    width: 100%;
}

/* MENSAJES Y ALERTAS FLOTANTES */
#flotante {
    text-align: center;
    float: left;
    position: fixed;
    padding: 0 0 0 0;
    /*padding-top: 15px;
    padding-bottom: 5px;*/
    width: 450px;
    height: auto;
    z-index: 300;
    left: 35%;
    padding-top: 20px;
}

.alerta,
.confirmacion,
.informacion,
.alerta_especifica,
.confirmacion_especifica,
.informacion_especifica {
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
    padding: 4px 4px 4px 4px;
    margin-top: 10px;
    height: auto;
    border: #FFF 1px solid;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
}

.alerta_especifica,
.confirmacion_especifica,
.informacion_especifica { width: 250px; }
.alerta,
.alerta_especifica { background-color: #FF2222; }
.confirmacion,
.confirmacion_especifica { background-color: #E3F7DD; }
.informacion,
.informacion_especifica { background-color: #fcf7bd; }

.opciones_flotantes {
    background-color: white;
    text-align: right;
    font-weight: normal;
    position: absolute;
    z-index: 301;
    left: 35%;
    margin-top: 30px;
    padding: 10px;
    line-height: 20px;
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
    border: #FFF 1px solid;
    box-shadow: 0px 0px 5px #999;
    -webkit-box-shadow: 0px 0px 5px #999;
    -moz-box-shadow: 0px 0px 5px #999;
}

.opciones_flotantes a img {
    padding-bottom: 10px;
}

.cerrar_mensaje_flotante {
  display: inline;
  float: right;
}

.cuadro_alerta {
    background-color: #FF2222;
}

.actualizar {
    display: none;
}

.ordenar {
    text-decoration: none;
    color: #000;
}

.realizar {
    display: none;
}

/* TAGS INCLUIDOS EN LAS OBSERVACIONES */

table {
    border-collapse: collapse;
    font-size: 1em;
}

table td {
    /*border: 1px solid #ddd; TODO Sacar este comentario cuando esté terminado el módulo de exportación */
    padding: 5px;
}

table thead td {
    border-bottom: 2px solid #000 !important;
    font-weight: bold;
}
/*Copiado de css estilo viejo de informes */
.t_ventana_cuerpo, .t_ventana_lista {
    width: 98%;
    /*margin-left: 8px;*/
    margin-top: 10px;
    margin-bottom: 0px;
    padding-bottom: 0px;
}

.tr_titulo_lista {
    font-family: "Arial", Helvetica, sans-serif;
    text-align: left;
    font-weight: bold;
    color: #333333;
    font-size: 12px;
    width:160px;
}

.importar{
    border-collapse: collapse;
    white-space: nowrap;
    border: solid 1px;
    padding: 5px;
}

/*Fin de copia de estilo viejo de informes */

code, pre {
    font-family: sans-serif;
}

code {
    background-color: #d8d7d7;
}

pre {
    padding: 1em;
    border: 1px dashed #ccc;
    background: #f5f5f5;
    overflow: auto;
}

hr {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid #ccc;
}

h1, h2, h3, h4,h5 {
    font-weight: bold;
}

h1 {
    font-size: 36px;
    line-height: 40px;
    margin-bottom: 10px;
}

h2 {
    font-size: 30px;
    line-height: 38px;
    margin-bottom: 15px;
}

h3 {
    font-size: 24px;
    line-height: 30px;
    margin-bottom: 10px;
}

h4 {
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 10px;
}

h5 {
    font-size: 1em;
    margin-bottom: 10px;
}

/* BOTONES LATERALES FIJOS SOLO PARA USUARIO DEMO */

#botoneslaterales {
    position: fixed; top: 200px; right: 0px; height: 270px; width: 50px; z-index: 9999;
}

.nuevo-menu,
.nuevo-menu:hover {
  background-image: url(../images/background_nuevo.png) !important;
  background-position: top left;
  background-repeat: no-repeat !important;
}
li .nuevo-menu:hover {
  background-color: #ddd !important ;
}
.entrada.nuevo-menu, .texto.nuevo-menu {padding-top:10px; padding-left:15px !important;}

.nuevo-bg,
.nuevo-boton {
  background-image: url(../images/nuevo_bg.png);
  background-position: left center;
  background-repeat: no-repeat;
  padding-left: 55px !important;
}
.nuevo-boton {
  padding-left: 45px !important;
}

p.encabezado_empresa small {
    font-weight: normal;
    color: #666666;
    font-size: 12px;
}


/* CONTENIDO DE TOTALES EN LOS COMPROBANTES */

#totales .campo_nombre, #totales .campo_texto {
    font-size: 14px;
}

#totales .texto:last-child {
    padding-top: 20px;
    border-top: solid 2px #ccc;
}

#totales .texto:last-child .campo_nombre, #totales .texto:last-child .campo_texto {
    font-size: 25px;
    font-weight: bold;
    color: #069;
}

.desglose {
    float: right;
    margin-right: 15px;
    text-align: right;
  }

.match {background-color: #CCCCCC}

#mailing_cliente_saldo
{
    width: 50px;
}

main {
    display: flex;
    flex-direction: column;
}

.droparea {
    margin: 1rem auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 100%;
    height: 160px;
    border: 1px dashed grey;
    border-radius: 15px;
    padding-top: 20px;
}

.droparea i {
    font-size: 3rem;
    flex-grow: 1;
    padding-top: 1rem;
}

.droparea-border {
    border-color: #069;
}

.droparea img {
    width: 50px;
    height: auto;
}

.droparea p small:hover {
    cursor: pointer;
}