/* Nuevo estilo tipo responsive etapas DEV */

/* VARIOS PREDETERMINADOS */
body {
  padding: 0px 0px 0px 0px;
  margin: 0px 0px 0px 0px;
  background-color: #0C4D59;
  font-family: tahoma, arial;
}

img {
  border: none;
  vertical-align: middle;
}
form {
  margin-bottom: 0;
}
a {
  color: #2d5ca3;
}
select {
  margin: 0px;
}

p {
  text-align: left;
  font-family: tahoma, arial;
  font-weight: normal;
  font-size: 15px;
}

/* ENCABEZADO */
#contenedor {
  padding: 0px 0px 0px 0px;
  margin: 0px 0px 0px 0px;
  /* margin-top: -16px; */
  text-align: left;
  height: auto;
  overflow: hidden;
}

#encabezado {
  padding: 0px 0px 0px 0px;
  margin: px 0px 0px 0px;
  background: #ffffff;
  height: auto;
/*  border-radius: 0px 0px 5px 5px;
  border-bottom: 2px solid #cccccc;*/
}

.encabezado_empresa {
  font-family: Arial, georgia;
  padding-top: 18px;
  padding-left: 20px;
  margin-bottom: 0px;
  font-weight: bold;
  font-style: italic;
  font-size: 16px;
  line-height: 13px;
}

.encabezado_version,
.encabezado_usuario,
.encabezado_fecha {
  font-size: 12px;
  color: #666666;
  margin-top: 2px;
  margin-bottom: 0px;
  padding-top: 0px;
  padding-left: 20px;
}

#botones_encabezado {
  position: relative;
  text-align: right;
  margin-top: -70px;
  height: 70px;
  margin-right: 10px;
  z-index: 301;
}

#botones_encabezado a img {
  background-color: #FFFFFF;
  border-radius: 50%;
  margin-right: 2px;
  height: 40px;
  width: 40px;
  border:1px #D6D6D6 solid;
}

.encabezado_marca {
  background: url(../images/saas-vertical-menu.png) no-repeat center center;
}

.encabezado_marca_dark {
  background: url(../images/saas-vertical-menu-dark.png) no-repeat center center;
}

.encabezado_marca, .encabezado_marca_dark {
  background-size: 100px 100px;
  float: left;
  height: 100px;
  width: 100px;
}

/* Resoluciones menores a 1050 */
@media (max-width: 1050px) {
  .encabezado_marca {
    background: url(../images/saas-horizontal-menu.png) no-repeat center center;
  }

  .encabezado_marca_dark {
    background: url(../images/saas-horizontal-menu-dark.png) no-repeat center center;
  }

  .encabezado_marca, .encabezado_marca_dark {
    background-size: 130px 55px;
    float: left;
    margin-top: 3px;
    margin-left: 4px;
    height: 55px;
    width: 130px;
  }
}

/* CUERPO */
#cuerpo {
  margin-top: 5px;
  margin-left: 0px;
  min-height: 350px;
  height: expression(this.scrollHeight < 351 ? "350px": "auto");
  padding: 0 0 0 0;
}

/* PIE */
#fin {
  /*width: 100%;*/
}

.fin {
  padding: 15px;
  padding-bottom: 15px;
  text-align: right;
  font-size: 11px;
  color: #DEEBF1;
}

.fin-espacio {
  height: 120px;
  float: right;
  margin-right: 0px;
  margin-top:-20px;
}

.img_rs {
  width: 30px;
}

.subir {
  float: left;
  pointer: cursor;
}

.roll {
  width: 30px;
}

/* MENU PRINCIPAL */
#menu {
  height: auto;
  min-height: 50px;
  padding: 0px 0px 0px 0px;
  text-align: center;
  margin-top: 10px;
  font-size: 12px;
  color: #999;
  overflow: hidden !important;
  border-top: 1px solid #dcdcdc;
  background-color: #f1f1f1;
  /*border-radius: 0px 0px 5px 5px;*/
}

/* LISTA DE OPCIONES DEL MENU */
#menu ul {
  list-style: none;
  line-height: 0px;
  text-align: left;
  margin-left: 0px;
  margin-top: 0px;
  padding-left: 0px;
}

#menu li {
  float: left;
  width: 80px;
  list-style: none;
  line-height: 20px;
  margin-left: 0px;
  padding-top: 0px;
  display: inline-block;
  text-decoration: none;
  cursor: default;
  text-align: center;
  transition-duration:.5s;
}

#menu a img {
  margin-top: 0px;
  border-radius: 0%;
  margin-right: 0px;
  height: 50px;
  width: 50px;
}

.li_menu_activo {
  text-align: center;
  background-color: #cccccc;
  text-decoration: none;
  font-weight: bold;
  border-top:2px solid #5696A6;
}

.li_menu_hover {
  text-align: center;
  background-color: #cccccc;
  text-decoration: none;
  color: #333333;
  font-weight: bold;
  transition-duration:.5s;
}

li.li_menu:hover {padding-bottom: 0px !important;}

.gratis a {
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
}

.gratis {
  opacity: 0.5;
}

img.ad_feg_fin {width: 100%; margin-top: 20px;}
#botones_encabezado > a.ad_feg_encabezado > img,
#botones-encabezado-menu > a.ad_feg_encabezado > img {
  width: 100% !important;
  max-width: 229px;
  max-height: 32px;
  border-radius: 0px !important;}

#menu .submenu ul {
  list-style: none;
  margin-left: -30px;
  position: absolute;
  padding: 10px 10px 10px 10px;
  padding-left: 0px;
  width: 140px;
  background-color: #fff;
  z-index: 100;
  text-indent: 0px;
  margin-top: 0px;
  border: #ccc solid 1px;
}

#menu .submenu ul li a,
#menu .submenu ul li a:hover {
  background: none;
  display: block;
  height: 35px;
  border-bottom: #ccc dotted 1px;
  width: 120px;
  text-align: center;
  vertical-align: middle;
  color: #555;
  font-weight: normal;
  text-indent: 0px;
  margin-left: 10px;
  text-decoration: none;
  padding-top: 5px;
  margin-top: 0px;
}

#menu .submenu .submenu2 ul {
  margin-top: -30px;
  margin-left: 120px;
  width: auto;
  border-radius: 0px 15px 15px 15px;
}

#menu .submenu .submenu2 ul li {
  margin-left: 0px;
  width: auto;
}

#menu .submenu .submenu2 ul li a {
  margin-left: 10px;
  width: 200px;
  display: table;
}

.menu_titulo {
  display: block;
  height: 8px;
  width: 100%;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
  padding: 10px 10px 10px 20px;
  color: #365E80;
  height: 25px;
}

#menu .submenu ul li a:hover,
#menu .li_menu_activo .submenu ul li a:hover {
  background-color: #cccccc;
  color: #333333;
  cursor: pointer;
}

ul li ul {
  display: none;
}

/* Aca hago que el menú no se mueva en IE8+ */
.li_menu_hover {
  margin-top: 0px\9;
  border-top: #666 solid 2px\9;
  padding-bottom: 3px\9;
}

/* MARCOS Y VENTANAS */
.marco {
  background: none;
  color: #2a2a2a;
  font-size: 14px;
}

.ventana_100,
.ventana_66,
.ventana_50,
.ventana_33 {
  margin-top: 25px;
}

.ventana_inicio {
    margin-top:10px;
    height: auto;
    min-height: 58px;
    padding-top:15px !important;
    padding-left:15px !important;
    /*background: url('../images/bg-ventanainicio.jpg') top left no-repeat;*/
    border-top:2px solid #5888BB;
}

.ventana_contenido {
  width: 100%;
  overflow: hidden;
}

.ventana_fin {
  height: 15px;
  /*border-bottom: 1px solid #cccccc;*/
}

.ventana_titulo {
  /*font-family: tahoma, arial;*/
  font-size: 16px;
  padding-left: 5px;
  /*font-weight: bold;*/
  color: #365E80;
}

.titulo_iconito, .desplegable_ver img {
  border: 2px solid #FFFFFF;
  margin-top: -3px;
  position: relative;
  background-color: #FFFFFF;
  border-radius: 0% 50% 50% 0%;
}

.ventana_datos {
  float: left;
  margin-right: 15px;
  margin-top: -5px;
}

.ventana_botones {
  float: right;
  margin-right: 10px;
  background: none;
}

.ventana_iconito {
  padding: 1px;
}

.ventana_100 {
  width: 100%;
}
.ventana_100 .ventana_inicio {
  /*background-color: #f1f1f1;*/
  /*border-radius: 5px 5px 0px 0px;
  height: 35px;*/
}
.ventana_100 .ventana_contenido {
  background-color: #ffffff;
}
.ventana_100 .ventana_fin {
  background-color: #ffffff;
  border-radius: 0px 0px 0px 0px;
}

.ventana_66 {
  width: 100%;
}
.ventana_66 .ventana_inicio {
  background-color: #f1f1f1;
  height: 32px;
}
.ventana_66 .ventana_contenido {
  background-color: #ffffff;
}
.ventana_66 .ventana_fin {
  background-color: #ffffff;
}

.ventana_50 {
  width: 100%;
}
.ventana_50 .ventana_inicio {
  background-color: #f1f1f1; /*border-radius: 5px 5px 0px 0px;*/
}
.ventana_50 .ventana_contenido {
  background-color: #ffffff;
}
.ventana_50 .ventana_fin {
  background-color: #ffffff; /*border-radius:0px 0px 5px 5px;*/
}

.ventana_33 {
  width: 100%;
}
.ventana_33 .ventana_inicio {
  background-color: #f1f1f1; /*border-radius: 5px 5px 0px 0px;*/
}
.ventana_33 .ventana_contenido {
  background-color: #ffffff;
}
.ventana_33 .ventana_fin {
  background-color: #ffffff; /*border-radius:0px 0px 5px 5px;*/
}

/* VENTANAS FLOTANTES */
#bloquea {
  display: none;
  width: 100%;
  height: 110%;
  position: fixed;
  z-index: 304 !important;
  background-color: black;
  zoom: 1;
  filter: alpha(opacity=60);
  opacity: 0.6;
}

#actualizando {
  text-align: center;
  display: none;
  z-index: 101;
  zoom: 1;
  filter: alpha(opacity=50);
  opacity: 0.5;
}

#marco_flotante {
  position: fixed;
  display: none;
  z-index: 305;
  font-size: 13px;
  text-align: left;
  width: 80%;
}

.modal {
  display: inline;
  position: relative;
  /*top: 381.5px;
  left: 250px;*/
  width: 80% !important;
  height: 100% !important;
  top: 0%;
  left: 0%;
  background-color: none;
}

.modal .ventana_fin {box-shadow: none;}

/* CONTENIDOS */
.contenido_100,
.contenido_66,
.contenido_50,
.contenido_33 {
  float: left;
  margin: 10px 0px 5px 2px;
  padding: 5px 15px 5px 15px;
  line-height: 19px;
  /*  border-left: dotted 1px #b6b6b6;*/
}
.contenido_100 {
  width: 100%;
}
.contenido_66 {
  width: 62%;
}
.contenido_50 {
  width: 46%;
}
.contenido_33 {
  width: 30%;
}

#marco_flotante .contenido_100 {line-height: 18px !important;}

/* Le quito la linea si esta al final del ventana_contenido */
div.ventana_contenido > div.contenido_100:last-child,
div.ventana_contenido > div.contenido_66:last-child,
div.ventana_contenido > div.contenido_50:last-child,
div.ventana_contenido > div.contenido_33:last-child {
  border-bottom: none;
  width: 100%;
}

.contenido_titulo {
  font-size: 14px;
  background-color: #DCECF1;
  display: inline-block;
  margin-left: -10px;
  margin-bottom: 3px;
  margin-top: 0px;
  color: #365E80;
  padding: 5px 10px 5px 15px;
  border-radius: 0px 3px 0px 0px;
  letter-spacing: 0.5px;
}

/* BOTONES */
.botones {
  clear: both;
  float: left;
  width: 100%;
  text-align: center;
  padding-top: 15px;
  padding-bottom: 15px;
}

.boton {
  background: #e1e1e1;
  padding: 10px 18px 10px 18px;
  font-size: 16px;
  border-radius: 3px;
  border: none;
}
.boton:hover {
  background: #445766;
  color: #ffffff;
}

.boton-izquierda {
  float: left;
  margin-left: 5px;
}

input[type=submit]:disabled {
  background: #e1e1e1;
  color: grey;
  cursor:not-allowed;
}

/* LÍNEAS */
#marco_flotante div.linea_titulo,
#marco_flotante div.linea_impar,
#marco_flotante div.linea_par {
  min-width: auto;
}

.linea_titulo,
.linea_impar,
.linea_par,
.linea_subtotal,
.linea_total,
.linea_division,
.linea_agrandar,
.linea_temp,
.linea_contenido {
  width: 100%;
  min-height: 35px;
  /*height: expression(this.scrollHeight < 36 ? "35px": "auto");*/
  margin: 0px;
  overflow: hidden;
}

.linea_titulo,
.linea_impar,
.linea_par {
  min-width: 990px;
}

.linea_titulo {
  border-bottom: #ccc solid 2px;
  font-weight: bold;
}

.linea_impar,
.linea_impar:hover,
.linea_par,
.linea_par:hover,
.linea_temp:hover {
  border-bottom: #ccc dotted 1px;
  padding-left: 5px;
}

.linea_impar:hover,
.linea_par:hover,
.linea_temp:hover,
.linea_hover {
  background: #D6E0E7 !important;
  transition-duration: 0.4s;
}

.linea_impar,
.linea_par,
.linea_temp {
  background: none;
  transition-duration: 0.4s;
}

.linea_subtotal {
  border-bottom: #ccc dotted 1px;
}

.linea_total {
  border-bottom: #ccc dotted 2px;
}

.linea_division {
  border-bottom: #ccc dotted 1px;
}

.linea_agrandar {
  border-bottom: #ccc dotted 1px;
  text-align: center;
  min-height: 24px;
  padding: 6px 0 0 0;
}

.linea_temp {
}

.linea_contenido {
  float: left;
}

.linea_contenido_0 {
  width: 95.7%;
}
.linea_contenido_1 {
  width: 93.1%;
}
.linea_contenido_2 {
  width: 90.5%;
}
.linea_contenido_3 {
  width: 87.9%;
}
.linea_contenido_4 {
  width: 85.2%;
}
.linea_contenido_5 {
  width: 82.2%;
}
.linea_contenido_6 {
  width: 80%;
}

.linea_contenido_0_33 {
  width: 31.9%;
}
.linea_contenido_1_33 {
  width: 29.1%;
}
.linea_contenido_2_33 {
  width: 26.5%;
}
.linea_contenido_3_33 {
  width: 23.9%;
}
.linea_contenido_4_33 {
  width: 21.2%;
}
.linea_contenido_5_33 {
  width: 18.6%;
}
.linea_contenido_6_33 {
  width: 16%;
}

.linea_contenido_0_50 {
  width: 47.8%;
}
.linea_contenido_1_50 {
  width: 45.1%;
}
.linea_contenido_2_50 {
  width: 42.5%;
}
.linea_contenido_3_50 {
  width: 39.9%;
}
.linea_contenido_4_50 {
  width: 37.2%;
}
.linea_contenido_5_50 {
  width: 34.6%;
}
.linea_contenido_6_50 {
  width: 32%;
}

.linea_contenido_0_66 {
  width: 63.8%;
}
.linea_contenido_1_66 {
  width: 61.1%;
}
.linea_contenido_2_66 {
  width: 58.5%;
}
.linea_contenido_3_66 {
  width: 55.9%;
}
.linea_contenido_4_66 {
  width: 53.2%;
}
.linea_contenido_5_66 {
  width: 50.6%;
}
.linea_contenido_6_66 {
  width: 48%;
}

.linea_botones {
  float: right;
  margin: 0px;
  padding: 5px 0 0 0px;
  text-align: right;
  /* display: inline; */
  /* position: absolute; */
  margin-right: 12px;
}

.linea_botones_0 {
  width: 0px;
}
.linea_botones_1 {
  width: 28px;
}
.linea_botones_2 {
  width: 54px;
}
.linea_botones_3 {
  width: 81px;
}
.linea_botones_4 {
  width: 110px;
}
.linea_botones_5 {
  width: 138px;
}
.linea_botones_6 {
  width: 162px;
}

.salto_linea {
  clear: both;
}

.linea_seleccionada {
  background-color: #e2e2e2;
}

.linea_mod {
  background-color: yellow;
}

.linea_baja {
  background-color: #fb6c6c;
}

.linea_destacada {
  font-weight: bold;
  border-radius: 0px 0px 5px 5px;
  background: #f1f1f1;
}

/* CELDAS */
.celda {
  float: left;
  height: 24px;
  margin: 0px;
  padding: 5px 2px 0px 2px;
  overflow: hidden;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  white-space: nowrap;
}

.celda_auto {
  height: 24px;
  width: auto;
  margin: 0px;
  padding: 5px 2px 0px 2px;
  overflow: hidden;
}

.celda_imagen {
  float: left;
  height: 24px;
  width: 26px;
  margin: 0px;
  padding: 2px 0 0;
}

.alineacion-derecha {
  text-align: right;
}

/* CAMPOS DE FORMULARIOS: INPUTS, CHECKBOXS, TEXTAREAS, SELECTS */
.entrada {
  float: left;
  height: auto;
  margin: 0px;
  display: inline;
  overflow: hidden;
  margin-top: 3px;
}

.entrada_text,
.entrada_password,
.entrada_file,
.entrada_textarea,
.entrada_select {
  /*margin-top: 4px;*/
  height: 30px;
  width: 98%;
  overflow: hidden;
  /*box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;*/
  background-color: #ededed;
  border: none;
  border-radius: 3px 3px 3px 3px;
  /*border-color: #dedede;*/
  padding: 5px 5px 5px 5px;
}

label {
  margin-top: 5px;
  margin-bottom: 2px;
  margin-left: 2px;
}

input[type="text"]:disabled.entrada_text {
  background-color:#ffe9e9;
  color:#a0a0a0;
}

.entrada_checkbox {
  width: 13px;
  /* margin: 4px !important; */
  vertical-align: bottom !important;
  margin-top: 7px;
}
.celda .entrada_checkbox {
  margin-top: 0;
}
.entrada_image {
  clear: both;
  float: left;
}
.entrada_textarea {
  height: 60px;
  overflow: visible;
}

.resaltado {
  font-size: 16px;
  font-weight: bold;
}

.italica {
  font-style: italic;
  color: #666666;
  font-size: 13px;
}

.alerta {
  font-size: 16px;
  font-weight: bold;
  color: red;
}

.desactivado {
  color: grey;
  text-decoration: line-through;
}

/* PLACEHOLDERS */
::-webkit-input-placeholder {
  color: #ccc;
}
::-moz-placeholder {
  color: #ccc;
} /* firefox 19+ */
:-ms-input-placeholder {
  color: #ccc;
} /* ie */
input:-moz-placeholder {
  color: #ccc;
}

/* CAMPOS DE DATOS: TEXTOS OBSERVACIONES, VIDEOS */
.texto {
  float: left;
  min-height: 18px;
  height: expression(this.scrollHeight < 19 ? "18px": "auto");
  padding: 3px 0px;
  display: inline;
  overflow: hidden;
}

.observacion {
  float: left;
  height: auto;
  padding: 3px 0px;
  display: inline;
  margin-top: 14px;
}

.video {
}

.imagen {
  text-align: center;
  box-shadow: 0px 0px 5px #999;
  -webkit-box-shadow: 0px 0px 5px #999;
  -moz-box-shadow: 0px 0px 5px #999;
}

.campo_nombre,
.campo_nombre_unico,
.campo_texto,
.campo_observacion {
  font-family: tahoma, arial;
  font-weight: normal;
  color: #333333;
  font-size: 14px;
}

.campo_nombre,
.campo_nombre_unico {
  color: #656565;
  font-weight: normal;
}

.campo_nombre_unico {
  float: left;
  margin-top: 4px;
}

.campo_texto {
  margin-top: 0px;
  color: #3b3b3b;
}

#ML_ventas_okautorizado, #ML_ventas_noautorizado, #MP_pagos_okautorizado, #MP_pagos_noautorizado {
  display: inline-block;
  margin-top: 10px;
}

.enlace {
  text-decoration: none;
}

.enlace:hover {
  text-decoration: underline;
}

/* BUSCADORES */
.buscar_input {
  /*width: 90.0%;*/
  width: 100%;
  height: 35px;
  border: #777 1px solid;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  font-size: 15px;
  text-indent: 10px;
  margin-bottom: 10px;
  margin-top: 10px;
}

.seleccionar_input,
.seleccionar_input_gratis {
  width: 35%;
  height: 30px;
  /*margin-top: 3px;*/
  border: #777 1px solid;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  padding-inline: 5px;
}

#ventas_input.seleccionar_input,
#ventas_input.seleccionar_input_gratis {
  height: 30px;
  margin-top: 0px;
}

.buscar_botones {
  display: inline;
  width: 53px !important;
  /*float: right;*/
}

.buscar_botones img {
  padding: 0px 0px 0px 0px;
  margin: 0px 0px 0px 0px;
}

/* SOLAPAS */
ul.solapas {
  margin-top:20px;
  padding: 0;
  float: left;
  margin-left: 10px;
  padding-left: 4px;
  padding-right: 4px;
  min-width: 990px !important;
}

ul.solapas li {
  float: left;
  margin: 0;
  padding: 0;
  height: 31px; /*--Sustrae 1px de la altura de la lista desordenada--*/
  line-height: 31px; /*--Alineamiento vertical del texto dentro de la tabla--*/
  border: 1px solid #999;
  border-left: none;
  margin-bottom: -1px; /*--Desplaza los item de la lista abajo 1px--*/
  overflow: hidden;
  position: relative;
  background: #cccccc;
}

ul.solapas li:first-child {
  border-left: 1px solid #999;
}

ul.solapas li a {
  text-decoration: none;
  color: #000;
  display: block;
  padding: 0 20px;
}

ul.solapas li a:hover {
  background: #cccccc;
}

ul.solapas li.active,
ul.solapas li.active a:hover {
  /*--Estate seguro de que a la tab activa no se le aplicarán estas propiedades hover--*/
  font-weight: bold;
  background: #ffffff;
  border-bottom: 1px solid #ffffff; /*--Esto hace que la tab activa esté conectada con respecto a su contenido--*/
}

.solapa {
  width: 100%;
}

/* MENSAJES Y ALERTAS FLOTANTES */
#flotante {
  text-align: center;
  float: left;
  position: fixed;
  padding: 0 0 0 0;
  /*padding-top: 15px;
    padding-bottom: 5px;*/
  width: 450px;
  height: auto;
  z-index: 305;
  left: 35%;
  padding-top: 20px;
}

#flotante-mobile {
  text-align: center;
  height: auto;
}

.ayuda-flotante {
  position: fixed;
  bottom: 10px;
  right: 150px;
  width: 35px;
}

.alerta,
.confirmacion,
.informacion,
.alerta_especifica,
.confirmacion_especifica,
.informacion_especifica {
  /*-moz-border-radius: 8px;
  -webkit-border-radius: 8px;
  border-radius: 8px;*/
  padding: 10px 10px 10px 10px;
  margin-bottom: 5px;
  height: auto;
  /*border: #fff 1px solid;*/
  box-shadow: 0px 0px 5px #999;
  -webkit-box-shadow: 0px 0px 5px #999;
  -moz-box-shadow: 0px 0px 5px #999;
}

.alerta_especifica,
.confirmacion_especifica,
.informacion_especifica {
  width: 50%;
}

.alerta,
.alerta_especifica {
  background-color: #FF6565;
}
.confirmacion,
.confirmacion_especifica {
  background-color: #e3f7dd;
}
.informacion,
.informacion_especifica {
  background-color: #fcf7bd;
}

.opciones_flotantes {
  background-color: white;
  text-align: right;
  font-weight: normal;
  position: absolute;
  z-index: 301;
  left: 35%;
  margin-top: 30px;
  padding: 10px;
  line-height: 20px;
  -moz-border-radius: 8px;
  -webkit-border-radius: 8px;
  border-radius: 8px;
  border: #fff 1px solid;
  box-shadow: 0px 0px 5px #999;
  -webkit-box-shadow: 0px 0px 5px #999;
  -moz-box-shadow: 0px 0px 5px #999;
}

.opciones_flotantes a img {
  padding-bottom: 10px;
}

#flotante div.informacion_especifica {
  box-shadow: none !important;
  background: #FFFF;
  border-radius: 5px;
  border:2px solid #0C4D59;
  padding: 5px;
}



.cerrar_mensaje_flotante {
  display: inline;
  float: right;
}

.cuadro_alerta {
  background-color: #ff2222;
}

.actualizar {
  display: none;
}

.ordenar {
  text-decoration: none;
  color: #000;
}

.realizar {
  display: none;
}

/* TAGS INCLUIDOS EN LAS OBSERVACIONES */

table {
  border-collapse: collapse;
  font-size: 1em;
}

table td {
  /*border: 1px solid #ddd; TODO Sacar este comentario cuando esté terminado el módulo de exportación */
  padding: 5px;
}

table thead td {
  border-bottom: 2px solid #000 !important;
  font-weight: bold;
}
/*Copiado de css estilo viejo de informes */
.t_ventana_cuerpo,
.t_ventana_lista {
  width: 98%;
  /*margin-left: 8px;*/
  margin-top: 10px;
  margin-bottom: 0px;
  padding-bottom: 0px;
}

.tr_titulo_lista {
  font-family: tahoma, arial;
  text-align: left;
  color: #333333;
  font-size: 13px;
  width: 160px;
}

.importar {
  border-collapse: collapse;
  white-space: nowrap;
  border: solid 1px;
  padding: 5px;
}

/*Fin de copia de estilo viejo de informes */

code,
pre {
  font-family: sans-serif;
}

code {
  background-color: #d8d7d7;
}

pre {
  padding: 1em;
  border: 1px dashed #ccc;
  background: #f5f5f5;
  overflow: auto;
}

hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
}

h1,
h2,
h3,
h4,
h5 {
  font-weight: bold;
}

h1 {
  font-size: 36px;
  line-height: 40px;
  margin-bottom: 10px;
}

h2 {
  font-size: 30px;
  line-height: 38px;
  margin-bottom: 15px;
}

h3 {
  font-size: 24px;
  line-height: 30px;
  margin-bottom: 10px;
}

h4 {
  font-size: 18px;
  line-height: 24px;
  margin-bottom: 10px;
}

h5 {
  font-size: 1em;
  margin-bottom: 10px;
}

/* BOTONES LATERALES FIJOS SOLO PARA USUARIO DEMO */

#botoneslaterales {
  position: fixed;
  top: 200px;
  right: 0px;
  height: 270px;
  width: 50px;
  z-index: 9999;
}

.nuevo-menu,
.nuevo-menu:hover {
  background-image: url(../images/background_nuevo.png) !important;
  background-position: top left;
  background-repeat: no-repeat !important;
}
li .nuevo-menu:hover {
  background-color: #ddd !important ;
}
.entrada.nuevo-menu, .texto.nuevo-menu {padding-top:10px; padding-left:15px !important;}

.nuevo-bg,
.nuevo-boton {
  background-image: url(../images/nuevo_bg.png);
  background-position: left center;
  background-repeat: no-repeat;
  padding-left: 55px !important;
}
.nuevo-boton {
  padding-left: 45px !important;
}

p.encabezado_empresa small {
  font-weight: normal;
  color: #666666;
  font-size: 12px;
}

/* CONTENIDO DE TOTALES EN LOS COMPROBANTES */

#totales .campo_nombre,
#totales .campo_texto {
  font-size: 14px;
}

#totales .texto:last-child {
  padding-top: 20px;
  border-top: solid 2px #ccc;
}

#totales .texto:last-child .campo_nombre,
#totales .texto:last-child .campo_texto {
  font-size: 25px;
  font-weight: bold;
  color: #445766;
}

.desglose {
  float: right;
  margin-right: 15px;
  text-align: right;
}

div.modal {
  overflow: hidden;
}

.match {
  background-color: #C4D9D9;
}

.ventana_inicio {
  height: auto;
  min-height:55px;
  background-color: #CFE1E6 !important;
  border-top:0px;
}

.ventana_inicio .ventana_titulo img {
  float:left;
  background-color:#FFFFFF;
  border-radius: 10% 10% 0% 0%;
  height: 65px;
  width: 65px;
  margin-left: -5px;
  margin-right: 10px;
  margin-top: -25px;
}

.ventana_inicio .ventana_titulo {padding:0px;}

.ventana_botones {
    float: right;
    margin-right:10px;
    margin-top:-2px;
    padding: 8px 8px 10px 8px;
    text-align: right;
    background-color: #FFFFFF;
    border-radius: 10px 10px 0px 0px;
}

.container-fluid {
  padding: 0px;
  width: 1200px;
}

/* IMPORTACION */

#importar-2-form .entrada_select {
  margin-top: -4px;
}

/* MENU MOBILE */

.topnav {
    overflow: hidden;
    background-color: #f1f1f1;
    position: relative;
    height: auto;
  }

  .topnav #menu {
    display: none;
  }

  .topnav a {
    padding: 10px 16px;
    text-decoration: none;
    font-size: 14px;
    color: #3b3b3b;
    display: block;
  }

  .topnav .icon {
    position: absolute;
    right: 5px;
    top: 10px;
  }

  .topnav a:hover {
    background-color: #ddd;
  }

  .topnav .active {
    background-color: #f1f1f1;
  }

  .topnav #menu {
    margin-top: 0px;
    text-align: unset !important;
    padding-bottom: 15px !important;
  }

  .topnav .submenu {
    margin-left: 30px;
    border-left: 1px dotted #b0d1f4;
  }

  .topnav .submenu2 {
    margin-left: 30px;
    border-left: 1px dotted #cde3fa;
  }

  .topnav .submenu2 a {
    color: #555;
  }

  .topnav #menu a img {
    margin-top: -4px;
    background-color: #FFFFFF;
    border-radius: 50%;
    margin-right: 10px;
    height: 40px;
    width: 40px;
    border:1px #D6D6D6 solid;
  }

  .topnav .saas-horizontal-menu img {
    width: 100px;
  }

  .topnav #botones-encabezado-menu {
    padding-left: 15px;
    background-color: #d6d6d6;
    display: inline-flex;
    width: 100%;
  }

  .topnav #botones-encabezado-menu img {background: none !important; padding:0px !important;}

  .topnav #botones-encabezado-menu a {
    padding: 10px 5px 0px 0px;
  }

  .topnav #encabezado-empresa-menu-fin {
    background-color: #d6d6d6;
    color: #505050;
    margin: -15px !important;
    padding: 10px;
  }

/* FIN MENU MOBILE */

.container-fluid {width: 100%;}

/* Resoluciones menores a 1050 */
@media (max-width: 1050px) {

  #marco_flotante div.linea_titulo,
  #marco_flotante div.linea_impar,
  #marco_flotante div.linea_par {
    min-width: 990px;
  }

  #cuerpo {background-color: #FFFFFF;}

  .ventana_100,
  .ventana_66,
  .ventana_50,
  .ventana_33 {
    margin-top: 16px;
  }

  .ventana_contenido {
    overflow-x: scroll;
  }
  .ventana_contenido::-webkit-scrollbar {
    height: 5px;
  }
  .ventana_contenido::-webkit-scrollbar-track {
    background: none;
  }
  .ventana_contenido::-webkit-scrollbar-thumb {
    background: #E7E7E7;
    border: none !important;
    border-radius: 4px;
  }

  .container-fluid {
    padding: 0px;
    width: 100%;
  }

  #botones_encabezado {
    margin-top: -35px !important;
    height: 35px !important;
  }
  #botones_encabezado img {
    height: 30px;
  }
  .encabezado_usuario,
  .encabezado_fecha {
    visibility: hidden;
    display: none;
  }

 /* .ventana_inicio {
    height: auto;
    min-height:57px;
    padding-top:12px !important;
    padding-left:10px !important;
    padding-right: 20px;
    background-color: #CFE1E6 !important;
    margin-top: 10px;
    border-top:2px solid #5696A6;
  }

  .ventana_inicio .ventana_titulo img {
    float:left;
    background:none !important;
    padding: 0px;
    height: 50px;
    width: 50px;
    margin-left: -5px;
    margin-right: 0px;
    margin-top: -15px;
  }
*/


.ventana_inicio {
  height: auto;
  min-height:45px;
  background-color: #CFE1E6 !important;
  border-top:1px solid #5696A6;
}

.ventana_inicio .ventana_titulo img {
  float:left;
  background-color:#FFFFFF;
  border-radius: 10% 10% 0% 0%;
  height: 55px;
  width: 55px;
  margin-left: -5px;
  margin-right: 10px;
  margin-top: -23px;
}


  .ventana_inicio .ventana_titulo {padding:0px;}

  .ventana_botones {
    float: right;
    margin-left:-65px;
    margin-right:0px;
    margin-top:-10px;
    padding-right: 10px;
    padding: 5px 5px 5px 10px;
    text-align: right;
    border-bottom: 1px solid #BEBEBE;
    background-color: #F9F9F9;
    border-radius: 10px 0px 0px 10px;
  }

  .ventana_botones a img {
    padding-right: 3px;
  }

  #form_Ver_desglose_de_totales .ventana_botones {
    margin-top: 0px;
    height: 41px !important;
  }

/*  .form_compras .ventana_botones {
    margin-top: -45px;
    margin-right: 1px;
  }

  .form_ventas .ventana_botones {
    margin-top: -45px;
    margin-right: 1px;
  }*/

  .ventana_100 .ventana_inicio {
    height: auto !important;
  }

  .ventana_100 .ventana_inicio,
  .ventana_100 .ventana_fin {
    border-radius: 0px 0px 0px 0px;
  }

  .ventana_fin {
    border-radius: none !important;
    box-shadow: none !important;
  }

  .contenido_100,
  .contenido_66,
  .contenido_50,
  .contenido_33 {
    clear: both !important;
    width: 99% !important;
  }

  .contenido_100 .texto {width: 48% !important;}

  /*.celda {clear:both !important;}*/
  .entrada {
    width: 95.7% !important;
    /*clear: both !important;*/
  }

  .linea_contenido .entrada {
    width: 6% !important;
  }

  #listado .entrada {
    width: unset !important;
  }

  #marco_ventas_mod div.celda,
  #marco_ventas_mod div.entrada {
    float: left !important;
    /*margin-left: 5px;*/
  }

  #marco_ventas_mod div.entrada {
    /*height: 80px;*/
  }

  #clientes_todo,
  #productos_todo,
  #proveedores_todo {
    visibility: hidden;
    display: none;
  }

  .linea_titulo,
  .linea_impar,
  .linea_par {
    max-height: 35px;
    overflow: hidden;
  }

  .linea_titulo {
    position: relative;
  }

  .linea_titulo {
    font-size: 12px;
    font-style: normal;
  }

  #localidades_seleccionador {
    width: 220px;
  }

  .linea_botones {
    /*position: fixed;
    background-color: #fff;*/
    float: right;
    right: 0;
    margin-right: 15px;
  }

  .buscar_botones {
    display: none;
  }

  #fin p {
    font-size: 12px !important;
  }

  img.ad_feg_fin {margin-top: 5px;}

  /*.linea_contenido_3 {
    padding-top: 10px;
  }*/

  .linea_contenido_3 .campo_texto {
    font-size: 12px !important;
  }



  #ventas_seleccionador .seleccionar_input {
    margin-top: 0px;
  }

  .modal {
    overflow-y: scroll;
    background: #FFFFFF;
    display: block !important;
    height: auto !important;
    top: 20px;
    left: 0px !important;
    width: 96% !important;
    margin-left: 2%;
    max-height: 90%;
  }



  .mobile-container {
    max-width: auto;
    margin: auto;
  }

  .opciones_flotantes {
    z-index: 305;
    position: fixed;
    left: 10% !important;
    margin-top: -50% !important;
    top: 50% !important;
    text-align: left;
    padding: 10px;
  }

  .linea_botones .opciones_flotantes, .marco .opciones_flotantes{
    /*position: absolute;*/
    margin: 0 !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%);
    width: 300px;
  }

  .opciones_flotantes a {
    display: inline-block;
    color:#2a2a2a;
    margin-top:2px;
  }

  .opciones_flotantes a img {float:left; margin-bottom:5px; margin-right: 10px;}

  .buscar_resultados {
    margin-top: 25px;
  }

  .seleccionar_input {
    height: 27px;
  }

  .entrada_text {
    /*width: 50px;*/
    width: 100%;
  }

  input[name="cuit"], input[name="dni"], input[name="ML_user_id2"], input[name="ML_user_id"] {
    text-align: unset !important;
  }

  .form_configuraciones .linea_titulo {
    max-height: none;
  }

  .form_configuraciones .entrada_select {
    width: auto;
  }

  #importar-2-form .entrada{
    width: 60% !important;
    float: right;
  }
  #importar-2-form .celda{
    height: unset;
    padding-top: 8px;
  }

  #linea_agregar {
    max-height: none;
  }

  #linea_agregar .entrada {
    width: auto !important;
  }

  #linea_agregar .val_class_cantidad {
    width: 70px !important;
    /*margin-left: -10px;*/
  }

  #linea_agregar .seleccionar_input {
    width: 310px !important;

  }
  #linea_agregar .idiva {
  }

  #linea_agregar .val_class_precio {
    width: 80px !important;
  }

  #linea_agregar .val_class_idiva {
    width: 60px !important;
  }

  #linea_agregar .val_class_preciofinal {
    width: 80px !important;
  }

  #linea_agregar .val_class_descuento {
    width: 80px !important;
  }

  #linea_agregar .linea_contenido {
    display: flex;
  }

  #totales {
    text-align: right !important;
  }

  .ayuda-flotante {
    display: scroll;
    position: fixed;
    bottom: 10px;
    right: 10px;
    width: 35px;
  }

  #logo-mercadolibre {
    width: 150px;
  }

  #logo-mercadopago {
    width: 150px;
    padding-top: 25px;
    float: right;
  }

  #marco_flotante {width: 100%;}

  #marco_flotante .ventana_botones {
    margin-top: -30px;
    background-color: unset;
    background: none;
    box-shadow: none;
  }

  a object{
    pointer-events: none;
  }

  /*#fin {padding-left: 15px; padding-top:10px;}
  #fin p {text-align: left !important; text-indent: 10px; color:#d51c1d;}
  #fin p a > img {width: 25px;}*/

  /* PARA ACTIVAR MODO OSCURO */
  /*#cuerpo {filter:invert(1);}
  #cuerpo img {filter:invert(1);}
  #cuerpo .ventana_inicio .ventana_botones img {filter:invert(0) !important;}
  .contenido_titulo {filter:invert(1); color:#FFFFFF !important; background-color: #595959;}
  .entrada_text, .entrada_password, .entrada_file, .entrada_textarea, .entrada_select {filter:invert(1); background-color: #595959; color:#FFFFFF;}*/


} /*FIN de personalización para resoluciones menores a 1050 */

/* Resoluciones menores a 650 */
@media (max-width: 650px) {
.contenido_100 .texto {width: 99% !important;}

#flotante {
    position: initial;
    padding-top: 0px !important;
    width: 100% !important;
    float: none;
  }

.informacion_especifica {
    width: auto;
    left: 0 !important;
    top: 0 !important;
  }

} /*FIN de personalización para resoluciones menores a 650 */


#mailing_cliente_saldo
{
    width: 50px;
}

main {
  display: flex;
  flex-direction: column;
}

.droparea {
  margin: 1rem auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 100%;
  height: 160px;
  border: 1px dashed grey;
  border-radius: 15px;
  padding-top: 20px;
}

.droparea i {
  font-size: 3rem;
  flex-grow: 1;
  padding-top: 1rem;
}

.droparea-border {
  border-color: #069;
}

.droparea img {
  width: 50px;
  height: auto;
}

.droparea p small:hover {
  cursor: pointer;
}

