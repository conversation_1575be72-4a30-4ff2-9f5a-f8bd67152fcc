ALTER TABLE `tienda`
    ADD `tienda_idtipoventa_a` tinyint(3) UNSIGNED NOT NULL
        AFTER `tienda_idtipoventa`,
    ADD `tienda_idtipoventa_b` tinyint(3) UNSIGNED NOT NULL
        AFTER `tienda_idtipoventa_a`;

UPDATE tienda
    SET tienda_idtipoventa_b = tienda_idtipoventa
    WHERE tienda_idtipoventa > 0
        AND EXISTS (
            SELECT 1
            FROM configuraciones
            WHERE configuraciones.idtipoiva = 1
        );
