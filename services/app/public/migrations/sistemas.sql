SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

CREATE TABLE `abonos` (
  `idabono` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `tiporelacion` enum('tipocliente', 'multiple', 'unica') NOT NULL,
  `idtipocliente`  smallint(3) unsigned NOT NULL,
  `idtipoventa` tinyint(1) unsigned NOT NULL,
  `descuento` decimal(4,2) unsigned NOT NULL,
  `idplantilla` smallint(5) unsigned NOT NULL,
  PRIMARY KEY (`idabono`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `abonosxclientes` (
  `idabono` smallint(5) unsigned NOT NULL,
  `idcliente` int(10) unsigned NOT NULL,
  UNIQUE KEY `idabono+idcliente` (`idabono`,`idcliente`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `archivos` (
  `idarchivo` int(10) unsigned NOT NULL auto_increment,
  `iua` char(20) NOT NULL,
  `tipo` enum('enlace','imagen','procesador','texto','planilla','pdf','archivo') NOT NULL DEFAULT 'archivo',
  `modulo` enum('bienes','clientes','comunicaciones','conocimientos','productos','proveedores','servicios','ventas','compras','ventaspagos','compraspagos','trazabilidades','tickets') NOT NULL,
  `id` int(10) unsigned NOT NULL,
  `publico` tinyint(1) unsigned NOT NULL,
  `nombre` varchar(60) NOT NULL,
  `archivo` varchar(255) NOT NULL,
  `idusuario` int(10) unsigned NOT NULL,
  `fecha` datetime NOT NULL,
  `url` varchar(2083) NOT NULL,
  `bytes` float unsigned NOT NULL,
  PRIMARY KEY (`idarchivo`),
  INDEX `modulo+id` (`modulo`, `id`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `automatizaciones` (
  `idautomatizacion` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) NOT NULL DEFAULT '1',
  `tipoautomatizacion` enum('abono','aviso','avisovencimiento','avisocumpleaños','avisosaldo','avisopendiente','intereses','servicioprogramado','mantenimiento') NOT NULL,
  `idrelacion` mediumint(8) unsigned NOT NULL,
  `nombre` varchar(200) NOT NULL,
  `codigo` varchar(60) NOT NULL,
  `observacion` text NOT NULL,
  `fechainicio` date NOT NULL,
  `fechafin` date NOT NULL,
  `dia` tinyint(2) unsigned NOT NULL,
  `mes` tinyint(2) unsigned NOT NULL,
  `año` smallint(4) unsigned NOT NULL,
  `diadesemana` char(7) NOT NULL DEFAULT '0000000',
  PRIMARY KEY (`idautomatizacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `bienes` (
  `idbien` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `idtipobien` tinyint(3) unsigned NOT NULL,
  `idcliente` int(10) unsigned NOT NULL,
  `estado` tinyint(1) NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `codigo` varchar(60) NOT NULL,
  `observacion` text NOT NULL,
  `obsinterna` text NOT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`idbien`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `bienesxservicios` (
  `idbienxservicio` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idservicio` mediumint(8) unsigned NOT NULL,
  `idbien` smallint(5) unsigned NOT NULL,
  `nombre` varchar(200) NOT NULL,
  `fechainicio` datetime NOT NULL,
  `fechafin` datetime NOT NULL,
  PRIMARY KEY (`idbienxservicio`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `cajas` (
  `idcaja` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idtipocaja` smallint(5) unsigned NOT NULL,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `fechaapertura` datetime NOT NULL,
  `fechacierre` datetime NOT NULL,
  `saldoapertura` decimal(15,2) NOT NULL,
  `saldocierre` decimal(15,2) NOT NULL,
  `observacion` text NULL,
  PRIMARY KEY (`idcaja`),
  INDEX `idtipocaja+idcaja` (`idtipocaja`, `idcaja`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `cajas` (`idcaja`, `idtipocaja`, `idmoneda`, `fechaapertura`) VALUES
(1, 1, 1, NOW()),
(2, 2, 1, NOW()),
(3, 3, 1, NOW()),
(4, 4, 1, NOW());

CREATE TABLE `categorias_bienes` (
  `idtipobien` smallint(3) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `idtipobienpadre` smallint(3) unsigned NOT NULL,
  `padres` tinyint(1) unsigned NOT NULL,
  PRIMARY KEY (`idtipobien`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_bienes` (`idtipobien`, `estado`, `nombre`, `idtipobienpadre`, `padres`) VALUES
(0, 1, 'Sin especificar', 0, 0);

CREATE TABLE `categorias_cajas` (
  `idtipocaja` smallint(3) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(60) NOT NULL,
  `compartida` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `estado` tinyint(1) unsigned NOT NULL,
  `idcaja` int unsigned NOT NULL,
  `tipo` enum('efectivo', 'banco', 'cheque', 'retencion') NOT NULL DEFAULT 'efectivo',
  PRIMARY KEY (`idtipocaja`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_cajas` (`idtipocaja`, `nombre`, `compartida`, `estado`, `idcaja`, `tipo`) VALUES
(1, 'Caja principal', 1, 1, 1, 'efectivo'),
(2, 'Banco', 1, 1, 2, 'banco'),
(3, 'Cartera de cheques', 1, 1, 3, 'cheque'),
(4, 'Retenciones', 1, 1, 4, 'retencion');

CREATE TABLE `categorias_clientes` (
  `idtipocliente` smallint(3) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `idlista` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `cuentacorriente` tinyint(3) unsigned NOT NULL,
  `maxcuentacorriente` decimal(15,2) unsigned NOT NULL,
  `pagacosto` tinyint(1) unsigned NOT NULL,
  `descuento` decimal(5,2) unsigned NOT NULL,
  `condicion` enum('contado','cuentacorriente','debito','credito','cheque','ticket','transferencia','otros_electronicos','otra') NOT NULL DEFAULT 'contado',
  PRIMARY KEY (`idtipocliente`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_clientes` (`idtipocliente`, `estado`, `nombre`, `idlista`, `cuentacorriente`, `maxcuentacorriente`, `pagacosto`, `descuento`) VALUES
(0, 1, 'Sin especificar', 1, 0, '0.00', 0, '0.00'),
(1, 1, 'Habitual', 1, 1, '50000.00', 0, '0.00');

CREATE TABLE `categorias_comunicaciones` (
  `idtipocomunicacion` smallint(3) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `idtipocomunicacionpadre` smallint(3) unsigned NOT NULL,
  `padres` tinyint(1) unsigned NOT NULL,
  `enviamail` tinyint(1) unsigned NOT NULL,
  PRIMARY KEY (`idtipocomunicacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `categorias_conceptos` (
  `idconcepto` smallint(3) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `idconceptopadre` smallint(3) NOT NULL,
  `padres` tinyint(1) unsigned NOT NULL,
  PRIMARY KEY (`idconcepto`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_conceptos` (`idconcepto`, `estado`, `nombre`, `idconceptopadre`, `padres`) VALUES
(0, 1, 'Sin especificar', 0, 0),
(1, 1, 'Ventas', 0, 0),
(2, 1, 'Compras', 0, 0),
(3, 1, 'Gastos', 0, 0),
(4, 1, 'Fletes', 3, 1),
(5, 1, 'Insumos', 3, 1),
(6, 1, 'Impuestos', 3, 1),
(7, 1, 'Alquiler', 6, 2),
(8, 1, 'Luz', 6, 2),
(9, 1, 'Gas', 6, 2),
(10, 1, 'Comunicaciones', 6, 2),
(11, 1, 'Contables', 6, 2),
(12, 1, 'Sueldos', 3, 1),
(13, 1, 'Empleados', 12, 2),
(14, 1, 'Retiro socios', 12, 2),
(15, 1, 'Publicidad', 3, 1),
(16, 1, 'Varios', 0, 0),
(17, 1, 'Ajuste', 0, 0),
(18, 1, 'Transferencias', 17, 1),
(19, 1, 'Préstamos', 17, 1),
(20, 1, 'Faltantes', 17, 1);

CREATE TABLE `categorias_conocimientos` (
  `idtipoconocimiento` smallint(3) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `idtipoconocimientopadre` smallint(3) NOT NULL,
  `padres` tinyint(1) unsigned NOT NULL,
PRIMARY KEY (`idtipoconocimiento`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_conocimientos` (`idtipoconocimiento`, `estado`, `nombre`, `idtipoconocimientopadre`, `padres`) VALUES
(0, 1, 'Sin especificar', 0, 0);

CREATE TABLE `categorias_localidades` (
  `idlocalidad` smallint(3) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `idprovincia` tinyint(1) unsigned NOT NULL,
  `codigopostal` char(4) NOT NULL,
  PRIMARY KEY (`idlocalidad`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_localidades` (`idlocalidad`, `estado`, `nombre`, `idprovincia`) VALUES
(0, 1, 'Sin especificar', 0);

CREATE TABLE `categorias_proveedores` (
  `idtipoproveedor` smallint(3) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `iddeposito` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `idtipoproveedorpadre` smallint(3) NOT NULL,
  `padres` tinyint(1) unsigned NOT NULL,
  `condicion` enum('contado', 'cuentacorriente') NOT NULL DEFAULT 'contado',
  PRIMARY KEY (`idtipoproveedor`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_proveedores` (`idtipoproveedor`, `estado`, `nombre`, `iddeposito`, `idtipoproveedorpadre`, `padres`) VALUES
(0, 1, 'Sin especificar', 1, 0, 0);

CREATE TABLE `categorias_respuestasxcomunicaciones` (
  `idtiporespuestaxcomunicacion` tinyint(3) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(100) NOT NULL,
  `idtiporespuestaxcomunicacionpadre` smallint(3) unsigned NOT NULL,
  `padres` tinyint(1) unsigned NOT NULL,
  PRIMARY KEY (`idtiporespuestaxcomunicacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_respuestasxcomunicaciones` (`idtiporespuestaxcomunicacion`, `estado`, `nombre`, `idtiporespuestaxcomunicacionpadre`, `padres`) VALUES
(0, 1, 'Sin especificar', 0, 0);

CREATE TABLE `categorias_rubros` (
  `idrubro` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `idrubropadre` mediumint(8) unsigned NOT NULL,
  `padres` tinyint(1) unsigned NOT NULL,
  PRIMARY KEY `idrubro` (`idrubro`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_rubros` (`idrubro`, `estado`, `nombre`, `idrubropadre`, `padres`) VALUES
(0, 1, 'Sin especificar', 0, 0);

CREATE TABLE `categorias_servicios` (
  `idtiposervicio` smallint(3) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `idtiposerviciopadre` smallint(3) NOT NULL,
  `padres` tinyint(1) unsigned NOT NULL,
  PRIMARY KEY (`idtiposervicio`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_servicios` (`idtiposervicio`, `estado`, `nombre`, `idtiposerviciopadre`, `padres`) VALUES
(0, 1, 'Sin especificar', 0, 0);

CREATE TABLE `categorias_tributos` (
  `idtributo` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idtipotributo` int(11) unsigned NOT NULL,
  `arba` tinyint(1) unsigned NOT NULL DEFAULT 0,
  `nombre` varchar(50) NOT NULL,
  `auto` enum ('', '5329') NOT NULL DEFAULT '',
  PRIMARY KEY (`idtributo`),
  INDEX `auto` (`auto`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `categorias_ventas` (
  `idtipoventa` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `idcomportamiento` tinyint(1) unsigned NOT NULL,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `letra` char(1) NOT NULL,
  `puntodeventa` mediumint(5) unsigned NOT NULL,
  `ultimonumero` int(9) unsigned NOT NULL,
  `ultimo_idventa_aprobado` mediumint(8) unsigned NOT NULL DEFAULT 0,
  `discrimina` char(1) NOT NULL,
  `muevestock` tinyint(1) unsigned NOT NULL,
  `muevesaldo` tinyint(1) unsigned NOT NULL,
  `operacioninversa` tinyint(1) unsigned NOT NULL,
  `unificar_productos` tinyint(1) unsigned NOT NULL DEFAULT 0,
  `tienesituacion` tinyint(1) unsigned NOT NULL,
  `situacion` enum('no_utilizar', 'sin_especificar', 'pendiente', 'aprobado', 'rechazado') NOT NULL DEFAULT 'sin_especificar',
  `auto_aprobar` tinyint(1) unsigned NOT NULL,
  `tipofacturacion` enum('interno', 'manual', 'electronico', 'enlinea') NOT NULL DEFAULT 'interno',
  `tipoimpresion` enum('predeterminado', 'ticket', 'viejo') NOT NULL DEFAULT 'predeterminado',
  `iddeposito` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `deposito_venta_relacionada` tinyint(1) unsigned NOT NULL,
  `idusuario` smallint(1) NOT NULL DEFAULT '-1',
  `telefonos` varchar(150) NOT NULL,
  `mail` varchar(150) NOT NULL,
  `domicilio` varchar(150) NOT NULL,
  `idlocalidad` smallint(3) unsigned NOT NULL,
  `estilo_venta` char(20) NOT NULL,
  `estilo_venta_pdf` char(20) NOT NULL,
  `observacion` TEXT NOT NULL,
  `obsinterna` TEXT NOT NULL,
  PRIMARY KEY (`idtipoventa`),
  INDEX `categorias_ventas_idx_tienesituacion_idtipoventa` (`tienesituacion`,`idtipoventa`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `cheques` (
  `idcheque` int NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) NOT NULL,
  `tipo` enum('propio','tercero')  NOT NULL,
  `fechacobro` date NOT NULL,
  `idbanco` int NULL,
  `numero` varchar(36) NOT NULL,
  `titular` varchar(60) NOT NULL,
  `idmensaje` int NOT NULL,
  PRIMARY KEY (`idcheque`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `clientes` (
  `idcliente` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idtipocliente` tinyint(1) unsigned NOT NULL,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `idusuario` smallint(5) unsigned NOT NULL,
  `nombre` varchar(60) NOT NULL,
  `contacto` varchar(150) NOT NULL,
  `telefonos` varchar(150) NOT NULL,
  `telefonos_verificado` tinyint(1) unsigned NOT NULL,
  `telefonos_codigo` char(5) NOT NULL,
  `domicilio` varchar(150) NOT NULL,
  `idlocalidad` smallint(3) unsigned NOT NULL,
  `mail` varchar(320) NOT NULL,
  `idtipoiva` tinyint(1) unsigned NOT NULL,
  `razonsocial` varchar(150) NOT NULL,
  `cuit` bigint(11) unsigned NOT NULL,
  `tipodoc` tinyint(3) unsigned NOT NULL DEFAULT '96',
  `dni` int(8) unsigned NOT NULL,
  `pass` char(32) NOT NULL,
  `ML_user_id` bigint(10) unsigned NOT NULL,
  `ML_user_id2` bigint(10) unsigned NOT NULL,
  `ML_nickname` varchar(60) NOT NULL,
  `MP_external_reference` char(20) NOT NULL,
  `random` char(50) NOT NULL,
  `observacion` text NOT NULL,
  `obsinterna` text NOT NULL,
  `obsrecordatorio` text NOT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`idcliente`),
  INDEX `mail` (`mail`),
  INDEX `ML_user_id` (`ML_user_id`),
  INDEX `idusuario` (`idusuario`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `clientes` (`idcliente`, `idtipocliente`, `idmoneda`, `estado`, `nombre`, `contacto`, `telefonos`, `domicilio`, `idlocalidad`, `mail`, `idtipoiva`, `razonsocial`, `cuit`, `pass`, `observacion`, `obsinterna`, `obsrecordatorio`) VALUES
(1, 0, 1, 1, 'Consumidor final', '', '', '', 0, '', 0, '', 0, '', '', '', '');

CREATE TABLE `compras` (
  `idcompra` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idtipocompra` tinyint(1) unsigned NOT NULL,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `estado` enum('abierto','cerrado','anulado','borrado') NOT NULL DEFAULT 'abierto',
  `situacion` enum('sin_especificar', 'pendiente', 'aprobado', 'rechazado') NOT NULL DEFAULT 'sin_especificar',
  `idusuario` smallint(5) unsigned NOT NULL,
  `idproveedor` smallint(5) unsigned NOT NULL,
  `iddeposito` tinyint(1) unsigned NOT NULL,
  `discrimina` char(1) NOT NULL,
  `tiporelacion` enum('servicio') DEFAULT NULL,
  `idrelacion` mediumint(8) unsigned NOT NULL,
  `condicionventa` enum('contado','cuentacorriente','debito','credito','cheque','ticket','transferencia','otros_electronicos','otra') NOT NULL DEFAULT 'contado',
  `fecha` datetime NOT NULL,
  `fechaimputacion` date NOT NULL,
  `vencimiento1` date NOT NULL,
  `vencimiento2` date NOT NULL,
  `numerocompleto` varchar(60) NOT NULL,
  `idcomportamiento` tinyint(1) unsigned NOT NULL,
  `puntodeventa` mediumint(5) unsigned NOT NULL,
  `numero` int(9) unsigned NOT NULL,
  `subtotal` decimal(15,2) unsigned NOT NULL,
  `descuento` decimal(4,2) unsigned NOT NULL,
  `neto` decimal(15,2) unsigned NOT NULL,
  `nogravado` decimal(15,2) unsigned NOT NULL,
  `exento` decimal(15,2) unsigned NOT NULL,
  `iva` decimal(15,2) unsigned NOT NULL,
  `tributos` decimal(15,2) unsigned NOT NULL,
  `total` decimal(15,2) unsigned NOT NULL,
  `observacion` text NOT NULL,
  `idtipoiva` tinyint(1) unsigned NOT NULL,
  `cuit` bigint(11) unsigned NOT NULL,
  `razonsocial` varchar(150) NOT NULL,
  `domicilio`varchar(150) NOT NULL,
  `idlocalidad` smallint(3) unsigned NOT NULL,
  `muevesaldo` tinyint(1) unsigned NOT NULL,
  `muevestock` tinyint(1) unsigned NOT NULL,
  `operacioninversa` tinyint(1) unsigned NOT NULL,
  `esfiscal` tinyint(1) unsigned NOT NULL,
  `closed_at` timestamp NOT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`idcompra`),
  INDEX `tiporelacion+idrelacion` (`tiporelacion`, `idrelacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `compraspagos` (
  `idcomprapago` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idproveedor` smallint(5) unsigned NOT NULL,
  `idusuario` smallint(5) unsigned NOT NULL,
  `idcompra` mediumint(8) unsigned NOT NULL,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `idformapago` tinyint(1) unsigned NOT NULL,
  `tiporelacion` enum('', 'cheque', 'retencion') NOT NULL,
  `idnumerocomprapago` mediumint (8) NOT NULL,
  `idrelacion` int NOT NULL,
  `fecha` datetime NOT NULL,
  `total` decimal(15,2) NOT NULL,
  `observacion` text NOT NULL,
  `closed_at` timestamp NOT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`idcomprapago`),
  INDEX `idcompra` (`idcompra`),
  INDEX `tiporelacion+idrelacion` (`tiporelacion`, `idrelacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `comprasxcompras` (
  `idcompra` mediumint(8) unsigned NOT NULL,
  `idrelacion` mediumint(8) unsigned NOT NULL,
  UNIQUE KEY `idcompra` (`idcompra`,`idrelacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `comprasxproveedores` (
  `idcompraxproveedor` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idproveedor` smallint(5) unsigned NOT NULL,
  `idtipocompra` tinyint(1) signed NOT NULL,
  `id` mediumint(8) unsigned NOT NULL,
  `fecha` datetime NOT NULL,
  `total` decimal(15,2) NOT NULL,
  `numero` char(16) NOT NULL,
  PRIMARY KEY (`idcompraxproveedor`),
  INDEX `idproveedor` (`idproveedor`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `comunicaciones` (
  `idcomunicacion` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL,
  `prioridad` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `idtipocomunicacion` tinyint(3) unsigned NOT NULL,
  `idcliente` int(10) unsigned NOT NULL DEFAULT '1',
  `idusuario` smallint(5) unsigned NOT NULL,
  `fechainicio` datetime NOT NULL,
  `fechafin` datetime NOT NULL,
  `obssolicitado` text NOT NULL,
  `obsrealizado` text NOT NULL,
  `idtiporespuestaxcomunicacion` tinyint(3) unsigned NOT NULL,
  PRIMARY KEY (`idcomunicacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `configuraciones` (
  `idconfiguracion` tinyint(1) unsigned NOT NULL,
  `telefonos` varchar(150) NOT NULL,
  `mail` varchar(150) NOT NULL,
  `url` varchar(2083) NOT NULL,
  `domicilio` varchar(150) NOT NULL,
  `idlocalidad` smallint(3) unsigned NOT NULL,
  `razonsocial` varchar(150) NOT NULL,
  `idtipoiva` tinyint(1) unsigned NOT NULL,
  `cuit` bigint(11) unsigned NOT NULL,
  `ingresosbrutos` varchar(20) NOT NULL,
  `emitir_impuestos` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `tributo_5329` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `auto_tributo_5329` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `espacio_archivos` varchar(50) NOT NULL DEFAULT '2',
  `abonos` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `inicio` date NOT NULL,
  `estilo_logo` char(20) NOT NULL,
  `ext_logo` char(3) NOT NULL,
  `estilo_empresa` char(20) NOT NULL,
  `estilo_informes` char(20) NOT NULL,
  `estilo_ventas` char(20) NOT NULL,
  `estilo_ventas_pdf` char(20) NOT NULL,
  PRIMARY KEY (`idconfiguracion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `configuraciones_mail` (
    `idusuario` smallint(5) unsigned NOT NULL,
    `idplantilla` smallint(5) unsigned NOT NULL,
    `mail` text NOT NULL,
    `remitente` varchar(100) NOT NULL,
    `adjunto` varchar(50) NOT NULL,
    `copia` tinyint(1) unsigned NOT NULL,
    UNIQUE KEY `idusuario` (`idusuario`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `conocimientos` (
  `idconocimiento` int unsigned NOT NULL AUTO_INCREMENT,
  `idtipoconocimiento` smallint unsigned NOT NULL,
  `titulo` varchar(200) NOT NULL,
  `texto` text NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`idconocimiento`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `constantes` (
  `idconstante` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `imagen_ancho` int(1) unsigned NOT NULL DEFAULT '1024',
  `miniatura_ancho` int(1) unsigned NOT NULL DEFAULT '160',
  `logo_ancho` int(1) unsigned NOT NULL DEFAULT '360',
  PRIMARY KEY (`idconstante`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `constantes` (`imagen_ancho`, `miniatura_ancho`) VALUES
('640', '160');

CREATE TABLE `controles` (
  `idusuario` smallint(5) unsigned NOT NULL,
  `ultimoacceso` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ultimosid` char(40) NOT NULL,
  `bienvenida` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ayuda_puntual` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `agrandar` tinyint(1) unsigned NOT NULL DEFAULT '10',
  `mensajes_ocultar` tinyint(1) unsigned NOT NULL,
  `tareas_ocultar` tinyint(1) unsigned NOT NULL,
  `servicios_ocultar` tinyint(1) unsigned NOT NULL,
  `servicios_usuario` tinyint(1) unsigned NOT NULL,
  `servicios_ordenar` enum('prioridad ASC', 'prioridad DESC', 'fechasolicitado ASC', 'fechasolicitado DESC', 'fechalimite ASC', 'fechalimite DESC') NOT NULL DEFAULT 'prioridad ASC',
  `ventas_ocultar` tinyint(1) signed NOT NULL,
  `ventas_pendientes_usuario` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventas_ordenar` enum('fecha ASC', 'fecha DESC', 'vencimiento1 ASC', 'vencimiento1 DESC', 'idtipoventa ASC', 'idtipoventa DESC', 'total ASC', 'total DESC') NOT NULL DEFAULT 'fecha ASC',
  `ventas_agregarpago` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `formato_separador_miles` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `cheques_ordenar` enum('fecha DESC', 'fechacobro DESC') NOT NULL DEFAULT 'fechacobro DESC',
  `ultimotipoventa` tinyint(1) signed NOT NULL DEFAULT '1',
  `ultimotipocompra` tinyint(1) signed NOT NULL DEFAULT '1',
  `ultimotiposervicio` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ultimoformapagoventa` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ultimoformapagocompra` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ultimotipocajaventa` smallint(5) unsigned NOT NULL DEFAULT '1',
  `ultimotipocajacompra` smallint(5) unsigned NOT NULL DEFAULT '1',
  `ultimoconceptoventa` smallint(5) unsigned NOT NULL DEFAULT '1',
  `ultimoconceptocompra` smallint(5) unsigned NOT NULL DEFAULT '1',
  `ultimobanco` smallint(5) unsigned NOT NULL,
  `ultimocheque` varchar(36) NOT NULL,
  `ultimodepositoorigen` smallint(5) unsigned NOT NULL DEFAULT '1',
  `texto_rapido_1` text NOT NULL,
  `texto_rapido_2` text NOT NULL,
  `texto_rapido_3` text NOT NULL,
  `texto_rapido_4` text NOT NULL,
  `texto_rapido_5` text NOT NULL,
  `ultimoconceptotransferencia` smallint (5) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`idusuario`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `cotizaciones` (
    `idcotizacion` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
    `idmoneda` smallint(3) unsigned NOT NULL,
    `idusuario` smallint(5) unsigned NOT NULL,
    `fechayhora` datetime NOT NULL,
    `cotizacion` decimal(15,2) NOT NULL,
    PRIMARY KEY (`idcotizacion`),
    INDEX `fechayhora` (`fechayhora`)
) ENGINE=MyISAM DEFAULT charSET=utf8mb4;

INSERT INTO `cotizaciones` SET
    `idmoneda` = 1,
    `idusuario` = 0,
    `fechayhora` = NOW(),
    `cotizacion` = 1;

CREATE TABLE `datosxextras` (
  `idextraxmodulo` mediumint(8) unsigned NOT NULL,
  `idrelacion` int(10) unsigned NOT NULL,
  `idlistaxextra` int(10) unsigned NOT NULL,
  `texto` varchar(100) NOT NULL,
  UNIQUE KEY `idextraxmodulo` (`idextraxmodulo`,`idrelacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `depositos` (
  `iddeposito` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(100) NOT NULL,
  `domicilio` varchar(150) NOT NULL,
  `idlocalidad` smallint(3) unsigned NOT NULL,
  `observacion` text NOT NULL,
  PRIMARY KEY (`iddeposito`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `depositos`  (`iddeposito`, `nombre`, `domicilio`, `idlocalidad`, `observacion`) VALUES
(1, 'Principal', '', 0, '');

CREATE TABLE `extrasxmodulos` (
  `idextraxmodulo` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `modulo` enum('bienes','clientes','comunicaciones','conocimientos','productos','proveedores','servicios','ventas','compras') NOT NULL,
  `tipo` enum('lista','texto') NOT NULL,
  `nombre` varchar(60) NOT NULL,
  `buscado` tinyint(1) unsigned NOT NULL,
  `mostrar_en_comprobantes` tinyint(1) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`idextraxmodulo`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `fullsearch` (
  `id` int(10) unsigned NOT NULL,
  `modulo` enum('bienes', 'clientes', 'conocimientos', 'productos', 'proveedores', 'servicios') NOT NULL,
  `fullsearch` text NOT NULL,
  PRIMARY KEY (id, modulo),
  FULLTEXT (fullsearch)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `fullsearch` (`id`, `modulo`, `fullsearch`) VALUES
(1, 'proveedores', 'Proveedor sin registrar');

CREATE TABLE `fullsearch_clientes` (
  `id` int(10) unsigned NOT NULL,
  `modulo` enum('clientes') NOT NULL,
  `fullsearch` text NOT NULL,
  PRIMARY KEY (id, modulo),
  FULLTEXT (fullsearch)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

# Temporalmente paso fullsearch_clientes a fullsearch
INSERT INTO `fullsearch` (`id`, `modulo`, `fullsearch`) VALUES
(1, 'clientes', 'Consumidor Final');

CREATE TABLE `fullsearch_productos` (
  `id` int(10) unsigned NOT NULL,
  `modulo` enum('productos') NOT NULL,
  `fullsearch` text NOT NULL,
  PRIMARY KEY (id, modulo),
  FULLTEXT (fullsearch)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `historial` (
  `idhistorial` bigint unsigned NOT NULL AUTO_INCREMENT,
  `fechayhora` datetime NOT NULL,
  `idproducto` int(10) unsigned NOT NULL,
  `usuario_nombre` varchar(100) NOT NULL,
  `stockactual` text NOT NULL,
  `costo` decimal(15,2) NOT NULL,
  `precio` text NOT NULL,
  `preciofinal` text NOT NULL,
  `motivo` varchar(100) NOT NULL,
  `idrelacion` int unsigned NOT NULL,
  PRIMARY KEY (idhistorial),
  INDEX `idproducto` (`idproducto`),
  INDEX `idrelacion` (`idrelacion`),
  INDEX `idproducto_fechayhora` (`idproducto`, `fechayhora`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ivasxcompras` (
  `ivaxcompra` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idcompra`  mediumint(8) NOT NULL,
  `idiva` tinyint(1) unsigned NOT NULL,
  `iva` decimal(15,2) unsigned NOT NULL,
  PRIMARY KEY (`ivaxcompra`),
  INDEX `idcompra` (`idcompra`)
) ENGINE=InnoDB DEFAULT charSET = utf8mb4;

CREATE TABLE `ivasxventas` (
  `ivaxventa` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idventa`  mediumint(8) NOT NULL,
  `idiva` tinyint(1) unsigned NOT NULL,
  `iva` decimal(15,2) unsigned NOT NULL,
  PRIMARY KEY (`ivaxventa`),
  INDEX `idventa` (`idventa`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `listas` (
  `idlista` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `nombre` varchar(100) NOT NULL,
  `observacion` text NOT NULL,
  PRIMARY KEY (`idlista`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `listas` (`idlista`, `idmoneda`, `nombre`, `observacion`) VALUES
(1, 1, 'Principal', '');

CREATE TABLE `listasxextras` (
  `idlistaxextra` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idextraxmodulo` mediumint(9) unsigned NOT NULL,
  `nombre` varchar(60) NOT NULL,
  PRIMARY KEY (`idlistaxextra`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `mails_enviados`(
  `tiporelacion` enum('clientes', 'proveedores','ventasxservicios', 'comprasxservicios', 'ventas', 'compras', 'servicios', 'ventaspagos', 'compraspagos', 'traslados', 'mensajes', 'tareas') NOT NULL,
  `idrelacion` mediumint(8) unsigned NOT NULL,
  `fecha` datetime NOT NULL,
  UNIQUE KEY `tiporelacion+idrelacion` (`tiporelacion`,`idrelacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `mensajes` (
  `idmensaje` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idusuario` smallint(5) unsigned NOT NULL,
  `idremitente` smallint(5) NOT NULL,
  `tipo` enum('Alerta','Confirmacion','Recordatorio','Personal','Informacion','Notificacion','Consulta') NOT NULL DEFAULT 'Alerta',
  `fecha` datetime NOT NULL,
  `texto` text NOT NULL,
  `destacado` tinyint(1) NOT NULL DEFAULT '0',
  `visto` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`idmensaje`),
  INDEX `tipo` (`tipo`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `migrations` (
  `archivo` varchar(40) NOT NULL DEFAULT '',
  `timestamps` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`archivo`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `migrations` (`archivo`) VALUES
('20161001000000_3106.sql'),
('20161020144656_2987.sql'),
('20161126141150_3025.sql'),
('20161206190423_3035.sql'),
('20161226100040_2913.sql'),
('20170104233026_3064.sql'),
('20170116000000_3042.sql'),
('20170117235044_3070.php'),
('20170205144721_3069.sql'),
('20170214131256_3025.php'),
('20170410163547_3070.sql'),
('20170508151100_3101.php'),
('20170523164402_3069.sql'),
('20170213000000_2917.sql'),
('20170304000000_2917.sql'),
('20170425102100_2917.sql'),
('20170427115300_2917.sql'),
('20170605122200_2917.sql'),
('20170606090002_2917.sql'),
('20170612113936_35.sql'),
('20170622110129_2.31.sql'),
('20170623155352_2917.php'),
('20170626105813_2917.php'),
('20170629164700_2.31.sql'),
('20170714122200_82.php'),
('20170727181000_2.32.sql'),
('20170811201234_3040.sql'),
('20170815001738_2.31.sql'),
('20170815112300_0112.sql'),
('20170822134700_2.33.sql'),
('20170803175500_2.33.sql'),
('20170822134701_2.33.no'),
('20171010151180_2.34.sql'),
('20171011110100_2.34.sql'),
('20171117165246_2.35.sql'),
('20171205194852_179.php'),
('20180221163653_240.sql'),
('20180222220722_178.sql'),
('20180302174939_261.sql'),
('20180605164400_189.sql'),
('20180622163900_381.php'),
('20180702121900_389.sql'),
('20180702121901_389.php'),
('20180723153420_389.sql'),
('20180817111420_446.sql'),
('20180910151001_471.sql'),
('20180918110001_425.sql'),
('20180919170501_425.sql'),
('20181017182901_485.sql'),
('20181029150001_515.php'),
('20181031001613_349.sql'),
('20181106101836_545.sql'),
('20181107194900_548.sql'),
('20181109111800_549.sql'),
('20181120152900_305.sql'),
('20181127113730_575.sql'),
('20181204085100_297.sql'),
('20181211174700_576.sql'),
('20190123105310_553.php'),
('20190123105310_553.sql'),
('20190214131221_627.sql'),
('20190410112600_692.sql'),
('20190424104827_692.php'),
('20190426181400_712.php'),
('20190507102719_718.sql'),
('20190513153805_724.sql'),
('20190516102830_718.sql'),
('20190527165325_731.sql'),
('20190528092851_733.sql'),
('20190619130913_730.sql'),
('20190712101704_777.php'),
('20190729182916_775.sql'),
('20190805162916_790.php'),
('20190806193423_768.sql'),
('20190807184837_802.sql'),
('20190902113700_818.php'),
('20191018122722_788.sql'),
('20191208122200_883.php'),
('20191228101100_900.sql'),
('20200111205800_1216.sql'),
('20200615110700_1044.sql'),
('20200701152000_891.php'),
('20200909203400_1094.sql'),
('20201212185300_1169.sql'),
('20201214174500_1228.sql'),
('20202102095200_931.php'),
('20202805194600_1026.sql'),
('20202807191200_1075.sql'),
('20202807191201_1075.php'),
('20210119181822_1261.sql'),
('20210203185500_1267.sql'),
('20210205151100_1239.sql'),
('20210306105800_1297.sql'),
('20210320185000_1231.php'),
('20210420104213_1337.sql'),
('20210420120817_1330.sql'),
('20210513161900_1348.sql'),
('20210722065009_1391.sql'),
('20210727101607_1403.sql'),
('20210727130214_1360.sql'),
('20210803144705_1430.sql'),
('20210803161830_1391.sql'),
('20210823090616_1360.sql'),
('20210824105914_1378.sql'),
('20210905194000_1446.sql'),
('20210913215300_1450.sql'),
('20210915111000_1414.sql'),
('20210925155800_1449.sql'),
('20211001121944_1014.sql'),
('20211021170928_1461.sql'),
('20211081172001_1255.sql'),
('20211119111800_1480.sql'),
('20211202004309_1491.sql'),
('20220111150400_1507.php'),
('20220111150400_1507.sql'),
('20220117192746_1172.sql'),
('20220120125700_1519.php'),
('20220124124616_1419.sql'),
('20220125111351_1014.sql'),
('20220212193000_1465.sql'),
('20220228162930_1534.php'),
('20220307095509_1538.sql'),
('20220323155500_880.sql'),
('20220330152347_1550.sql'),
('20220419183500_1428.sql'),
('20220616202200_1594.sql'),
('20220617104200_1595.sql'),
('20220816114529_1630.sql'),
('20220830183800_1622.sql'),
('20221203115500_1622.sql'),
('20221212192200_1604.sql'),
('20221220105700_1660.php'),
('20230214174006_1712.sql'),
('20230304172606_1724.sql'),
('20230327092041_1733.sql'),
('20230329114023_1732.sql'),
('20230331145512_1739.sql'),
('20230425192639_1760.sql'),
('20230510085854_1763.sql'),
('20230516130300_1770.sql'),
('20230518144800_1769.sql'),
('20230518152500_1726.php'),
('20230518195412_1767.sql'),
('20230531125500_1227.sql'),
('20230808092010_1808.sql'),
('20230829145400_1814.sql'),
('20240105100200_1881.php'),
('20240126122307_1900.sql'),
('20240130105603_1412.sql'),
('20240518162100_1927.sql'),
('20240422144535_1955.sql'),
('20240606122154_1904.sql'),
('20240627105156_1988.sql'),
('20240719125900_1995.php'),
('20240820175355_2004.sql'),
('20240929114100_1952.sql'),
('20241125200700_2060.sql'),
('20250324201235_2015.php'),
('20250420200700_2112.sql'),
('20250509201000_2132.sql');

CREATE TABLE `monedas` (
    `idmoneda` smallint(3) unsigned NOT NULL AUTO_INCREMENT,
    `nombre` varchar(60) NOT NULL,
    `simbolo` varchar(3) NOT NULL,
    `codigo` varchar(60) NOT NULL,
    `cotizacion` decimal(15,2) NOT NULL,
    PRIMARY KEY (`idmoneda`)
) ENGINE=MyISAM DEFAULT charSET=utf8mb4;

INSERT INTO `monedas` (`idmoneda`, `nombre`, `simbolo`, `codigo`, `cotizacion`) VALUES
(1, 'Pesos', '$', 'PES', 1);

CREATE TABLE `movimientosxcajas` (
  `idmovimientoxcaja` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idcaja` int(5) unsigned NOT NULL,
  `idusuario` smallint(5) unsigned NOT NULL,
  `idconcepto` smallint(5) unsigned NOT NULL,
  `fecha` datetime NOT NULL,
  `fechaconciliacion` date NOT NULL,
  `total` decimal(15,2) NOT NULL,
  `detalle` varchar(200) NOT NULL,
  `tiporelacion` enum('','transferencia','clientepago','proveedorpago') NOT NULL,
  `idrelacion` int(10) unsigned NOT NULL,
  `conciliacion` tinyint(1) NOT NULL,
  `idconciliacion` int(10) NOT NULL,
  `observacion` text NOT NULL,
  PRIMARY KEY (`idmovimientoxcaja`),
  INDEX `idcaja` (`idcaja`),
  INDEX `tiporelacion+idrelacion` (`tiporelacion`, `idrelacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `movimientosxtrazabilidades` (
  `idmovimientoxtrazabilidad` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idtrazabilidad` int(10) unsigned NOT NULL,
  `tiporelacion` enum('compra','venta','reserva','merma','aumento','transferencia') NOT NULL,
  `idrelacion` int(10) unsigned NOT NULL,
  `cantidad` decimal(9,3) unsigned NOT NULL,
  PRIMARY KEY (`idmovimientoxtrazabilidad`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `perfiles` (
  `idperfil` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(60) NOT NULL,
  `idusuario_supervisor` smallint(5) unsigned NOT NULL DEFAULT '0',
  `configuraciones_empresa` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `configuraciones_tablas` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `configuraciones_cuentas` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `usuarios_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `usuarios_todos` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `inicio_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `mensajes_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `tareas_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `tareas_alta_todos` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `tareas_ver_todos` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `clientes_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `clientes_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `clientes_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `clientes_mod_todos` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `clientes_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `clientes_informes` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `clientes_herramientas` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `productos_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `productos_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `productos_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `productos_costo` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `productos_traslados` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `productos_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `productos_informes` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `productos_herramientas` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `servicios_tiene` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `servicios_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `servicios_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `servicios_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `servicios_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `servicios_informes` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `servicios_herramientas` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `servicios_alta_todos` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `servicios_mod_todos` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `proveedores_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `proveedores_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `proveedores_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `proveedores_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `proveedores_informes` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `proveedores_herramientas` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `ventas_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventas_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventas_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventas_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventas_informes` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `ventas_herramientas` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `ventas_mod_todos` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventas_nuevo` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventas_mod_stock` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventas_mod_precios` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventaspagos_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventaspagos_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventaspagos_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `ventaspagos_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `compras_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `compras_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `compras_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `compras_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `compras_informes` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `compras_herramientas` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `compraspagos_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `compraspagos_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `compraspagos_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `compraspagos_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `cajas_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `cajas_informes` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `cajas_herramientas` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `conocimientos_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `conocimientos_herramientas` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `bienes_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `bienes_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `bienes_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `bienes_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `bienes_informes` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `bienes_herramientas` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `comunicaciones_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `comunicaciones_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `comunicaciones_mod` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `comunicaciones_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `comunicaciones_informes` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `comunicaciones_herramientas` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `comunicaciones_tiene` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `comunicaciones_alta_todos` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `comunicaciones_mod_todos` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `archivos_ver` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `archivos_alta` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `archivos_baja` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `archivos_baja_todos` tinyint(1) unsigned NOT NULL DEFAULT '1',
  PRIMARY KEY (`idperfil`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `perfiles` SET
  `idperfil` = '1',
  `nombre` = 'Administrador',
  `idusuario_supervisor` = '1',
  `configuraciones_empresa` = '1',
  `configuraciones_tablas` = '1',
  `configuraciones_cuentas` = '1',
  `usuarios_mod` = '1',
  `usuarios_todos` = '1',
  `inicio_ver` = '1',
  `mensajes_ver` = '1',
  `tareas_ver` = '1',
  `tareas_alta_todos` = '1',
  `tareas_ver_todos` = '1',
  `clientes_ver` = '1',
  `clientes_alta` = '1',
  `clientes_mod` = '1',
  `clientes_mod_todos` = '1',
  `clientes_baja` = '1',
  `clientes_informes` = '1',
  `clientes_herramientas` = '1',
  `productos_ver` = '1',
  `productos_alta` = '1',
  `productos_mod` = '1',
  `productos_baja` = '1',
  `productos_informes` = '1',
  `productos_herramientas` = '1',
  `servicios_tiene` = '1',
  `servicios_ver` = '1',
  `servicios_alta` = '1',
  `servicios_mod` = '1',
  `servicios_baja` = '1',
  `servicios_alta_todos` = '1',
  `servicios_mod_todos` = '1',
  `servicios_informes` = '1',
  `servicios_herramientas` = '1',
  `proveedores_ver` = '1',
  `proveedores_alta` = '1',
  `proveedores_mod` = '1',
  `proveedores_baja` = '1',
  `proveedores_informes` = '1',
  `proveedores_herramientas` = '1',
  `ventas_ver` = '1',
  `ventas_alta` = '1',
  `ventas_mod` = '1',
  `ventas_baja` = '1',
  `ventas_mod_todos` = '1',
  `ventas_nuevo` = '1',
  `ventas_mod_stock` = '1',
  `ventas_mod_precios` = '1',
  `ventas_informes` = '1',
  `ventas_herramientas` = '1',
  `ventaspagos_ver` = '1',
  `ventaspagos_alta` = '1',
  `ventaspagos_mod` = '1',
  `ventaspagos_baja` = '1',
  `compras_ver` = '1',
  `compras_alta` = '1',
  `compras_mod` = '1',
  `compras_baja` = '1',
  `compras_informes` = '1',
  `compras_herramientas` = '1',
  `compraspagos_ver` = '1',
  `compraspagos_alta` = '1',
  `compraspagos_mod` = '1',
  `compraspagos_baja` = '1',
  `cajas_ver` = '1',
  `cajas_informes` = '1',
  `cajas_herramientas` = '1',
  `conocimientos_ver` = '1',
  `conocimientos_herramientas` = '1',
  `bienes_ver` = '1',
  `bienes_alta` = '1',
  `bienes_mod` = '1',
  `bienes_baja` = '1',
  `bienes_informes` = '1',
  `bienes_herramientas` = '1',
  `comunicaciones_ver` = '1',
  `comunicaciones_alta` = '1',
  `comunicaciones_mod` = '1',
  `comunicaciones_baja` = '1',
  `comunicaciones_tiene` = '1',
  `comunicaciones_alta_todos` = '1',
  `comunicaciones_mod_todos` = '1',
  `comunicaciones_informes` = '1',
  `comunicaciones_herramientas` = '1',
  `archivos_ver` = '1',
  `archivos_alta` = '1',
  `archivos_baja` = '1',
  `archivos_baja_todos` = '1';

INSERT INTO `perfiles` SET
  `idperfil` = '2',
  `nombre` = 'Vendedor (Usuario limitado)',
  `idusuario_supervisor` = '0',
  `configuraciones_empresa` = '0',
  `configuraciones_tablas` = '0',
  `configuraciones_cuentas` = '0',
  `usuarios_mod` = '1',
  `usuarios_todos` = '0',
  `inicio_ver` = '1',
  `mensajes_ver` = '1',
  `tareas_ver` = '1',
  `tareas_alta_todos` = '1',
  `tareas_ver_todos` = '0',
  `clientes_ver` = '1',
  `clientes_alta` = '1',
  `clientes_mod` = '1',
  `clientes_mod_todos` = '0',
  `clientes_baja` = '0',
  `clientes_informes` = '0',
  `clientes_herramientas` = '0',
  `productos_ver` = '1',
  `productos_alta` = '1',
  `productos_mod` = '1',
  `productos_baja` = '0',
  `productos_informes` = '0',
  `productos_herramientas` = '0',
  `servicios_tiene` = '1',
  `servicios_ver` = '1',
  `servicios_alta` = '1',
  `servicios_mod` = '1',
  `servicios_baja` = '0',
  `servicios_alta_todos` = '1',
  `servicios_mod_todos` = '0',
  `servicios_informes` = '0',
  `servicios_herramientas` = '0',
  `proveedores_ver` = '1',
  `proveedores_alta` = '1',
  `proveedores_mod` = '1',
  `proveedores_baja` = '0',
  `proveedores_informes` = '0',
  `proveedores_herramientas` = '0',
  `ventas_ver` = '1',
  `ventas_alta` = '1',
  `ventas_mod` = '1',
  `ventas_baja` = '0',
  `ventas_nuevo` = '1',
  `ventas_mod_stock` = '1',
  `ventas_mod_precios` = '1',
  `ventas_informes` = '0',
  `ventas_herramientas` = '0',
  `ventas_mod_todos` = '0',
  `ventaspagos_ver` = '1',
  `ventaspagos_alta` = '1',
  `ventaspagos_mod` = '1',
  `ventaspagos_baja` = '0',
  `compras_ver` = '1',
  `compras_alta` = '1',
  `compras_mod` = '1',
  `compras_baja` = '0',
  `compras_informes` = '0',
  `compras_herramientas` = '0',
  `compraspagos_ver` = '1',
  `compraspagos_alta` = '1',
  `compraspagos_mod` = '1',
  `compraspagos_baja` = '0',
  `cajas_ver` = '1',
  `cajas_informes` = '1',
  `cajas_herramientas` = '1',
  `conocimientos_ver` = '1',
  `conocimientos_herramientas` = '1',
  `bienes_ver` = '1',
  `bienes_alta` = '1',
  `bienes_mod` = '1',
  `bienes_baja` = '0',
  `bienes_informes` = '0',
  `bienes_herramientas` = '0',
  `comunicaciones_ver` = '1',
  `comunicaciones_alta` = '1',
  `comunicaciones_mod` = '1',
  `comunicaciones_baja` = '0',
  `comunicaciones_tiene` = '1',
  `comunicaciones_alta_todos` = '1',
  `comunicaciones_mod_todos` = '0',
  `comunicaciones_informes` = '0',
  `comunicaciones_herramientas` = '0',
  `archivos_ver` = '0',
  `archivos_alta` = '0',
  `archivos_baja` = '0',
  `archivos_baja_todos` = '0';

CREATE TABLE `perfilesxcajas` (
  `idperfil` tinyint(1) NOT NULL,
  `idtipocaja` tinyint(1) NOT NULL,
  `abrir` tinyint(1) NOT NULL,
  `ver` tinyint(1) NOT NULL,
  `alta` tinyint(1) NOT NULL,
  `mod_` tinyint(1) NOT NULL,
  `baja` tinyint(1) NOT NULL,
  `cerrar` tinyint(1) NOT NULL,
  `mod_cerrada` tinyint(1) unsigned NOT NULL,
  UNIQUE KEY `idperfil+idtipocaja` (`idperfil`,`idtipocaja`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `plantillas` (
  `idplantilla` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `asunto_nombre` tinyint(1) unsigned NOT NULL,
  `asunto` varchar(200) NOT NULL,
  `texto` text NOT NULL,
  PRIMARY KEY (`idplantilla`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `precios` (
  `idproducto` int(10) unsigned NOT NULL,
  `idlista` tinyint(1) unsigned NOT NULL,
  `utilidad` decimal(6,2) NOT NULL,
  `precio` decimal(15,2) unsigned NOT NULL,
  `preciofinal` decimal(15,2) unsigned NOT NULL,
  UNIQUE KEY `idproducto+idlista` (`idproducto`,`idlista`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `productos` (
  `idproducto` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `estadoventa` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `estadocompra` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `estadocombo` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `idrubro` smallint(1) unsigned NOT NULL,
  `codigo` varchar(60) NOT NULL,
  `codigoproveedor` varchar(60) NOT NULL,
  `nombre` varchar(200) NOT NULL,
  `url_amigable` varchar(200) NOT NULL,
  `idunidad` tinyint(1) unsigned NOT NULL,
  `idiva` tinyint(1) unsigned NOT NULL,
  `idproveedor` smallint(5) unsigned NOT NULL,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `costo` decimal(15,2) unsigned NOT NULL,
  `controlarstock` tinyint(1) unsigned NOT NULL,
  `trazabilidad` tinyint(1) unsigned NOT NULL,
  `tributo_5329` tinyint(1) unsigned NOT NULL,
  `combo` tinyint(1) unsigned NOT NULL,
  `stocknegativo` tinyint(1) unsigned NOT NULL,
  `mostrartienda` tinyint(1) unsigned NOT NULL,
  `observacion` text NOT NULL,
  `obstienda` text NOT NULL,
  `obsinterna` text NOT NULL,
  `sku` varchar(255) NOT NULL,
  `ML_item_id` bigint(10) unsigned NOT NULL,
  `ML_item_id2` bigint(10) unsigned NOT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`idproducto`),
  UNIQUE KEY (`codigo`),
  INDEX `sku` (`sku`),
  INDEX `ML_item_id` (`ML_item_id`),
  INDEX `ML_item_id2` (`ML_item_id2`),
  INDEX `idproveedor` (`idproveedor`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `productosxabonos` (
  `idproductoxabono` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idabono` mediumint(8) unsigned NOT NULL,
  `idproducto` int(10) unsigned NOT NULL,
  `codigo` varchar(60) NOT NULL,
  `cantidad` decimal(9,3) unsigned NOT NULL,
  `idunidad` tinyint(1) unsigned NOT NULL,
  `idiva` tinyint(1) unsigned NOT NULL,
  `nombre` varchar(200) NOT NULL,
  `costo` decimal(15,2) unsigned NOT NULL,
  `precio` decimal(15,2) unsigned NOT NULL,
  `preciofinal` decimal(15,2) unsigned NOT NULL,
  `descuento` decimal(4,2) unsigned NOT NULL,
  `observacion` text NOT NULL,
  PRIMARY KEY (`idproductoxabono`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `productosxcompras` (
  `idproductoxcompra` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idcompra` mediumint(8) unsigned NOT NULL,
  `idproducto` int(10) unsigned NOT NULL,
  `codigo` varchar(60) NOT NULL,
  `cantidad` decimal(9,3) unsigned NOT NULL,
  `idunidad` tinyint(1) unsigned NOT NULL,
  `idiva` tinyint(1) unsigned NOT NULL,
  `nombre` varchar(200) NOT NULL,
  `costo` decimal(15,2) unsigned NOT NULL,
  `descuento` decimal(5,2) unsigned NOT NULL,
  `observacion` text NOT NULL,
  PRIMARY KEY (`idproductoxcompra`),
  INDEX `idcompra` (`idcompra`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `productosxcombos` (
  `idproductoxcombo` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idproducto` int(10) unsigned NOT NULL,
  `idcombo` mediumint(8) unsigned NOT NULL,
  `cantidad` decimal(9,3) unsigned NOT NULL,
  PRIMARY KEY (`idproductoxcombo`),
  INDEX `idcombo` (`idcombo`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `productosxtraslados` (
  `idproductoxtraslado` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idtraslado` mediumint(8) unsigned NOT NULL,
  `idproducto` int(10) unsigned NOT NULL,
  `cantidad` decimal(9,3) unsigned NOT NULL,
  PRIMARY KEY (`idproductoxtraslado`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `productosxventas` (
  `idproductoxventa` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idventa` mediumint(8) unsigned NOT NULL,
  `idproducto` int(10) unsigned NOT NULL,
  `codigo` varchar(60) NOT NULL,
  `cantidad` decimal(9,3) unsigned NOT NULL,
  `idunidad` tinyint(1) unsigned NOT NULL,
  `idiva` tinyint(1) unsigned NOT NULL,
  `nombre` varchar(200) NOT NULL,
  `costo` decimal(15,2) unsigned NOT NULL,
  `precio` decimal(15,2) NOT NULL,
  `preciofinal` decimal(15,2) NOT NULL,
  `descuento` decimal(5,2) unsigned NOT NULL,
  `observacion` text NOT NULL,
  PRIMARY KEY (`idproductoxventa`),
  INDEX `idventa` (`idventa`),
  INDEX `idiva` (`idiva`),
  INDEX `codigo` (`codigo`),
  INDEX `nombre` (`nombre`),
  INDEX `idproducto` (`idproducto`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `proveedores` (
  `idproveedor` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `idtipoproveedor` tinyint(1) unsigned NOT NULL,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `contacto` varchar(150) NOT NULL,
  `telefonos` varchar(150) NOT NULL,
  `domicilio` varchar(150) NOT NULL,
  `idlocalidad` smallint(3) NOT NULL,
  `mail` varchar(320) NOT NULL,
  `idtipoiva` tinyint(1) unsigned NOT NULL,
  `razonsocial` varchar(150) NOT NULL,
  `cuit` bigint(11) unsigned NOT NULL,
  `obsinterna` text NOT NULL,
  `idconcepto` smallint(5) unsigned NOT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`idproveedor`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `proveedores` (`idproveedor`, `idmoneda`, `nombre`, `contacto`, `telefonos`, `domicilio`, `idlocalidad`, `mail`, `idtipoiva`, `razonsocial`, `cuit`, `obsinterna`) VALUES
(0, 1, 'Sin especificar', '', '', '', 0, '', 0, '', 0, ''),
(1, 1, 'Proveedor sin registrar', '', '', '', 0, '', 0, '', 0, '');

CREATE TABLE `retenciones` (
  `idretencion` int NOT NULL AUTO_INCREMENT,
  `idtributo` int(11) NOT NULL,
  `puntodeventa` mediumint(5) unsigned NOT NULL,
  `numero` int(9) unsigned NOT NULL,
  `observacion` text NOT NULL,
  PRIMARY KEY (`idretencion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `saldos`(
  `tiporelacion` enum('clientes', 'proveedores', 'ventasxservicios', 'comprasxservicios', 'ventas', 'compras', 'cajas') NOT NULL,
  `idrelacion` mediumint(8) unsigned NOT NULL,
  `saldo` decimal(15,2) signed NOT NULL,
  UNIQUE KEY `tiporelacion+idrelacion` (`tiporelacion`,`idrelacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `saldos` (`tiporelacion`, `idrelacion`, `saldo`) VALUES
('clientes', 1, 0),
('proveedores', 0, 0),
('proveedores', 1, 0),
('cajas', 1, 0),
('cajas', 2, 0),
('cajas', 3, 0),
('cajas', 4, 0);

CREATE TABLE `scripts` (
  `idscript` tinyint(3) unsigned NOT NULL,
  `estado` tinyint(1) unsigned NOT NULL,
  `nombre` varchar(60) NOT NULL,
  `observacion` text NOT NULL,
  `crontab` tinyint(1) unsigned NOT NULL,
  `fechainicio` date NOT NULL,
  `fechafin` date NOT NULL,
  `dia` tinyint(2) unsigned NOT NULL,
  `mes` tinyint(2) unsigned NOT NULL,
  `año` smallint(4) unsigned NOT NULL,
  `diadesemana` char(7) NOT NULL DEFAULT '0000000',
  PRIMARY KEY (`idscript`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `servicios` (
  `idservicio` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idcliente` int(10) unsigned NOT NULL DEFAULT '1',
  `idusuario` smallint(5) unsigned NOT NULL,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `idtiposervicio` tinyint(1) unsigned NOT NULL,
  `prioridad` tinyint(1) unsigned NOT NULL DEFAULT '3',
  `titulo` varchar(200) NOT NULL,
  `fechasolicitado` datetime NOT NULL,
  `fechainicio` datetime NOT NULL,
  `fechafin` datetime NOT NULL,
  `fechalimite` datetime NOT NULL,
  `tiempoestimado` time NOT NULL,
  `tiempodedicado` time NOT NULL,
  `contartiempo` tinyint(1) unsigned NOT NULL,
  `obssolicitado` text NOT NULL,
  `obsrealizado` text NOT NULL,
  `obsinterna` text NOT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`idservicio`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `smtps` (
  `idsmtp` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `estado` tinyint(3) unsigned NOT NULL,
  `nombre` varchar(60) NOT NULL,
  `mail` varchar(360) NOT NULL,
  `servidor` varchar(60) NOT NULL,
  `puerto` smallint(5) unsigned NOT NULL,
  `seguridad` enum('','ssl','tls') NOT NULL,
  `autenticacion` tinyint(3) unsigned NOT NULL,
  `user` varchar(360) NOT NULL,
  `pass` char(32) NOT NULL,
  PRIMARY KEY (`idsmtp`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `stock` (
  `idproducto` int(10) unsigned NOT NULL,
  `iddeposito` tinyint(1) unsigned NOT NULL,
  `stockactual` decimal(9,3) NOT NULL,
  `stockminimo` decimal(9,3) NOT NULL,
  `stockideal` decimal(9,3) NOT NULL,
  UNIQUE KEY `idproducto+iddeposito` (`idproducto`,`iddeposito`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `tablas_bancos` (
  `idbanco` int NOT NULL AUTO_INCREMENT,
  `nombre` varchar(100) NOT NULL,
  PRIMARY KEY (`idbanco`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_bancos` (`idbanco`, `nombre`) VALUES
(NULL, "Banco de la Nación Argentina"),
(NULL, "Banco de la Provincia de Buenos Aires"),
(NULL, "Banco de Galicia y Buenos Aires Sociedad Anónima"),
(NULL, "Citibank N.A."),
(NULL, "BBVA Banco Francés S.A."),
(NULL, "Banco Supervielle S.A."),
(NULL, "Banco Patagonia S.A."),
(NULL, "Banco Santander Río S.A."),
(NULL, "HSBC Bank Argentina S.A."),
(NULL, "Banco Credicoop Cooperativo Limitado"),
(NULL, "Banco Macro S.A."),
(NULL, "ICBC - Industrial AND Commercial Bank of China ARGENTINA S.A."),
(NULL, "Banco Itaú Argentina S.A."),
(NULL, "Banco de La Pampa Sociedad de Economía Mixta"),
(NULL, "Banco de la Ciudad de Buenos Aires"),
(NULL, "Banco Hipotecario S.A."),
(NULL, "Banco de la Provincia de Córdoba S.A."),
(NULL, "Banco Provincia del Neuquén Sociedad Anónima"),
(NULL, "Banco de Formosa S.A."),
(NULL, "Banco de Santiago del Estero S.A."),
(NULL, "Banco Piano S.A."),
(NULL, "Banco Provincia de Tierra del Fuego"),
(NULL, "Banco de San Juan S.A."),
(NULL, "Banco do Brasil S.A."),
(NULL, "Banco del Tucum n S.A."),
(NULL, "Banco Municipal de Rosario"),
(NULL, "Banco del Chubut S.A."),
(NULL, "Banco de Santa Cruz S.A."),
(NULL, "Banco de Corrientes S.A."),
(NULL, "Banco Interfinanzas S.A."),
(NULL, "Banco Columbia S.A."),
(NULL, "Banco CMF S.A."),
(NULL, "Banco de Valores S.A."),
(NULL, "Banco Roela S.A."),
(NULL, "Banco Mariva S.A."),
(NULL, "Bank of America - National Association"),
(NULL, "BNP Paribas"),
(NULL, "Banco de la República Oriental del Uruguay"),
(NULL, "Banco Sáenz S.A."),
(NULL, "Banco Meridian S.A."),
(NULL, "The Royal Bank of Scotland N.V."),
(NULL, "JPMorgan Chase Bank"),
(NULL, "The Bank of Tokyo-Mitsubishi UFJ Ltd."),
(NULL, "American Express Bank Ltd. Sociedad Anónima"),
(NULL, "Banco Comafi Sociedad Anónima"),
(NULL, "Banco de Inversión y Comercio Exterior S.A."),
(NULL, "Banco Finansur S.A."),
(NULL, "Banco Julio Sociedad Anónima"),
(NULL, "Banco Privado de Inversiones Sociedad Anónima"),
(NULL, "Banco Rioja Sociedad Anonima Unipersonal"),
(NULL, "Banco del Sol S.A."),
(NULL, "Nuevo Banco del Chaco S.A."),
(NULL, "Banco Voii S.A."),
(NULL, "Banco Industrial S.A."),
(NULL, "Deutsche Bank S.A."),
(NULL, "Nuevo Banco de Santa Fe S.A."),
(NULL, "Banco Cetelem Argentina S.A."),
(NULL, "Banco de Servicios Financieros S.A."),
(NULL, "Banco Bradesco Argentina S.A."),
(NULL, "Banco de Servicios y Transacciones S.A."),
(NULL, "RCI Banque"),
(NULL, "BACS Banco de Crédito y Securitización S.A."),
(NULL, "Banco Masventas S.A."),
(NULL, "Nuevo Banco de Entre Ríos S.A."),
(NULL, "Banco Bica S.A."),
(NULL, "Banco Coinag S.A."),
(NULL, "Ford Credit Compañía Financiera S.A."),
(NULL, "BANCO DE COMERCIO"),
(NULL, "Compañía Financiera Argentina S.A."),
(NULL, "Volkswagen Credit Compañía Financiera S.A."),
(NULL, "Cordial Compañía Financiera S.A."),
(NULL, "Fiat Crédito Compañía Financiera S.A."),
(NULL, "GPAT Compañía Financiera S.A."),
(NULL, "Mercedes-Benz Compañía Financiera Argentina S.A."),
(NULL, "Rombo Compañía Financiera S.A."),
(NULL, "John Deere Credit Compañía Financiera S.A."),
(NULL, "PSA Finance Argentina Compañía Financiera S.A."),
(NULL, "Toyota Compañía Financiera de Argentina S.A."),
(NULL, "Finandino Compañía Financiera S.A."),
(NULL, "Montemar Compañía Financiera S.A."),
(NULL, "Multifinanzas Compañía Financiera S.A."),
(NULL, "Caja de Crédito Cooperativa La Capital del Plata Limitada"),
(NULL, "Caja de Crédito Cuenca Cooperativa Limitada"),
(NULL, "Otro banco");

CREATE TABLE `tablas_comportamientos` (
  `idcomportamiento` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(60) NOT NULL,
  `nombre_corto` char(3) NOT NULL,
  `quienusa` enum('nadie','responsablesincriptos','monotributistas','todos') NOT NULL,
  `discrimina` char(1) NOT NULL,
  `muevestock` tinyint(1) unsigned NOT NULL,
  `muevesaldo` tinyint(1) unsigned NOT NULL,
  `operacioninversa` tinyint(1) unsigned NOT NULL,
  `esfiscal` tinyint(1) unsigned NOT NULL,
  PRIMARY KEY (`idcomportamiento`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_comportamientos` (`idcomportamiento`, `nombre`, `nombre_corto`, `quienusa`, `discrimina`, `muevestock`, `muevesaldo`, `operacioninversa`, `esfiscal`) VALUES
(1, 'Factura A', 'FC', 'responsablesincriptos', 'A', 0, 1, 0, 1),
(2, 'Nota de Débito A', 'ND', 'responsablesincriptos', 'A', 0, 1, 0, 1),
(3, 'Nota de Crédito A', 'NC', 'responsablesincriptos', 'A', 0, 1, 1, 1),
(4, 'Recibos A', 'RC', 'responsablesincriptos', 'A', 0, 1, 0, 1),
(5, 'Notas de Venta al contado A', 'NV', 'responsablesincriptos', 'A', 0, 0, 0, 1),
(6, 'Factura B', 'FC', 'responsablesincriptos', 'B', 0, 1, 0, 1),
(7, 'Nota de Débito B', 'ND', 'responsablesincriptos', 'B', 0, 1, 0, 1),
(8, 'Nota de Crédito B', 'NC', 'responsablesincriptos', 'B', 0, 1, 1, 1),
(9, 'Recibos B', 'RC', 'responsablesincriptos', 'B', 0, 1, 0, 1),
(10, 'Notas de Venta al contado B', 'NV', 'responsablesincriptos', 'B', 0, 0, 0, 1),
(11, 'Factura C', 'FC', 'monotributistas', 'C', 0, 1, 0, 1),
(12, 'Nota de Débito C', 'ND', 'monotributistas', 'C', 0, 1, 0, 1),
(13, 'Nota de Crédito C', 'NC', 'monotributistas', 'C', 0, 1, 1, 1),
(15, 'Recibo C', 'RC', 'monotributistas', 'C', 0, 1, 0, 1),
(51, 'Factura M', 'FC', 'responsablesincriptos', 'A', 0, 1, 0, 1),
(52, 'Nota de Débito M', 'ND', 'responsablesincriptos', 'A', 0, 1, 0, 1),
(53, 'Nota de Crédito M', 'NC', 'responsablesincriptos', 'A', 0, 1, 1, 1),
(54, 'Recibos M', 'RC', 'responsablesincriptos', 'A', 0, 1, 0, 1),
(55, 'Notas de Venta al contado M', 'NV', 'responsablesincriptos', 'A', 0, 0, 0, 1),

(102, 'Presupuesto A', 'PT', 'responsablesincriptos', 'A', 0, 0, 0, 0),
(103, 'Pedido A', 'PD', 'responsablesincriptos', 'A', 0, 0, 0, 0),
(105, 'Remito A', 'RT', 'responsablesincriptos', 'A', 1, 0, 0, 0),
(108, 'Remito de devolución A', 'RD', 'responsablesincriptos', 'A', 1, 0, 1, 0),

(109, 'Presupuesto B', 'PT', 'responsablesincriptos', 'B', 0, 0, 0, 0),
(110, 'Pedido B', 'PD', 'responsablesincriptos', 'B', 0, 0, 0, 0),
(111, 'Remito B', 'RT', 'responsablesincriptos', 'B', 1, 0, 0, 0),
(112, 'Remito de devolución B', 'RD', 'responsablesincriptos', 'B', 1, 0, 1, 0),

(113, 'Presupuesto C', 'PT', 'monotributistas', 'C', 0, 0, 0, 0),
(114, 'Pedido C', 'PD', 'monotributistas', 'C', 0, 0, 0, 0),
(115, 'Remito C', 'RT', 'monotributistas', 'C', 1, 0, 0, 0),
(116, 'Remito de devolución C', 'RD', 'monotributistas', 'C', 1, 0, 1, 0),

(101, 'Remito sin valorizar', 'RT', 'todos', 'R', 1, 0, 0, 0),
(104, 'Remito de devolución sin valorizar', 'RD', 'todos', 'R', 1, 0, 1, 0),
(106, 'Remito sin IVA', 'RT', 'responsablesincriptos', 'C', 1, 0, 0, 0),
(107, 'Remito de devolución sin IVA', 'RT', 'responsablesincriptos', 'C', 1, 0, 1, 0),

(117, 'Pedido de cotización', 'PC', 'nadie', 'R', 0, 0, 0, 0),
(201, 'Factura de crédito electrónica MiPymes (FCE)', 'FCE', 'responsablesincriptos', 'A', 0, 1, 0, 1),
(202, 'Nota de débito electrónica MiPymes (FCE)', 'FCE', 'responsablesincriptos', 'A', 0, 1, 0, 1),
(203, 'Nota de crédito electrónica MiPymes (FCE)', 'FCE', 'responsablesincriptos', 'A', 0, 1, 1, 1);

CREATE TABLE `categorias_compras` (
  `idtipocompra` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `nombre` varchar(20) NOT NULL,
  `muevestock` tinyint(1) unsigned NOT NULL,
  `muevesaldo` tinyint(1) unsigned NOT NULL,
  `operacioninversa` tinyint(1) unsigned NOT NULL,
  `esfiscal` tinyint(1) NOT NULL,
  `tienesituacion` tinyint(1) unsigned NOT NULL,
  `situacion` enum('no_utilizar', 'sin_especificar', 'pendiente', 'aprobado', 'rechazado') NOT NULL DEFAULT 'sin_especificar',
  PRIMARY KEY (`idtipocompra`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `categorias_compras` (`idtipocompra`, `idmoneda`, `nombre`, `muevestock`, `muevesaldo`, `operacioninversa`, `esfiscal`, `tienesituacion`, `situacion`) VALUES
(1, 1, 'Factura', 1, 1, 0, 1, 0, 'sin_especificar'),
(2, 1, 'Remito', 1, 1, 0, 0, 0, 'sin_especificar'),
(3, 1, 'Nota de crédito', 0, 1, 1, 1, 0, 'sin_especificar'),
(4, 1, 'Nota de débito', 0, 1, 0, 1, 0, 'sin_especificar'),
(5, 1, 'Remito de devolución', 1, 0, 1, 0, 0, 'sin_especificar'),
(6, 1, 'Presupuesto', 0, 0, 0, 0, 0, 'sin_especificar'),
(7, 1, 'Pedido', 0, 0, 0, 0, 0, 'sin_especificar'),
(8, 1, 'Pedido de cotización', 0, 0, 0, 0, 0, 'sin_especificar'),
(9, 1, 'Remito sin valorizar', 1, 0, 0, 0, 0, 'sin_especificar');

CREATE TABLE `tablas_condiciones` (
  `idtipoiva` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(60) NOT NULL,
  `nombre_corto` char(2) NOT NULL,
  `discrimina` tinyint(1) unsigned NOT NULL,
  PRIMARY KEY (`idtipoiva`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_condiciones` (`idtipoiva`, `nombre`, `nombre_corto`, `discrimina`) VALUES
(0, 'Consumidor final', 'CF', 0),
(1, 'IVA Responsable Inscripto', 'RI', 1),
(2, 'Responsable Monotributo', 'RM', 0),
(3, 'IVA Exento', 'IE', 0),
(4, 'Monotributista Social', 'MS', 0),
(5, 'Monotributo Trabajador Independiente Promovido', 'MT', 0),
(6, 'No Responsable IVA', 'NR', 0),
(15, 'IVA No Alcanzado', 'NA', 0);

CREATE TABLE `tablas_estilos` (
  `idestilo` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(255) NOT NULL,
  PRIMARY KEY (`idestilo`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_estilos` (`idestilo`, `nombre`) VALUES
(2, 'Adaptativo - Responsive'),
(3, 'Adaptativo amplio - Responsive');

CREATE TABLE `tablas_formasdepago` (
  `idformapago` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(30) NOT NULL,
  `tipo` ENUM('efectivo', 'banco', 'cheque', 'retencion') NOT NULL DEFAULT 'efectivo',
  PRIMARY KEY (`idformapago`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_formasdepago` (`idformapago`, `nombre`, `tipo`) VALUES
(0, 'Sin especificar', 'efectivo'),
(1, 'Efectivo', 'efectivo'),
(3, 'Tarjeta de crédito', 'banco'),
(4, 'Tarjeta de débito', 'banco'),
(5, 'Cheque/Echeq', 'cheque'),
(6, 'Transferencia bancaria', 'banco'),
(7, 'Depósito bancario', 'banco'),
(10, 'MercadoPago', 'banco'),
(11, 'PayPal', 'banco'),
(12, 'Débito automático', 'banco'),
(2, 'Cuenta Corriente', 'efectivo'),
(8, 'Canje', 'efectivo'),
(9, 'Otra', 'efectivo'),
(13, 'Retención', 'retencion'),
(14, 'TodoPago', 'banco'),
(15, 'Modo', 'banco');

CREATE TABLE `tablas_idiomas` (
  `ididioma` tinyint(1) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(20) NOT NULL,
  PRIMARY KEY (`ididioma`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_idiomas` (`ididioma`, `nombre`) VALUES
(1, 'Español (Argentina)');

CREATE TABLE `tablas_ivas` (
  `idiva` tinyint(1) unsigned NOT NULL,
  `nombre` varchar(20) NOT NULL,
  `valor` decimal(5,2) NOT NULL,
  `campo` varchar(10) NOT NULL,
  PRIMARY KEY (`idiva`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_ivas` (`idiva`, `nombre`, `valor`, `campo`) VALUES
(0, 'No aplica', 0, 'noaplica'),
(1, 'No gravado', 0, 'nogravado'),
(2, 'Exento', 0, 'exento'),
(3, '0%', 0, 'iva_3'),
(4, '10.5%', 10.50, 'iva_4'),
(5, '21%', 21.00, 'iva_5'),
(6, '27%', 27.00, 'iva_6'),
(8, '5%', 5.00, 'iva_8'),
(9, '2.5% ', 2.50, 'iva_9');

CREATE TABLE `tablas_provincias` (
  `idprovincia` tinyint(2) unsigned NOT NULL,
  `nombre` varchar(20) NOT NULL,
  PRIMARY KEY (`idprovincia`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_provincias` (`idprovincia`, `nombre`) VALUES
(0, 'Sin especificar'),
(25, 'CABA'),
(1, 'Buenos Aires'),
(2, 'Catamarca'),
(3, 'Córdoba'),
(4, 'Corrientes'),
(5, 'Entre Ríos'),
(6, 'Jujuy'),
(7, 'Mendoza'),
(8, 'La Rioja'),
(9, 'Salta'),
(10, 'San Juan'),
(11, 'San Luis'),
(12, 'Santa Fe'),
(13, 'Santiago del Estero'),
(14, 'Tucumán'),
(16, 'Chaco'),
(17, 'Chubut'),
(18, 'Formosa'),
(19, 'Misiones'),
(20, 'Neuquén'),
(21, 'La Pampa'),
(22, 'Río Negro'),
(23, 'Santa Cruz'),
(24, 'Tierra del Fuego');

CREATE TABLE `tablas_tags` (
  `idtag` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `modulo` enum('bienes','clientes','conocimientos','productos','proveedores','servicios','ventas','compras','ventaspagos','compraspagos') NOT NULL,
  `tag`  varchar(50) NOT NULL,
  `campo`  varchar(50) NOT NULL,
  `nombre`  varchar(50) NOT NULL,
  `tabla_fk`  varchar(50) NOT NULL,
  `campo_fk`  varchar(50) NOT NULL,
  `campo_mostrar`  varchar(50) NOT NULL,
  PRIMARY KEY (`idtag`),
  CONSTRAINT uc_tag UNIQUE (modulo,tag)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_tags` (`idtag`, `modulo`, `tag`, `campo`, `nombre`, `tabla_fk`, `campo_fk`, `campo_mostrar`) VALUES
(1, 'ventas', 'cliente', 'idcliente', 'Nombre del cliente', 'clientes', 'idcliente', 'nombre'),
(2, 'ventas', 'idventa', 'idventa', 'Nº de venta', '', '', ''),
(3, 'ventas', 'numero', 'numero', 'Nº del comprobante', '', '', ''),
(4, 'ventas', 'tipoventa', 'idtipoventa', 'Tipo de venta', 'categorias_ventas', 'idtipoventa', 'nombre'),
(5, 'ventas', 'total', 'total', 'Total', '', '', ''),
(6, 'ventas', 'vencimiento1', 'vencimiento1', '1º vencimiento', '', '', ''),
(7, 'ventas', 'vencimiento2', 'vencimiento2', '2º vencimiento', '', '', ''),
(8, 'ventas', 'fecha', 'fecha', 'Fecha', '', '', ''),
(9, 'ventas', 'domicilio', 'idcliente', 'Domicilio del cliente', 'clientes', 'idcliente', 'domicilio'),
(10, 'ventas', 'usuario', 'idusuario', 'Usuario que cerró la venta', 'usuarios', 'idusuario', 'nombre'),

(11, 'clientes', 'nombre', 'nombre', 'Nombre', '', '', ''),
(12, 'clientes', 'domicilio', 'domicilio', 'Domicilio', '', '', ''),

(13, 'servicios', 'cliente', 'idcliente', 'Nombre del cliente', 'clientes', 'idcliente', 'nombre'),
(14, 'servicios', 'asunto', 'titulo', 'Asunto', '', '', ''),
(15, 'servicios', 'idservicio', 'idservicio', 'Nº de servicio', '', '', ''),
(16, 'servicios', 'usuario', 'idusuario', 'Usuario responsable', 'usuarios', 'idusuario', 'nombre'),

(17, 'compras', 'proveedor', 'idproveedor', 'Nombre del proveedor', 'proveedores', 'idproveedor', 'nombre'),
(18, 'compras', 'idcompra', 'idcompra', 'Nº de compra', '', '', ''),
(19, 'compras', 'numero', 'numerocompleto', 'Nº del comprobante', '', '', ''),
(20, 'compras', 'tipocompra', 'idtipocompra', 'Tipo de compra', 'categorias_compras', 'idtipocompra', 'nombre'),
(21, 'compras', 'total', 'total', 'Total', '', '', ''),
(22, 'compras', 'vencimiento1', 'vencimiento1', '1º vencimiento', '', '', ''),
(23, 'compras', 'vencimiento2', 'vencimiento2', '2º vencimiento', '', '', ''),
(24, 'compras', 'fecha', 'fecha', 'Fecha', '', '', ''),
(25, 'compras', 'domicilio', 'idproveedor', 'Domicilio del proveedor', 'proveedores', 'idproveedor', 'domicilio'),
(26, 'compras', 'usuario', 'idusuario', 'Usuario que cerró la compra', 'usuarios', 'idusuario', 'nombre'),

(27, 'ventas', 'saldo', 'saldo', 'Saldo de la venta', '', '', ''),
(28, 'ventas', 'saldo-cliente', 'idventa', 'Saldo del cliente', 'clientes', 'idcliente', 'saldo'),
(29, 'clientes', 'saldo', 'saldo', 'Saldo del cliente', '', '', ''),
(30, 'ventas', 'mp', 'saldo', 'Botón de MP del saldo de la venta', '', '', ''),
(31, 'ventas', 'mp-venta-cliente', 'idventa', 'Botón de MP del saldo del cliente', 'clientes', 'idcliente', 'saldo'),
(32, 'clientes', 'mp-cliente', 'saldo', 'Botón de MP del saldo del cliente', '', '', '');

CREATE TABLE `tablas_tributos` (
  `idtipotributo` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `nombre` varchar(50) NOT NULL,
  PRIMARY KEY (`idtipotributo`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_tributos` (`idtipotributo`, `nombre`) VALUES
('1', 'Impuestos nacionales'),
('2', 'Impuestos provinciales'),
('3', 'Impuestos municipales'),
('4', 'Impuestos internos'),
('5', 'IIBB'),
('6', 'Percepción de IVA'),
('7', 'Percepción de IIBB'),
('8', 'Percepciones por Impuestos Municipales'),
('9', 'Otras Percepciones'),
('13', 'Percepción de IVA a no Categorizado '),
('99', 'Otros');

CREATE TABLE `tablas_unidades` (
  `idunidad` tinyint(1) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(20) NOT NULL,
  PRIMARY KEY `idunidad` (`idunidad`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tablas_unidades` (`idunidad`, `nombre`) VALUES
(0, 'Sin especificar'),
(7, 'unidad'),
(1, 'kilogramo'),
(14, 'gramo'),
(41, 'miligramo'),
(2, 'metro'),
(3, 'metro cuadrado'),
(4, 'metro cúbico'),
(62, 'pack'),
(63, 'horma'),
(8, 'par'),
(9, 'docena'),
(5, 'litro'),
(47, 'mililitro'),
(18, 'hectolitro'),
(11, 'millar'),
(15, 'milímetro'),
(16, 'milímetro cúbico'),
(17, 'kilometro'),
(20, 'centímetro'),
(27, 'centímetro cúbico'),
(10, 'quilate'),
(29, 'tonelada'),
(6, '1000 kW/h'),
(30, 'decámetro cúbico'),
(31, 'hectómetro cúbico'),
(32, 'kilómetro cúbico'),
(33, 'micro gramo'),
(34, 'nano gramo'),
(35, 'pico gramo'),
(21, 'kilogramo activo'),
(22, 'gramo activo'),
(23, 'gramo base'),
(24, 'UIACTHOR'),
(25, 'JPMN'),
(26, 'MUIACTHOR'),
(28, 'UIACTANT'),
(36, 'MUIACTANT'),
(37, 'UIACTIG'),
(48, 'curie'),
(49, 'milicurie'),
(50, 'microcurie'),
(51, 'UIAH'),
(52, 'MUIAH'),
(53, 'kilogramo base'),
(54, 'gruesa'),
(55, 'MUIACTIG'),
(61, 'kilogramo bruto'),
(19, 'MUIAI'),
(12, 'MUIAA'),
(13, 'UIAI'),
(97, 'seña o anticipo'),
(98, 'otra unidad'),
(99, 'bonificación');

CREATE TABLE `tareas` (
  `idtarea` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idusuario` smallint(5) unsigned NOT NULL DEFAULT '1',
  `prioridad` tinyint(1) unsigned NOT NULL DEFAULT '3',
  `texto` text NOT NULL,
  PRIMARY KEY (`idtarea`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `textosxtickets` (
  `idtextoxticket` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idticket` mediumint(8) unsigned NOT NULL,
  `idusuario` int(10) unsigned NOT NULL,
  `fecha` datetime NOT NULL,
  `texto` text NOT NULL,
  PRIMARY KEY (`idtextoxticket`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `tickets` (
  `idticket` mediumint(8) unsigned NOT NULL,
  `idusuario` smallint(5) unsigned NOT NULL,
  `estado` enum('abierto', 'cerrado', 'pausado', 'pendiente', 'desarrollo') NOT NULL DEFAULT 'abierto',
  `titulo` varchar(200) NOT NULL,
  PRIMARY KEY (`idticket`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `tienda` (
  `idtienda` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `tienda_estado` tinyint(1) unsigned NOT NULL,
  `tienda_pausa` tinyint(1) NOT NULL,
  `tienda_nombre` varchar(60) NOT NULL,
  `estilo_tienda` char(20) NOT NULL,
  `tienda_idtipoventa` tinyint(3) unsigned NOT NULL,
  `tienda_idtipoventa_a` tinyint(3) unsigned NOT NULL,
  `tienda_idtipoventa_b` tinyint(3) unsigned NOT NULL,
  `tienda_sinstock` tinyint(1) unsigned NOT NULL,
  `loginsinpw` tinyint(1) unsigned NOT NULL,
  `API_estado` tinyint(1) unsigned NOT NULL,
  `API_secret` char(32) NOT NULL,
  `ML_estado` tinyint(1) unsigned NOT NULL,
  `ML_user_id` bigint(10) unsigned NOT NULL,
  `ML_access_token` char(81) NOT NULL,
  `ML_refresh_token` varchar(40) NOT NULL,
  `ML_expires_in` int(10) unsigned NOT NULL,
  `ML_idtipoventa` tinyint(3) unsigned NOT NULL,
  `ML_iddeposito_full` tinyint(1) unsigned NOT NULL,
  `ML_idiva` tinyint(1) unsigned NOT NULL DEFAULT '5',
  `MP_estado` tinyint(1) unsigned NOT NULL,
  `MP_access_token` char(81) NOT NULL,
  `MP_refresh_token` char(27) NOT NULL,
  `MP_expires_in` int(10) unsigned NOT NULL,
  `MP_grant_type` varchar(60) NOT NULL,
  `MP_client_id` bigint(20) NOT NULL,
  `MP_client_secret` char(32) NOT NULL,
  `MP_idtipocaja` int(10) unsigned NOT NULL,
  `MP_idconcepto` smallint unsigned NOT NULL,
  `tienda_idtipocliente` smallint(3) unsigned NOT NULL,
  `ML_idtipocliente` smallint(3) unsigned NOT NULL,
  `idsmtp` smallint unsigned NOT NULL,
  `idplantilla_venta_tienda` smallint unsigned NOT NULL,
  `idplantilla_venta_ML` smallint unsigned NOT NULL,
  `idplantilla_venta_MP` smallint unsigned NOT NULL,
  `idplantilla_alta_usuario` smallint unsigned NOT NULL,
  `idplantilla_resetear_pass` smallint unsigned NOT NULL,
  `puntodeventa_predeterminado_api_facturacion_masiva` mediumint(5) unsigned NOT NULL DEFAULT '0',
  `puntodeventa_predeterminado_ml_facturacion_masiva` mediumint(5) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`idtienda`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `tienda` (`idtienda`, `tienda_estado`, `tienda_nombre`, `estilo_tienda`, `tienda_idtipoventa`, `tienda_sinstock`, `API_estado`, `ML_estado`, `ML_user_id`, `ML_access_token`, `ML_refresh_token`, `ML_expires_in`, `ML_idtipoventa`, `ML_iddeposito_full`, `ML_idiva`, `MP_estado`, `MP_access_token`, `MP_refresh_token`, `MP_expires_in`, `MP_grant_type`, `MP_client_id`, `MP_client_secret`, `MP_idtipocaja`, `MP_idconcepto`, `idplantilla_venta_tienda`, `idplantilla_venta_ML`, `idplantilla_venta_MP`, `idplantilla_alta_usuario`, `idplantilla_resetear_pass`) VALUES
(1, 0, 'Cuenta Principal', '', 0, 0, 0, 1, 0, '', '', 0, 0, 0, 2, 0, '', '', 0, '', '', '', 0, 0, 0, 0, 0, 0, 0);

CREATE TABLE `traslados` (
  `idtraslado` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `estado` enum('abierto', 'enviado', 'recibido', 'anulado') NOT NULL DEFAULT 'abierto',
  `fechainicio` datetime NOT NULL,
  `fechafin` datetime NOT NULL,
  `iddepositoinicio` tinyint(1) unsigned NOT NULL,
  `iddepositofin` tinyint(1) unsigned NOT NULL,
  `idusuarioinicio` smallint(5) unsigned NOT NULL,
  `idusuariofin` smallint(5) unsigned NOT NULL,
  `obsinicio` text NOT NULL,
  `obsfin` text NOT NULL,
  PRIMARY KEY (`idtraslado`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `trazabilidades` (
  `idtrazabilidad` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idproducto` int(10) unsigned NOT NULL,
  `cantidad` int(10) unsigned NOT NULL,
  `codigo` varchar(60) NOT NULL,
  `vencimiento` date NOT NULL,
  `idarchivo` int(10) unsigned NOT NULL,
  `observacion` text NOT NULL,
  PRIMARY KEY (`idtrazabilidad`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `tributosxcompras` (
  `idtributoxcompra` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idtributo` int(11) unsigned NOT NULL,
  `idcompra` int(11) unsigned NOT NULL,
  `baseimponible` decimal(15,2) NOT NULL,
  `alicuota` decimal(15,2) NOT NULL,
  `importe` decimal(15,2) NOT NULL,
  PRIMARY KEY (`idtributoxcompra`),
  INDEX `idcompra` (`idcompra`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `tributosxventas` (
  `idtributoxventa` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `idtributo` int(11) unsigned NOT NULL,
  `idventa` int(11) unsigned NOT NULL,
  `baseimponible` decimal(15,2) NOT NULL,
  `alicuota` decimal(15,2) NOT NULL,
  `importe` decimal(15,2) NOT NULL,
  PRIMARY KEY (`idtributoxventa`),
  INDEX `idventa` (`idventa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `usuarios` (
  `idusuario` smallint(5) unsigned NOT NULL,
  `iuu` char(20) NOT NULL,
  `estado` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `nombre` varchar(60) NOT NULL,
  `nombrereal` varchar(60) NOT NULL,
  `telefonos` varchar(150) NOT NULL,
  `mail` varchar(320) NOT NULL,
  `idperfil` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `idestilo` tinyint(1) unsigned NOT NULL DEFAULT '2',
  `estilo_oscuro` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `ididioma` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `fondo` varchar(20) NOT NULL,
  `obsinterna` text NOT NULL,
  PRIMARY KEY (`idusuario`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

INSERT INTO `usuarios` (`idusuario`, `estado`, `nombre`, `nombrereal`) VALUES
('0', '0', 'Sin especificar', 'Sin especificar');

CREATE TABLE `variables` (
  `variable` varchar(100) NOT NULL,
  `valor` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO `variables` (`variable`, `valor`) VALUES
('ultimodolar', 0),
('puntodeventa_predeterminado_ventas_facturacion_masiva', 0),
('puntodeventa_predeterminado_api_facturacion_masiva', 0),
('puntodeventa_predeterminado_ml_facturacion_masiva', 0);

CREATE TABLE `ventas` (
  `idventa` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idtipoventa` tinyint(1) unsigned NOT NULL,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `estado` enum('abierto','cerrado','anulado','borrado') NOT NULL DEFAULT 'abierto',
  `situacion` enum('sin_especificar', 'pendiente', 'aprobado', 'rechazado') NOT NULL DEFAULT 'sin_especificar',
  `estadocae` enum('sin_especificar', 'pendiente', 'aprobado', 'rechazado') NOT NULL DEFAULT 'sin_especificar',
  `idusuario` smallint(5) unsigned NOT NULL,
  `idcliente` int(10) unsigned NOT NULL,
  `iddeposito` tinyint(1) unsigned NOT NULL,
  `idlista` tinyint(1) unsigned NOT NULL,
  `idrelacion` mediumint(8) unsigned NOT NULL,
  `tiporelacion` enum('servicio','abono') DEFAULT NULL,
  `condicionventa` enum('contado','cuentacorriente','debito','credito','cheque','ticket','transferencia','otros_electronicos','otra') NOT NULL DEFAULT 'contado',
  `concepto` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `fecha` datetime NOT NULL,
  `fechainicio` date NOT NULL,
  `fechafin` date NOT NULL,
  `vencimiento1` date NOT NULL,
  `vencimiento2` date NOT NULL,
  `numero` int(9) unsigned NOT NULL,
  `muevesaldo` tinyint(1) unsigned NOT NULL,
  `muevestock` tinyint(1) unsigned NOT NULL,
  `operacioninversa` tinyint(1) unsigned NOT NULL,
  `subtotal` decimal(15,2) unsigned NOT NULL,
  `descuento` decimal(4,2) unsigned NOT NULL,
  `neto` decimal(15,2) unsigned NOT NULL,
  `nogravado` decimal(15,2) unsigned NOT NULL,
  `exento` decimal(15,2) unsigned NOT NULL,
  `iva` decimal(15,2) unsigned NOT NULL,
  `tributos` decimal(15,2) unsigned NOT NULL,
  `total` decimal(15,2) unsigned NOT NULL,
  `observacion` text NOT NULL,
  `cae` char(14) NOT NULL,
  `vencimientocae` date NOT NULL,
  `obscae` text NOT NULL,
  `idtipoiva` tinyint(1) unsigned NOT NULL,
  `cuit` bigint(11) unsigned NOT NULL,
  `tipodoc` tinyint(3) unsigned NOT NULL DEFAULT '96',
  `dni` int(8) unsigned NOT NULL,
  `razonsocial` varchar(150) NOT NULL,
  `domicilio`varchar(150) NOT NULL,
  `idlocalidad` smallint(3) unsigned NOT NULL,
  `ML_order_id` bigint(10) unsigned NOT NULL,
  `MP_external_reference` char(20) NOT NULL,
  `obsML` text NOT NULL,
  `closed_at` timestamp NOT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`idventa`),
  INDEX `estado` (`estado`),
  INDEX `situacion` (`situacion`),
  INDEX `estadocae` (`estadocae`),
  INDEX `idtipoventa` (`idtipoventa`),
  INDEX `numero` (`numero`),
  INDEX `idusuario` (`idusuario`),
  INDEX `idcliente` (`idcliente`),
  INDEX `fecha` (`fecha`),
  INDEX `MP_external_reference` (`MP_external_reference`),
  INDEX `ventas_idx_estado_situacion_idusuario` (`estado`,`situacion`,`idusuario`),
  INDEX `tiporelacion+idrelacion` (`tiporelacion`, `idrelacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `ventas_ml` (
  `idventa` mediumint(8) unsigned NOT NULL,
  `ML_order_id` bigint(10) unsigned NOT NULL,
  `ML_pack_id` bigint(20) unsigned NOT NULL,
  `ML_shipping_id` bigint(10) unsigned NOT NULL,
  `idtienda` tinyint(1) unsigned NOT NULL,
  UNIQUE KEY `idventa+ML_order_id` (`idventa`,`ML_order_id`),
  INDEX `ML_shipping_id` (`ML_shipping_id`),
  INDEX `ML_pack_id` (`ML_pack_id`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `ventaspagos` (
  `idventapago` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `idcliente` int(10) unsigned NOT NULL,
  `idusuario` smallint(5) unsigned NOT NULL,
  `idventa` mediumint(8) unsigned NOT NULL,
  `idmoneda` smallint(3) unsigned NOT NULL DEFAULT 1,
  `idformapago` tinyint(1) unsigned NOT NULL,
  `tiporelacion` enum('', 'cheque', 'retencion') NOT NULL,
  `idrelacion` int NOT NULL,
  `idnumeroventapago` mediumint (8) NOT NULL,
  `fecha` datetime NOT NULL,
  `total` decimal(15,2) NOT NULL,
  `observacion` text NOT NULL,
  `MP_operation_id` bigint(10) unsigned NOT NULL,
  `MP_external_reference` char(20) NOT NULL,
  `ML_order_id` bigint(10) unsigned NOT NULL,
  `closed_at` timestamp NOT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`idventapago`),
  INDEX `idventa` (`idventa`),
  INDEX `ML_order_id` (`ML_order_id`),
  INDEX `MP_operation_id` (`MP_operation_id`),
  INDEX `tiporelacion+idrelacion` (`tiporelacion`, `idrelacion`),
  INDEX `idnumeroventapago` (`idnumeroventapago`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `ventasxclientes` (
  `idventaxcliente` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `idcliente` int(10) unsigned NOT NULL,
  `idtipoventa` tinyint(1) signed NOT NULL,
  `id` mediumint(8) unsigned NOT NULL,
  `fecha` datetime NOT NULL,
  `total` decimal(15,2) NOT NULL,
  `numero` char(16) NOT NULL,
  UNIQUE KEY `idclientescc` (`idventaxcliente`),
  INDEX `idcliente` (`idcliente`),
  INDEX `id` (`id`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;

CREATE TABLE `ventasxventas` (
  `idventa` mediumint(8) unsigned NOT NULL,
  `idrelacion` mediumint(8) unsigned NOT NULL,
  UNIQUE KEY `idventa` (`idventa`,`idrelacion`)
) ENGINE=InnoDB DEFAULT charSET=utf8mb4;
