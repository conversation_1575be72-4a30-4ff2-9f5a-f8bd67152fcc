ALTER TABLE `ventas` CHANGE `condicionventa` `condicionventa` ENUM(
        'contado',
        'cuentacorriente',
        'debito',
        'credito',
        'cheque',
        'ticket',
        'transferencia',
        'otros_electronicos',
        'otra'
    ) NOT NULL DEFAULT 'contado';

ALTER TABLE `compras` CHANGE `condicionventa` `condicionventa` ENUM(
        'contado',
        'cuentacorriente',
        'debito',
        'credito',
        'cheque',
        'ticket',
        'transferencia',
        'otros_electronicos',
        'otra'
    ) NOT NULL DEFAULT 'contado';

ALTER TABLE `categorias_clientes` CHANGE `condicion` `condicion` ENUM(
        'contado',
        'cuentacorriente',
        'debito',
        'credito',
        'cheque',
        'ticket',
        'transferencia',
        'otros_electronicos',
        'otra'
    ) NOT NULL DEFAULT 'contado';