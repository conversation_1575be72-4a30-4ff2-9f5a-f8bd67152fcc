<!DOCTYPE html>
<html>
<head>
    <title>Buscador de productos</title>
    <link rel="stylesheet" href="bootstrap.min.css">
</head>
<body>

<div class="container">

    <h1>Buscador de productos</h1>
    <div class="form-group">
        <input type="text" name="buscado" id="buscado" onkeypress="enter_buscar(event)" class="form-control" aria-describedby="buscar"><br>
        <div class="form-check form-check-inline ml-2">
            <input type="submit" name="buscar" id="buscar" value="Buscar" onclick="buscar()" class="btn btn-outline-secondary">&nbsp;
            <input type="checkbox" class="form-check-input" id="terminalPrecios" checked>
            <label class="form-check-label" for="terminalPrecios" title="Active esta opción si utiliza esta funcionalidad para una terminal de precios pública que no dispone de teclado o mouse">Terminal de precios</label>
        </div>
    </div>
    <br>

    <table class="table table-striped">
    </table>
</div>

<script src="jquery-2.2.4.min.js"></script>
<script src="buscador.js"></script>
<script>
    var iues = '<?php echo $_GET['iue']; ?>'.split(',');
    var empresas = '<?php echo $_GET['empresa']; ?>'.split(',');
    var cantidad = '<?php echo is_numeric($_GET['cantidad']) ? $_GET['cantidad'] : 10; ?>';

    $(function() {
        $("#buscado").focus();
    });
</script>

</body>
</html>