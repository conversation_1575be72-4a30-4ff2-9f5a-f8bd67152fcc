function enter_buscar(e) {
    if (e.keyCode == 13) {
        buscar();
        // Vaciar el campo de búsqueda solo si la opción "Terminal de precios" está marcada
        if ($("#terminalPrecios").is(":checked")) {
            $("#buscado").val("");
        }
    }
}

function buscar() {
    var table = $('table');
    var buscado = $("#buscado").val();

    var cargando = '<tr><td style="text-align: center;"><img src="https://app.saasargentina.com/estilos/estilo_1/images/cargando.gif"></td></tr>';
    table.html(cargando);

    $.each(iues, function(j, iue) {

        var thead = '<tr>'
            + '<th>Empresa</th>'
            + '<th>Código</th>'
            + '<th>Nombre</th>'
            + '<th>Precio</th>'
            + '<th>Precio Final</th>'
            + '<th>Stock</th>'
            + '</tr>';
        table.html(thead);

        var empresa = empresas[j].charAt(0).toUpperCase() + empresas[j].slice(1);
        var url = 'https://api.saasargentina.com/v1/productos/'
            + '?iue=' + iue
            + '&busqueda=' + buscado
            + '&cantidad=' + cantidad;

        $.getJSON(url, null, function(data) {
            if (data.resultados.length) {
                $.each(data.resultados, function(i, item) {
                    var tr = '<tr>'
                        + '<td>' + empresa + '</td>'
                        + '<td>' + item.codigo + '</td>'
                        + '<td>' + item.nombre + '</td>'
                        + '<td>$ ' + item.precio + '</td>'
                        + '<td>$ ' + item.preciofinal + '</td>'
                        + '<td>' + item.stockactual + '</td>'
                        + '</tr>';
                    table.append(tr);
                });

            } else {
                var tr = '<tr><td style="text-align: center;" colspan="6">No se encontraron productos con esta búsqueda en ' + empresa + '</tr></td>';
                table.append(tr);
            }
        });

    });
}
