<?php
/*
  Script para conseguir los CAEs cuando ARCA se cae y genera inconsistencias
*/
// ini_set('display_errors', '1');

require __DIR__.'/../../acc/acc.php';
require PATH_BETA.'public/vendor/autoload.php';

$idempresas = in_array('idempresas', $argv)
    ? $argv[array_search('idempresas', $argv) + 1]
    : false;
$antiafip = true;
$guardar_salida = in_array('salida', $argv);

if ($idempresas) {
    $empresas = explode(',', $idempresas);

} else if (!is_readable(PATH_LOGS . 'sincae.csv')) {
    // Si no existe el archivo, termino el script
    exit('No existe o no es legible el archivo sincae.csv');

} else {
    $filas = file(PATH_LOGS . 'sincae.csv');
    $empresas = array_unique($filas);
    sort($empresas);
}

// Reseteo la sesión
session_unset();
session_destroy();
session_start();

//Especifico el nombre del módulo en plural y singular para ser utilizado en varias partes de inclusiones
$modulo = 'ventas';
$modulo_singular = 'venta';

// Empiezo el texto que voy a mandar por mail
$reporte = "<br /><br />\r\n\r\n<u><b>Informe del Antiafip</b></u><br>Iniciado a las ".date("d-m-Y H:i:s").'<br>';

try {

    $bd_saasargentina = mysqli_connect(BD_HOST, BD_USER, BD_PASS, BD_BD)
    or mostrar_error('Se produjo un error conectandose a la base de datos de saasargentina.com en el antiafip.php');
    mysqli_set_charset($bd_saasargentina, "utf8mb4");

    // Busco las empresas que tienen caes pendientes y las recorro
    $todas_ok = true; // Para controlar que todas terminen ok antes de borrar el log
    $idservidor = 0;
    $cantidades = ['empresas' => 0, 'ventas' => 0,
        'empresas_resueltas' => 0, 'ventas_resueltas' => 0];
    $empresas_resueltas_antes = array();
    foreach ($empresas as $idempresa) {

        $idempresa = (int)trim($idempresa);

        if ($idempresa <= 0) {
            $todas_ok = false; // Si alguna empresa no es númerica, algo anda mal
            continue;
        }

        // Busco los datos de conexión, me hago pasar como que está logueada y conecto a su bd
        $sql = "SELECT empresas.idempresa, empresas.nombre AS empresa, empresas.idservidor,
                servidores.BD_HOST, servidores.BD_USER, servidores.BD_PASS, servidores.BD_BD
            FROM empresas
                INNER JOIN servidores ON empresas.idservidor=servidores.idservidor
            WHERE idempresa = '$idempresa' LIMIT 1";
        $datos_servidor = mysqli_fetch_assoc(mysqli_query($bd_saasargentina, $sql));

        if (!$idservidor) {
            $idservidor = $datos_servidor['idservidor'];

            if ($idservidor == 7) {
                require_once PATH_SAAS.'public/librerias/funciones_modelo.php';
                require_once PATH_SAAS.'public/librerias/funciones_wsfe.php';
                require_once PATH_SAAS.'public/librerias/funciones_comprobantes.php';
                require_once PATH_SAAS.'public/librerias/funciones_aws.php';
            } else {
                require_once PATH_BETA.'public/librerias/funciones_modelo.php';
                require_once PATH_BETA.'public/librerias/funciones_wsfe.php';
                require_once PATH_BETA.'public/librerias/funciones_comprobantes.php';
                require_once PATH_BETA.'public/librerias/funciones_aws.php';
            }
        }

        // Sólo puedo procesar uno de beta y prod por separado cuando las librerías son incompatibles
        if ($idservidor != $datos_servidor['idservidor'])
            continue;

        $_SESSION['logsueado'] = true;
        $_SESSION['empresa_idempresa'] = $idempresa;
        $_SESSION['servidor_host'] = $datos_servidor['BD_HOST'];
        $_SESSION['servidor_user'] = $datos_servidor['BD_USER'];
        $_SESSION['servidor_pass'] = $datos_servidor['BD_PASS'];
        $_SESSION['servidor_bd'] = $datos_servidor['BD_BD'];

        $bd_link = conectar_db();

        $_SESSION['configuracion_cuit'] = campo_sql(consulta_sql(
            "SELECT cuit FROM configuraciones LIMIT 1"));
        $_SESSION['configuracion_discrimina'] = campo_sql(consulta_sql(
            "SELECT discrimina FROM tablas_condiciones WHERE idtipoiva =
                (SELECT idtipoiva FROM configuraciones LIMIT 1) LIMIT 1"));

        $resultado_sql = consulta_sql(
        "SELECT ventas.*,
            cv.ultimonumero, cv.letra, cv.nombre, cv.puntodeventa, cv.tipofacturacion
        FROM ventas
            LEFT JOIN categorias_ventas AS cv ON ventas.idtipoventa = cv.idtipoventa
        WHERE estadocae = 'pendiente'
            AND ventas.estado = 'cerrado'
        ORDER BY idtipoventa, numero, idventa");

        $cantidad_ventas = contar_sql($resultado_sql);
        if (!$cantidad_ventas) {
            $empresas_resueltas_antes[] = $idempresa;
            continue;
        }

        $reporte.= "<br><hr><br>".PHP_EOL;
        $reporte.= "Ventas para cerrar de $idempresa (CUIT {$_SESSION['configuracion_cuit']}): $cantidad_ventas<br>".PHP_EOL;
        $cantidades['empresas']++;
        $cantidades['ventas']+= $cantidad_ventas;
        $i = 1;
        $resultado = 'OK'; // Si una venta de esta empresa falla, sigo con la próxima

        while ($resultado == 'OK'
            && ($venta = array_sql($resultado_sql))) {

            $_SESSION['usuario_idusuario'] = $venta['idusuario'];

            // Controlo que sea electrónica
            if ($venta['tipofacturacion'] != 'electronico') {
                $reporte.= PHP_EOL."<br>".$i." - Venta con estadocae pendiente pero no electrónica PARA REVISAR ".$venta['nombre']." "
                .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])
                ." (idtipoventa ".$venta['idtipoventa']." | idventa ".$venta['idventa'].")";
                unset($venta['observacion']);
                mostrar_error('En ANTIAFIP hay una venta con estadocae pendiente pero no electrónica PARA REVISAR<br>'
                    .'tipofacturacion: '.$venta['tipofacturacion'].'<br>'
                    .'estado: '.$venta['estado'].'<br>'
                    .'cae: '.$venta['cae'].'<br>'
                    .'idempresa: '.$idempresa.':<br>'
                    .'venta: '.json_encode($venta), true);
                continue;
            }

            // Controlo si el CAE ya está ok
            if (mb_strlen($venta['cae']) == 14) {
                $cae_correcto = rece1_verificar($venta['idventa']);
                if ($cae_correcto)
                    guardar_sql('ventas', ['estadocae' => 'aprobado'], $venta['idventa']);

                $reporte.= PHP_EOL."<br>".$i." - Venta a APROBADO ".$venta['nombre']." "
                    .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])
                    ." (idtipoventa ".$venta['idtipoventa']." | idventa ".$venta['idventa'].")";
                continue;
            }

            // Desactivo la valido que la factura se pueda aprobar
            if (false && !rece1_validar($venta['idventa'])) { // EN ALFA CAMBIA A validar_fe
                $reporte.= PHP_EOL."<br>".$i." - Venta con estadocae pendiente pero no se puede aprobar ".$venta['nombre']." "
                    .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])
                    ." (idtipoventa ".$venta['idtipoventa']." | idventa ".$venta['idventa'].")";
                $resultado = 'SINCAE';
                continue;
            }

            // Reviso que esté ok el tipodoc
            if (strlen($venta['cuit']) == 11 && $venta['tipodoc'] != 80) {
                $venta['tipodoc'] = 80;
                guardar_sql('ventas', ['tipodoc' => 80], $venta['idventa']);
            } else if (!$venta['cuit'] && !$venta['dni'] && $venta['tipodoc'] != 99) {
                $venta['tipodoc'] = 99;
                guardar_sql('ventas', ['tipodoc' => 99], $venta['idventa']);
            } else if (!$venta['cuit'] && strlen($venta['dni']) >= 5 && (!$venta['tipodoc'] || $venta['tipodoc'] == 99)) {
                $venta['tipodoc'] = 96;
                guardar_sql('ventas', ['tipodoc' => 96], $venta['idventa']);
            }

            comprobantes_recalculando($venta['idventa']);
            $resultado = rece1($venta['idventa'], $antiafip, $guardar_salida);

            $reporte.= PHP_EOL."<br>".$i." - Resultado de ".$venta['nombre']." "
                .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])
                ." (idtipoventa ".$venta['idtipoventa']." | idventa ".$venta['idventa']."): " . $resultado;

            if ($resultado == 'OK') {
                $cantidades['ventas_resueltas']++;
            } else {
                $todas_ok = false; // Marco que alguna empresa falló
                $reporte.= '<a href="https://scripts.saasargentina.com/numeracion.php?a=cae'
                    .'&idempresa='.$idempresa
                    .'&idtipoventa='.$venta['idtipoventa']
                    .'&numero_desde='.($venta['numero'] - 5)
                    .'&numero_hasta='.($venta['numero'] + 10)
                    .'">Probar con el recuperador de cae</a>'
                    .' - <a href="https://scripts.saasargentina.com/numeracion.php?a=anular'
                    .'&idempresa='.$idempresa
                    .'&idventa='.$venta['idventa']
                    .'">Anular</a>'
                    .PHP_EOL;
            }

            $i++;
            sleep(1);

        }

        if ($resultado == 'OK')
            $cantidades['empresas_resueltas']++;

        mysqli_close($bd_link);

    }

    if ($todas_ok && !$idempresas) {
        // Limpio el log de empresas que tienen caes pendientes si no se llamó a una particular
        unlink(PATH_LOGS . 'sincae.csv');
        $reporte.= '<hr><br>Limpio log';

    } else {
        // Termino texto y envío mail
        $reporte.= "<hr><br>Terminado a las ".date("d-m-Y H:i:s").'<br><br>'.PHP_EOL
            .'Cantidad de empresas totales: '.$cantidades['empresas'].'<br>'.PHP_EOL
            .'Cantidad de empresas resueltas: '.$cantidades['empresas_resueltas'].'<br>'.PHP_EOL
            .'Cantidad de empresas pendientes: '.($cantidades['empresas'] - $cantidades['empresas_resueltas']).'<br><br>'.PHP_EOL
            .'Cantidad de ventas totales: '.$cantidades['ventas'].'<br>'.PHP_EOL
            .'Cantidad de ventas resueltas: '.$cantidades['ventas_resueltas'].'<br>'.PHP_EOL
            .'Cantidad de ventas pendientes: '.($cantidades['ventas'] - $cantidades['ventas_resueltas']).'<br><br>'.PHP_EOL
            .'Cantidad de empresas resueltas antes: '.count($empresas_resueltas_antes).'<br>'.PHP_EOL
            .'Empresas resueltas antes: '.implode(', ', $empresas_resueltas_antes);

        if (ESTADO != 'desarrollo') {
            email_queue(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ANTIAFIP', $reporte, false);
        }
    }

} catch(Exception $e) {
    email_queue(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ERROR en ANTIAFIP', $reporte, false);

}

// Destruyo la sesión
session_unset();
session_destroy();

exit($reporte);
