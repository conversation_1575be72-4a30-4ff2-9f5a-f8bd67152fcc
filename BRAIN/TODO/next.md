# NEXT 🏹

## FRAMEWORK

- No más Week, ni deadlines, todos flujos continuos @flow para hacer cuando quiera (estoy orgulloso de poder trabajar sólo cuando tengo ganas y de lo que tengo ganas)
- Cada tanto utilizo @next para rellenar los flujos:
  - [ ] Actualizar el [BUEN DÍA](buendia.md)
  - [ ] Limpiar el [INBOX](../inbox.md)
  - [ ] Ordenar los DEV y SOPORTE de [SAAS 📦](../ROADS/SAAS/SAAS.md)
  - [ ] Ordenar los DEV y SOPORTE de [CRONO 🏅](../ROADS/CRONO/CRONO.md)
  - [ ] Revisar [AGENTE 🤖](../ROADS/AGENTE/AGENTE.md)
  - [ ] Revisar los ROADS:
    - [QUIEROS](../ROADS/NIRVANA/QUIEROS.md)
    - [PLAY](../ROADS/NIRVANA/PLAY.md)
    - [DIY](../ROADS/NIRVANA/DIY.md)
    - [PAISAJES](../ROADS/NIRVANA/PAISAJES.md)
    - [BRAIN](../ROADS/BRAIN/BRAIN.md)


*******************************************************************************

## ORDENAR

## SOPORTE

- [ ] Coordinar para compatibilizar fotocélulas con Alejandro Malnis

## MOBILE

- [ ] Coordinar mis 2 sponsors para el tría

## BRAIN

- [ ] Estudiar AI (vídeo de Platzi, estadística y ver que más, armar lista)

## OFFLINE


## PLAY

- [ ] Imprimir tapa rocola
- [ ] Imprimir entradas


*******************************************************************************

## NEXT MILESTONES

**PLAN INTEGRACIONES**

OBJETIVO: Terminar de estructurar el plan de integraciones y que termine en issues claros

- [ ] https://gitlab.com/saasargentina/app/-/issues/2152

**AGENTE MCP Consultas**
_Hasta tener un sincronizador y re ordenar proyecto_

**Evaluar Transferencias Inteligentes**
  [Transferencias Inteligentes (#303)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/303)

**EVENTOS Alta**
  - Ver si podemos poner la compra de promos que las cargue Juli y que se carguen en SaaS Automático
  - Agregar para ver usuarios

**API V2**

OBJETIVO: Definir framework para poder contar con esos archivos en API ARCA y empezar a avanzar con MCP e integraciones con una base sólida

- [ ] [API V2](https://gitlab.com/saasargentina/app/-/issues/2037) Tener un framework completo pero sólo Hola Mundo mostrando un producto
- [ ] [API V2 Productos](https://gitlab.com/saasargentina/app/-/issues/2091) Tener todos los recursos de productos

**SAAS Ayuda**

**Picos de consumo**
  - [Picos de consumo (#300)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/300)


*******************************************************************************

## WAITING

