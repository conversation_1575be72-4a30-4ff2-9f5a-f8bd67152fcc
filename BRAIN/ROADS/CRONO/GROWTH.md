# 🏅 ROADS > CRONO > GROWTH BOARD

## TODO ORDENAR

- [ ] Priscila modificaciones en el sitio web
    - Cambios en "Home"
    - Cambiar el listado de deportes por los 9 deportes principales, supongo que lo mejor es usar 3 líneas y 3 columnas.
    - Cambiar los nombres en los logos, siguiendo el listado de deportes, donde dice Gestión completa para ..., por ej. de "Downhill" por "Ciclismo"
    - Intercambiar el orden entre los deportes, dejar como el listado de arriba: Ciclismo, Running, Rally, Motociclismo, Aguas Abiertas, etc.
    - Agregar botones de los deportes con el texto: "Más información de Ciclismo" que lleve a la página de cada deporte
    - Hay que crear un menú en el sitio, antes de Tecnologías, que sea Deportes y que tenga la lista de los 9 deportes principales, cada uno lleva a la página correspondiente. También hay que crear 9 páginas, una para cada deporte, supongo que conviene crear la y duplicarla.

- [ ] Mover Apple a la LLC (https://mail.google.com/mail/u/0/#inbox/********************************)
- [ ] Reunión Andres Colombia y Preparar Contrato
- [ ] Manual de marca en sitio exclusivo
- [ ] Registrar la marca

---

## GROWTH

Siguiendo con el plan general 2024-2026, vamos a apuntar a las siguientes estrategias:

- *Estrategia SEO/AEO*: para eso vamos a mejorar la arquitectura de metadatos, generar sector de eventos, generar sectir de cursos y sitios.
- *Estrategia de deportes*: para eso vamos a generar las páginas en nuestro sitio y los cambios en el sistema.
- *Estrategia de contenido*: siguiendo el proceso (framework) de abajo y generando desde el listado de ideas.

## CONTENIDOS IDEAS

Para después tenemos un gran listado de IDEAS en [CONTENIDO](./CONTENIDO.md) para ir ordenando Y procesando.


## FRAMEWORK CONTENIDO

- Antes de generar las ideas, hay que analizar las metricas de los contenidos anteriores y ver que se puede mejorar o que es más efectivo.

**Preparo el guíon**

- Hacer un resumen de la idea, el objetivo y el contenido ya pensado. Se puede utilizar un audio y pasarlo por alguna AI para limpiar la idea.
- Generar el contenido con el prompt AI [CRONO_generar_contenido](/BRAIN/AI/CRONO_generar_contenido.md).

**Grabo el vídeo**

- Armar set (Luces: ventana, spot Juli para fondo, foco lectura sobre cama y luz pieza / Usar mic / Trípode sobre cama / Usar doble cámara con las mismas resoluciones)
- Grabar vídeos, recortar con Capcut, subir a Drive y generar los enlaces para el editor.

**Delego la publicación de contenido**

- Escribir una tarea para Prisci con las modificaciones en la WEB si son necesarias.
- Escribir una tarea para Prisci si va a llevar publicidad o diseño en la web.
- Escribir una orden de contenido (puede ser que requiera dividir el prompt AI), generar html y delegarlo por Whatsapp. Guardar el MD y HTML en CRONO.
- Escribir newsletter invitando a leer el contenido.
- Eliminar la tarea de CONTENIDOS y confiar en que Juli controla el proceso.


## MKT ORDENAR

- [ ] [Unlock SEO Potential with AI-Powered Keyword Research](https://neilpatel.com/blog/ai-keyword-research/?utm_medium=email&utm_source=convertkit-list&utm_campaign=us-mkt-campaigns-mql-content-blog-promotional-email&utm_content=blog-promote&utm_term=ck-november-9&cf-last-campaign-source=US%20-%20Mkt%20Campaigns%20-%20MQL%20-%20Content%20-%20Blog%20Promotional%20Email) @next
- [ ] Todas las herramientas de Google super configuradas y monitoreadas @next
- [ ] https://neilpatel.com/blog/google-analytics-4/
- [ ] [Accept the new Terms of Service to upgrade your Google Analytics for Firebase property - Analytics Help](https://support.google.com/analytics/answer/10960488)
- [ ] Configurar acceso a Pri también
- [ ] Para monitorear temas de SEO [Telescope - Usage-Based SEO Research Tool](https://withtelescope.com/)
- [ ] [Guía completa para evitar el contenido duplicado con enlaces canonicals - ▶️ Blog de Marketing Digital - Elabs Consulting](https://www.elabsconsulting.com/noticias/guia-completa-para-evitar-el-contenido-duplicado-con-enlaces-canonicals)
- [ ] Comparar como ChatGPT conoce a la empresa
- [ ] Más revisiones de SEO a la web y blog
- [ ] [Configuring “Schema Markup” in Rank Math » Rank Math](https://rankmath.com/kb/rich-snippets/)
- [ ] [AutoOptimize.ai - Automatically Optimize Your Site Conversion Rate](https://www.autooptimize.ai/?coupon=fbspecial)
- [ ] [12 Dead Simple Steps To Complete Your First SEO Audit for Free with Rank Math](https://rankmath.com/blog/seo-audit/)
- [ ] [Google now shows why it ranked a specific search result](https://searchengineland.com/google-now-shows-why-it-ranked-a-specific-search-result-350659?utm_source=pocket-app&utm_medium=share)
- [ ] [INP (Interaction to Next Paint): A Guide To Core Web Vital Changes](https://neilpatel.com/blog/inp/)
- [ ] Agregar los productos de Crono a Google Merchant Center
- [ ] Ofrecer software y RFID en ML
- [ ] Evaluar el mercado usando AI [Home - AI teammates for Your Startup](https://www.frederick.ai/)
