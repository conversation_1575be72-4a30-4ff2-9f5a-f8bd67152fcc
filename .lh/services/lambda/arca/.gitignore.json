{"sourceFile": "services/lambda/arca/.gitignore", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1742907214692, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750517293251, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,3 +1,5 @@\n vendor\n .serverless\n-.env\n\\ No newline at end of file\n+.env\n+.lh\n+afip-facturas.log\n\\ No newline at end of file\n"}], "date": 1742907214692, "name": "Commit-0", "content": "vendor\n.serverless\n.env"}]}