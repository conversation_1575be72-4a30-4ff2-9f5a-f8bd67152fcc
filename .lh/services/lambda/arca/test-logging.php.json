{"sourceFile": "services/lambda/arca/test-logging.php", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750517304067, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750517304067, "name": "Commit-0", "content": "#!/usr/bin/env php\n<?php declare(strict_types=1);\n\n/**\n * Script de prueba para el sistema de logging de facturas\n * Uso: php test-logging.php [idempresa] [idventa]\n */\n\nrequire __DIR__ . '/vendor/autoload.php';\nrequire __DIR__ . '/lib/FacturaLogger.php';\n\nuse FuncionesComunes\\FacturaLogger\\FacturaLogger;\n\n// Obtener parámetros\n$idempresa = isset($argv[1]) ? (int)$argv[1] : 161;\n$idventa = isset($argv[2]) ? (int)$argv[2] : 9999;\n\necho \"🧪 Probando sistema de logging de facturas...\\n\";\necho \"   Empresa: $idempresa, Venta: $idventa\\n\\n\";\n\n// Simular variables de entorno para desarrollo\n$_ENV['APP_ENV'] = 'dev';\n$_ENV['AWS_REGION'] = 'sa-east-1';\n\n// Crear instancia del logger\n$logger = new FacturaLogger('dev');\n\necho \"📝 Probando log de factura aprobada...\\n\";\n$logger->logFactura([\n    'idempresa' => $idempresa,\n    'idventa' => $idventa,\n    'estado' => 'APROBADO',\n    'idtipoventa' => 1,\n    'numero' => '00001-00000123',\n    'fecha_venta' => date('Y-m-d'),\n    'total' => 1210.50,\n    'cuit' => '20123456789',\n    'dni' => '12345678',\n    'cae' => '74123456789012',\n    'obscae' => date('Ymd', strtotime('+10 days')),\n]);\n\necho \"📝 Probando log de factura con error...\\n\";\n$logger->logFactura([\n    'idempresa' => $idempresa,\n    'idventa' => $idventa + 1,\n    'estado' => 'ERROR',\n    'error' => 'Error de conexión con AFIP',\n    'idtipoventa' => 1,\n    'numero' => '00001-00000124',\n    'fecha_venta' => date('Y-m-d'),\n    'total' => 2420.75,\n    'cuit' => '20123456789',\n    'dni' => '87654321',\n]);\n\necho \"📝 Probando log de empresa pausada...\\n\";\n$logger->logFactura([\n    'idempresa' => $idempresa,\n    'idventa' => $idventa + 2,\n    'estado' => 'PAUSADO',\n    'error' => 'Empresa en pausa temporal',\n    'idtipoventa' => 1,\n    'numero' => '00001-00000125',\n    'fecha_venta' => date('Y-m-d'),\n    'total' => 550.00,\n    'cuit' => '20123456789',\n    'dni' => '11223344',\n]);\n\necho \"\\n✅ Pruebas completadas!\\n\";\necho \"📄 Para ver los logs locales: tail -f afip-facturas.log\\n\";\necho \"🔍 Para filtrar logs: grep '\\\"estado\\\":\\\"ERROR\\\"' afip-facturas.log\\n\";\n"}]}