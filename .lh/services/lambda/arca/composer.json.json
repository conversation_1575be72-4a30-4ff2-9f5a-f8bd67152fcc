{"sourceFile": "services/lambda/arca/composer.json", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1742907816911, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1748530109019, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,8 @@\n         \"vlucas/phpdotenv\": \"^5.6\"\n     },\n     \"autoload\": {\n         \"psr-4\": {\n-            \"FuncionesComunes\\\\\": \"layers/lib/\"\n+            \"FuncionesComunes\\\\\": \"layers/lib/Database.php\"\n         }\n     }\n }\n"}, {"date": 1750517276615, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,12 +1,13 @@\n {\n     \"require\": {\n         \"bref/bref\": \"^2.3\",\n         \"afipsdk/afip.php\": \"^1.1\",\n-        \"vlucas/phpdotenv\": \"^5.6\"\n+        \"vlucas/phpdotenv\": \"^5.6\",\n+        \"aws/aws-sdk-php\": \"^3.346\"\n     },\n     \"autoload\": {\n         \"psr-4\": {\n-            \"FuncionesComunes\\\\\": \"layers/lib/Database.php\"\n+            \"FuncionesComunes\\\\\": \"lib/\"\n         }\n     }\n }\n"}], "date": 1742907816911, "name": "Commit-0", "content": "{\n    \"require\": {\n        \"bref/bref\": \"^2.3\",\n        \"afipsdk/afip.php\": \"^1.1\",\n        \"vlucas/phpdotenv\": \"^5.6\"\n    },\n    \"autoload\": {\n        \"psr-4\": {\n            \"FuncionesComunes\\\\\": \"layers/lib/\"\n        }\n    }\n}\n"}]}