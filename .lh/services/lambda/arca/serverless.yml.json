{"sourceFile": "services/lambda/arca/serverless.yml", "activeCommit": 0, "commits": [{"activePatchIndex": 25, "patches": [{"date": 1740431439864, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1742902301098, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,6 @@\n service: arca\n+stage: dev\n \n provider:\n     name: aws\n     region: sa-east-1\n"}, {"date": 1742902377207, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,20 +1,29 @@\n service: arca\n-stage: dev\n \n provider:\n     name: aws\n     region: sa-east-1\n+    stage: dev\n \n plugins:\n     - ./vendor/bref/bref\n \n functions:\n     fe:\n-        handler: index.php\n-        description: ''\n-        runtime: php-83-fpm\n+        handler: src/fe.php\n+        description: 'Frontend Lambda function'\n+        runtime: php-83\n+        events:\n+            - http:\n+                path: /\n+                method: ANY\n \n # Exclude files from deployment\n package:\n     patterns:\n         - '!tests/**'\n+\n+custom:\n+    bref:\n+        phpFpm:\n+            version: 83\n"}, {"date": 1742907474826, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,9 +9,9 @@\n     - ./vendor/bref/bref\n \n functions:\n     fe:\n-        handler: src/fe.php\n+        handler: public/fe.php\n         description: 'Frontend Lambda function'\n         runtime: php-83\n         events:\n             - http:\n"}, {"date": 1744284686506, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,8 +3,11 @@\n provider:\n     name: aws\n     region: sa-east-1\n     stage: dev\n+    environment:\n+        ARCA_SAAS_CERT: ${env:ARCA_SAAS_CERT}\n+        ARCA_SAAS_KEY: ${env:ARCA_SAAS_KEY}\n \n plugins:\n     - ./vendor/bref/bref\n \n"}, {"date": 1744284702342, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,8 +6,9 @@\n     stage: dev\n     environment:\n         ARCA_SAAS_CERT: ${env:ARCA_SAAS_CERT}\n         ARCA_SAAS_KEY: ${env:ARCA_SAAS_KEY}\n+        SAAS_CUIT: ${env:SAAS_CUIT}\n \n plugins:\n     - ./vendor/bref/bref\n \n"}, {"date": 1744288998609, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,12 +3,8 @@\n provider:\n     name: aws\n     region: sa-east-1\n     stage: dev\n-    environment:\n-        ARCA_SAAS_CERT: ${env:ARCA_SAAS_CERT}\n-        ARCA_SAAS_KEY: ${env:ARCA_SAAS_KEY}\n-        SAAS_CUIT: ${env:SAAS_CUIT}\n \n plugins:\n     - ./vendor/bref/bref\n \n"}, {"date": 1748523375800, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,13 +7,24 @@\n \n plugins:\n     - ./vendor/bref/bref\n \n+# Definición de la capa (layer)\n+layers:\n+  database:\n+    path: layers\n+    name: ${self:service}-database-layer\n+    description: \"Layer containing Database class\"\n+    compatibleRuntimes:\n+      - php-83\n+\n functions:\n     fe:\n         handler: public/fe.php\n         description: 'Frontend Lambda function'\n         runtime: php-83\n+        layers:\n+          - {Ref: DatabaseLambdaLayer}\n         events:\n             - http:\n                 path: /\n                 method: ANY\n"}, {"date": 1748523600543, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -33,8 +33,9 @@\n package:\n     patterns:\n         - '!tests/**'\n \n+# Configuración personalizada\n custom:\n     bref:\n         phpFpm:\n             version: 83\n"}, {"date": 1748523861387, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,26 +2,29 @@\n \n provider:\n     name: aws\n     region: sa-east-1\n-    stage: dev\n+    stage: ${opt:stage, 'dev'}\n+    environment:\n+        APP_ENV: ${self:provider.stage}\n+        # Puedes añadir más variables específicas por stage si es necesario\n \n plugins:\n     - ./vendor/bref/bref\n \n # Definición de la capa (layer)\n layers:\n   database:\n     path: layers\n-    name: ${self:service}-database-layer\n-    description: \"Layer containing Database class\"\n+    name: ${self:service}-database-layer-${self:provider.stage}\n+    description: \"Layer containing Database class for ${self:provider.stage}\"\n     compatibleRuntimes:\n       - php-83\n \n functions:\n     fe:\n         handler: public/fe.php\n-        description: 'Frontend Lambda function'\n+        description: 'Frontend Lambda function (${self:provider.stage})'\n         runtime: php-83\n         layers:\n           - {Ref: DatabaseLambdaLayer}\n         events:\n"}, {"date": 1748524113402, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,9 +5,8 @@\n     region: sa-east-1\n     stage: ${opt:stage, 'dev'}\n     environment:\n         APP_ENV: ${self:provider.stage}\n-        # Puedes añadir más variables específicas por stage si es necesario\n \n plugins:\n     - ./vendor/bref/bref\n \n@@ -17,15 +16,15 @@\n     path: layers\n     name: ${self:service}-database-layer-${self:provider.stage}\n     description: \"Layer containing Database class for ${self:provider.stage}\"\n     compatibleRuntimes:\n-      - php-83\n+      - provided.al2  # Cambiado de php-83 a provided.al2\n \n functions:\n     fe:\n         handler: public/fe.php\n         description: 'Frontend Lambda function (${self:provider.stage})'\n-        runtime: php-83\n+        runtime: php-83  # Esto está bien porque Bref maneja esta transformación\n         layers:\n           - {Ref: DatabaseLambdaLayer}\n         events:\n             - http:\n@@ -36,9 +35,8 @@\n package:\n     patterns:\n         - '!tests/**'\n \n-# Configuración personalizada\n custom:\n     bref:\n         phpFpm:\n             version: 83\n"}, {"date": 1748525519736, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,24 +9,15 @@\n \n plugins:\n     - ./vendor/bref/bref\n \n-# Definición de la capa (layer)\n-layers:\n-  database:\n-    path: layers\n-    name: ${self:service}-database-layer-${self:provider.stage}\n-    description: \"Layer containing Database class for ${self:provider.stage}\"\n-    compatibleRuntimes:\n-      - provided.al2  # Cambiado de php-83 a provided.al2\n-\n functions:\n     fe:\n         handler: public/fe.php\n         description: 'Frontend Lambda function (${self:provider.stage})'\n-        runtime: php-83  # Esto está bien porque Bref maneja esta transformación\n+        runtime: php-83\n         layers:\n-          - {Ref: DatabaseLambdaLayer}\n+          - arn:aws:lambda:${self:provider.region}:#{AWS::AccountId}:layer:database-layer-${self:provider.stage}:1\n         events:\n             - http:\n                 path: /\n                 method: ANY\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,9 +15,10 @@\n         handler: public/fe.php\n         description: 'Frontend Lambda function (${self:provider.stage})'\n         runtime: php-83\n         layers:\n-          - arn:aws:lambda:${self:provider.region}:#{AWS::AccountId}:layer:database-layer-${self:provider.stage}:1\n+          - arn:aws:arn:aws:lambda:us-east-1:************:layer:database-layer-alfa:1\n+\n         events:\n             - http:\n                 path: /\n                 method: ANY\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,9 +15,9 @@\n         handler: public/fe.php\n         description: 'Frontend Lambda function (${self:provider.stage})'\n         runtime: php-83\n         layers:\n-          - arn:aws:arn:aws:lambda:us-east-1:************:layer:database-layer-alfa:1\n+          - arn:aws:lambda:sa-east-1:************:layer:database-layer-alfa:1\n \n         events:\n             - http:\n                 path: /\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -14,11 +14,11 @@\n     fe:\n         handler: public/fe.php\n         description: 'Frontend Lambda function (${self:provider.stage})'\n         runtime: php-83\n-        layers:\n-          - arn:aws:lambda:sa-east-1:************:layer:database-layer-alfa:1\n-\n+        # Eliminamos la referencia al layer\n+        # layers:\n+        #   - arn:aws:lambda:sa-east-1:************:layer:database-layer-${self:provider.stage}:1\n         events:\n             - http:\n                 path: /\n                 method: ANY\n"}, {"date": 1748609990807, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -14,19 +14,17 @@\n     fe:\n         handler: public/fe.php\n         description: 'Frontend Lambda function (${self:provider.stage})'\n         runtime: php-83\n-        # Eliminamos la referencia al layer\n-        # layers:\n-        #   - arn:aws:lambda:sa-east-1:************:layer:database-layer-${self:provider.stage}:1\n         events:\n             - http:\n                 path: /\n                 method: ANY\n \n # Exclude files from deployment\n package:\n     patterns:\n+        - '!node_modules/**'\n         - '!tests/**'\n \n custom:\n     bref:\n"}, {"date": 1748890125522, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,8 +19,26 @@\n             - http:\n                 path: /\n                 method: ANY\n \n+    afipsdk-processor:\n+        handler: public/afipsdk-processor.php\n+        description: 'Procesador de mensajes AFIP SDK (${self:provider.stage})'\n+        runtime: php-83\n+        events:\n+            - sqs:\n+                arn: !GetAtt AfipSdkQueue.Arn\n+                batchSize: 1\n+\n+resources:\n+    Resources:\n+        AfipSdkQueue:\n+            Type: AWS::SQS::Queue\n+            Properties:\n+                QueueName: afipsdk-queue-${self:provider.stage}\n+                VisibilityTimeout: 120\n+                MessageRetentionPeriod: 345600 # 4 días\n+\n # Exclude files from deployment\n package:\n     patterns:\n         - '!node_modules/**'\n"}, {"date": 1748890908636, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -25,20 +25,11 @@\n         description: 'Procesador de mensajes AFIP SDK (${self:provider.stage})'\n         runtime: php-83\n         events:\n             - sqs:\n-                arn: !GetAtt AfipSdkQueue.Arn\n+                arn: arn:aws:sqs:sa-east-1:************:afipsdk-queue-${self:provider.stage}\n                 batchSize: 1\n \n-resources:\n-    Resources:\n-        AfipSdkQueue:\n-            Type: AWS::SQS::Queue\n-            Properties:\n-                QueueName: afipsdk-queue-${self:provider.stage}\n-                VisibilityTimeout: 120\n-                MessageRetentionPeriod: 345600 # 4 días\n-\n # Exclude files from deployment\n package:\n     patterns:\n         - '!node_modules/**'\n"}, {"date": 1748891552445, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,18 @@\n     region: sa-east-1\n     stage: ${opt:stage, 'dev'}\n     environment:\n         APP_ENV: ${self:provider.stage}\n+    iam:\n+        role:\n+            statements:\n+                - Effect: Allow\n+                  Action:\n+                    - sqs:ReceiveMessage\n+                    - sqs:DeleteMessage\n+                    - sqs:GetQueueAttributes\n+                    - sqs:ChangeMessageVisibility\n+                  Resource: arn:aws:sqs:sa-east-1:************:afipsdk-queue-${self:provider.stage}\n \n plugins:\n     - ./vendor/bref/bref\n \n"}, {"date": 1748897193791, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,22 +20,13 @@\n plugins:\n     - ./vendor/bref/bref\n \n functions:\n-    fe:\n-        handler: public/fe.php\n-        description: 'Frontend Lambda function (${self:provider.stage})'\n+    afipsdk:\n+        handler: public/afipsdk.php\n+        description: 'Procesador AFIP SDK (${self:provider.stage})'\n         runtime: php-83\n         events:\n-            - http:\n-                path: /\n-                method: ANY\n-\n-    afipsdk-processor:\n-        handler: public/afipsdk-processor.php\n-        description: 'Procesador de mensajes AFIP SDK (${self:provider.stage})'\n-        runtime: php-83\n-        events:\n             - sqs:\n                 arn: arn:aws:sqs:sa-east-1:************:afipsdk-queue-${self:provider.stage}\n                 batchSize: 1\n \n"}, {"date": 1748897237465, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -32,9 +32,8 @@\n \n # Exclude files from deployment\n package:\n     patterns:\n-        - '!node_modules/**'\n         - '!tests/**'\n \n custom:\n     bref:\n"}, {"date": 1748978561829, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,8 +24,9 @@\n     afipsdk:\n         handler: public/afipsdk.php\n         description: 'Procesador AFIP SDK (${self:provider.stage})'\n         runtime: php-83\n+        timeout: 30  # Aumentar el timeout a 30 segundos\n         events:\n             - sqs:\n                 arn: arn:aws:sqs:sa-east-1:************:afipsdk-queue-${self:provider.stage}\n                 batchSize: 1\n"}, {"date": 1748979051611, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,16 @@\n     region: sa-east-1\n     stage: ${opt:stage, 'dev'}\n     environment:\n         APP_ENV: ${self:provider.stage}\n+    vpc:\n+        securityGroupIds:\n+            - sg-df2cdcb9  # Security Group que permite acceso a RDS\n+        subnetIds:\n+            - subnet-58a1d73c\n+            - subnet-1bb7b142\n+            - subnet-59a1d73d\n+            - subnet-804867d9\n     iam:\n         role:\n             statements:\n                 - Effect: Allow\n@@ -15,8 +23,22 @@\n                     - sqs:DeleteMessage\n                     - sqs:GetQueueAttributes\n                     - sqs:ChangeMessageVisibility\n                   Resource: arn:aws:sqs:sa-east-1:************:afipsdk-queue-${self:provider.stage}\n+                - Effect: Allow\n+                  Action:\n+                    - ec2:CreateNetworkInterface\n+                    - ec2:DescribeNetworkInterfaces\n+                    - ec2:DeleteNetworkInterface\n+                    - ec2:AttachNetworkInterface\n+                    - ec2:DetachNetworkInterface\n+                  Resource: \"*\"\n+                - Effect: Allow\n+                  Action:\n+                    - logs:CreateLogGroup\n+                    - logs:CreateLogStream\n+                    - logs:PutLogEvents\n+                  Resource: \"*\"\n \n plugins:\n     - ./vendor/bref/bref\n \n"}, {"date": 1748979072373, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -51,8 +51,9 @@\n         events:\n             - sqs:\n                 arn: arn:aws:sqs:sa-east-1:************:afipsdk-queue-${self:provider.stage}\n                 batchSize: 1\n+                maximumRetryAttempts: 0  # Desactivar reintentos automáticos\n \n # Exclude files from deployment\n package:\n     patterns:\n"}, {"date": 1748981660790, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -51,9 +51,8 @@\n         events:\n             - sqs:\n                 arn: arn:aws:sqs:sa-east-1:************:afipsdk-queue-${self:provider.stage}\n                 batchSize: 1\n-                maximumRetryAttempts: 0  # Desactivar reintentos automáticos\n \n # Exclude files from deployment\n package:\n     patterns:\n"}, {"date": 1750517297365, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,9 +36,11 @@\n                   Action:\n                     - logs:CreateLogGroup\n                     - logs:CreateLogStream\n                     - logs:PutLogEvents\n-                  Resource: \"*\"\n+                  Resource:\n+                    - \"*\"\n+                    - \"arn:aws:logs:sa-east-1:************:log-group:afip-facturas-log:*\"\n \n plugins:\n     - ./vendor/bref/bref\n \n"}], "date": 1740431439864, "name": "Commit-0", "content": "service: arca\n\nprovider:\n    name: aws\n    region: sa-east-1\n\nplugins:\n    - ./vendor/bref/bref\n\nfunctions:\n    fe:\n        handler: index.php\n        description: ''\n        runtime: php-83-fpm\n\n# Exclude files from deployment\npackage:\n    patterns:\n        - '!tests/**'\n"}]}