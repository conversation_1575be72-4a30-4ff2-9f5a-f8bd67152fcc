{"sourceFile": "services/lambda/arca/SIMPLIFICACION.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750457732431, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750457732431, "name": "Commit-0", "content": "# Simplificación de AFIP SDK Lambda\n\n## Resumen de Cambios\n\n### ErrorHandler.php - Simplificado\n**Antes**: 338 líneas con múltiples métodos específicos\n**Después**: 150 líneas con un método único\n\n#### Cambios principales:\n- ✅ **Un solo método `error()`** en lugar de múltiples métodos específicos\n- ✅ **Contexto dinámico** - se pasa toda la información en un array\n- ✅ **Código más limpio** y reutilizable para otros Lambdas\n- ❌ **Eliminados métodos específicos**: `errorBaseDatos()`, `errorS3()`, `errorAfip()`, `logError()`\n\n#### Uso anterior:\n```php\n$errorHandler->errorAfip(\"Error en AFIP\", $idempresa, $idventa, $exception);\n$errorHandler->errorBaseDatos(\"Error en BD\", $idempresa, $exception);\n```\n\n#### Uso nuevo (simplificado):\n```php\n$errorHandler->error(\"Error en AFIP\", [\n    'idempresa' => $idempresa,\n    'idventa' => $idventa, \n    'funcion' => 'AFIPSDK',\n    'exception' => $exception\n]);\n\n$errorHandler->error(\"Error en BD\", [\n    'idempresa' => $idempresa,\n    'funcion' => 'Base de Datos',\n    'exception' => $exception\n]);\n```\n\n### afipsdk.php - Drasticamente Simplificado\n**Antes**: 1,229 líneas con validaciones complejas\n**Después**: 270 líneas (78% de reducción)\n\n#### Métodos eliminados:\n- ❌ `validateCriticalEnvironmentVariables()` - Las variables faltan → Exception automática\n- ❌ `validarMensaje()` - Validación simple con regex\n- ❌ `obtenerDatosVenta()` - Simplificado a `obtenerVenta()`\n- ❌ `prepararDatosComprobante()` - Simplificado a `prepararComprobante()`\n- ❌ `completarCuit()` - Lógica compleja eliminada\n- ❌ `obtenerIvasVenta()` - No implementado en versión simple\n- ❌ `obtenerBasesImponibles()` - No implementado en versión simple\n- ❌ `obtenerTributosVenta()` - No implementado en versión simple\n- ❌ `obtenerComprobantesAsociados()` - No implementado en versión simple\n- ❌ `procesarRespuestaAfip()` - Simplificado dentro de `enviarFacturaAfip()`\n- ❌ `actualizarEstadoVenta()` - Simplificado a `actualizarVentaExitosa()`\n- ❌ `isProductionEnvironment()`, `isProduction()` - Lógica directa\n- ❌ `limpiarCertificadosTemporales()` - Dentro de `finally`\n- ❌ `marcarVentaRechazada()` - Manejo por Exception\n\n#### Métodos simplificados:\n- ✅ `handleSqs()` - Manejo básico con try/catch\n- ✅ `procesarMensaje()` - Flujo lineal simple\n- ✅ `obtenerVenta()` - Query directo\n- ✅ `obtenerCuitEmpresa()` - Query directo\n- ✅ `descargarCertificados()` - Solo .crt y .key\n- ✅ `enviarFacturaAfip()` - AFIPSDK básico\n- ✅ `prepararComprobante()` - Campos básicos\n- ✅ `actualizarVentaExitosa()` - Update simple\n\n#### Filosofía de simplificación:\n1. **Fallar rápido**: Si algo falla → Exception inmediata\n2. **Sin validaciones complejas**: Las variables de entorno faltan → PHP falla solo\n3. **Manejo básico de AFIP**: Solo casos comunes, sin edge cases\n4. **Logs simples**: echo básico + ErrorHandler para errores\n5. **Estructura lineal**: Flujo secuencial sin ramificaciones complejas\n\n## Beneficios\n\n### ✅ Mantenimiento\n- **78% menos código** para mantener\n- **Lógica más clara** y fácil de seguir\n- **Menos bugs** por menor complejidad\n\n### ✅ Performance\n- **Menos memory footprint**\n- **Carga más rápida**\n- **Ejecución más directa**\n\n### ✅ Debugging\n- **Stack traces más simples**\n- **Logs más claros**\n- **Flujo más predecible**\n\n### ✅ Reutilización\n- **ErrorHandler reutilizable** para otros Lambdas\n- **Arquitectura más modular**\n\n## Qué se perdió (intencionalmente)\n\n### ❌ Validaciones complejas\n- Validación exhaustiva de variables de entorno\n- Validación compleja de documentos CUIT/DNI\n- Validación de IVAs y tributos\n\n### ❌ Manejo de casos edge\n- Comprobantes asociados (notas de crédito/débito)\n- IVAs complejos con múltiples alícuotas\n- Tributos especiales\n- Diferentes tipos de concepto\n\n### ❌ Logs verbosos\n- Logging paso a paso detallado\n- Contexto exhaustivo en cada paso\n\n## Estrategia de implementación\n\n1. **Fase 1**: Usar versión simple para casos básicos (80% de las facturas)\n2. **Fase 2**: Agregar funcionalidades según necesidad real\n3. **Principio**: \"Agregar complejidad solo cuando se necesite\"\n\n## Cómo usar\n\n### Desarrollo local:\n```bash\nphp afipsdk.php 161 3567\n```\n\n### AWS Lambda:\n- Mensaje SQS: `161|3567`\n- Variables de entorno configuradas\n- Funciona igual que antes pero más simple\n\n## Backup\n\nEl archivo original está guardado como `afipsdk_backup.php` por si necesitas recuperar alguna funcionalidad específica.\n"}]}