{"sourceFile": "services/lambda/arca/test.php", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1750701474867, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750701499413, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,12 @@\n // Si se proporcionaron argumentos, usarlos\n if ($argc === 3) {\n     $idempresa = $argv[1];\n     $idventa = $argv[2];\n+} else {\n+    // Valores predeterminados\n+    $idempresa = 161;\n+    $idventa = 3567;\n }\n \n // Crear una instancia del handler\n $handler = new AfipSdk();\n"}, {"date": 1750703172611, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,17 +2,15 @@\n // Incluir el handler\n require __DIR__ . '/public/afipsdk.php';\n \n // Si se proporcionaron argumentos, usarlos\n-if ($argc === 3) {\n-    $idempresa = $argv[1];\n-    $idventa = $argv[2];\n-} else {\n-    // Valores predeterminados\n-    $idempresa = 161;\n-    $idventa = 3567;\n-}\n+if ($argc !== 3) {\n+    echo \"Uso: php test.php <idempresa> <idventa>\\n\";\n+    exit(1);\n \n+$idempresa = $argv[1];\n+$idventa = $argv[2];\n+\n // Crear una instancia del handler\n $handler = new AfipSdk();\n \n // Procesar el mensaje\n"}, {"date": 1750703177786, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,9 @@\n // Si se proporcionaron argumentos, usarlos\n if ($argc !== 3) {\n     echo \"Uso: php test.php <idempresa> <idventa>\\n\";\n     exit(1);\n+}\n \n $idempresa = $argv[1];\n $idventa = $argv[2];\n \n"}], "date": 1750701474867, "name": "Commit-0", "content": "<?php\n// Incluir el handler\nrequire __DIR__ . '/public/afipsdk.php';\n\n// Si se proporcionaron argumentos, usarlos\nif ($argc === 3) {\n    $idempresa = $argv[1];\n    $idventa = $argv[2];\n}\n\n// Crear una instancia del handler\n$handler = new AfipSdk();\n\n// Procesar el mensaje\necho \"Ejecutando prueba con idempresa=$idempresa, idventa=$idventa\\n\";\n$handler->procesarMensaje(\"$idempresa|$idventa\");"}]}