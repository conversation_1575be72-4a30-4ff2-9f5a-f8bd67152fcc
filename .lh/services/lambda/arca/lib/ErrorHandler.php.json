{"sourceFile": "services/lambda/arca/lib/ErrorHandler.php", "activeCommit": 0, "commits": [{"activePatchIndex": 7, "patches": [{"date": 1750286265139, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750363808649, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,9 +11,9 @@\n     private SqsClient $sqsClient;\n     private string $queueUrl;\n     private string $stage;\n     private bool $isDevelopment;\n-    \n+\n     // Constantes para configuración\n     const EMAIL_QUEUE_URL = 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue';\n     const MAIL_SERVIDOR = '<EMAIL>';\n     const MAIL_DESARROLLO = '<EMAIL>';\n@@ -23,9 +23,9 @@\n     {\n         $this->stage = $_ENV['APP_ENV'] ?? 'dev';\n         $this->isDevelopment = $this->stage === 'dev' || $this->stage === 'desarrollo';\n         $this->queueUrl = self::EMAIL_QUEUE_URL;\n-        \n+\n         // Inicializar cliente SQS\n         $this->sqsClient = new SqsClient([\n             'version' => 'latest',\n             'region'  => $_ENV['AWS_REGION'] ?? 'sa-east-1',\n@@ -37,17 +37,17 @@\n     }\n \n     /**\n      * Función principal para manejar errores\n-     * \n+     *\n      * @param string $mensaje Mensaje de error\n      * @param bool $continuar Si debe continuar la ejecución después del error\n      * @param array $contexto Contexto adicional del error (idempresa, idventa, etc.)\n      */\n     public function mostrarError(string $mensaje, bool $continuar = false, array $contexto = []): void\n     {\n         $textoCompleto = $this->construirMensajeError($mensaje, $contexto);\n-        \n+\n         if ($this->isDevelopment) {\n             // En desarrollo, mostrar el error directamente\n             echo \"\\n\\nERROR: \" . $textoCompleto . \"\\n\\n\";\n             if (!$continuar) {\n@@ -67,42 +67,37 @@\n      */\n     private function construirMensajeError(string $mensaje, array $contexto = []): string\n     {\n         $texto = 'ERROR: ' . $mensaje . '<br /><br />';\n-        \n+\n         // Información del entorno\n         $texto .= 'stage: ' . $this->stage . '<br />';\n         $texto .= 'función: Lambda AFIP SDK<br />';\n         $texto .= 'fecha: ' . date('Y-m-d H:i:s') . '<br />';\n-        \n+\n         // Información de contexto si se proporciona\n         if (isset($contexto['idempresa'])) {\n             $texto .= 'idempresa: ' . $contexto['idempresa'] . '<br />';\n         }\n-        \n+\n         if (isset($contexto['idventa'])) {\n             $texto .= 'idventa: ' . $contexto['idventa'] . '<br />';\n         }\n-        \n+\n         if (isset($contexto['cuit'])) {\n             $texto .= 'cuit: ' . $contexto['cuit'] . '<br />';\n         }\n-        \n+\n         if (isset($contexto['funcion'])) {\n             $texto .= 'función: ' . $contexto['funcion'] . '<br />';\n         }\n-        \n-        // Variables de entorno relevantes\n-        $texto .= '<br />Variables de entorno:<br />';\n-        $texto .= 'AWS_REGION: ' . ($_ENV['AWS_REGION'] ?? 'no definida') . '<br />';\n-        $texto .= 'APP_ENV: ' . ($_ENV['APP_ENV'] ?? 'no definida') . '<br />';\n-        \n+\n         // Stack trace si hay una excepción en el contexto\n         if (isset($contexto['exception']) && $contexto['exception'] instanceof Exception) {\n             $texto .= '<br />Stack trace:<br />';\n             $texto .= nl2br($contexto['exception']->getTraceAsString()) . '<br />';\n         }\n-        \n+\n         return $texto;\n     }\n \n     /**\n@@ -114,9 +109,9 @@\n             // Verificar que el mensaje no sea demasiado largo\n             if (strlen($mensaje) > 260000) { // 260KB limit\n                 $mensaje = substr($mensaje, 0, 260000) . '<br /><br />... (mensaje truncado por tamaño)';\n             }\n-            \n+\n             $messageAttributes = [\n                 \"Subject\" => [\n                     'DataType' => \"String\",\n                     'StringValue' => \"ERROR en Lambda AFIP SDK - Stage: {$this->stage}\"\n@@ -142,11 +137,11 @@\n                 'MessageAttributes' => $messageAttributes,\n             ];\n \n             $this->sqsClient->sendMessage($params);\n-            \n+\n             return true;\n-            \n+\n         } catch (Exception $e) {\n             // Si falla el envío del email, al menos loguear el error original\n             error_log(\"Error al enviar email de error: \" . $e->getMessage());\n             error_log(\"Mensaje original del error: \" . strip_tags($mensaje));\n@@ -159,17 +154,17 @@\n      */\n     public function errorBaseDatos(string $mensaje, int $idempresa = null, Exception $exception = null): void\n     {\n         $contexto = ['funcion' => 'Base de Datos'];\n-        \n+\n         if ($idempresa !== null) {\n             $contexto['idempresa'] = $idempresa;\n         }\n-        \n+\n         if ($exception !== null) {\n             $contexto['exception'] = $exception;\n         }\n-        \n+\n         $this->mostrarError($mensaje, false, $contexto);\n     }\n \n     /**\n@@ -177,17 +172,17 @@\n      */\n     public function errorS3(string $mensaje, string $cuit = null, Exception $exception = null): void\n     {\n         $contexto = ['funcion' => 'AWS S3'];\n-        \n+\n         if ($cuit !== null) {\n             $contexto['cuit'] = $cuit;\n         }\n-        \n+\n         if ($exception !== null) {\n             $contexto['exception'] = $exception;\n         }\n-        \n+\n         $this->mostrarError($mensaje, false, $contexto);\n     }\n \n     /**\n@@ -195,21 +190,21 @@\n      */\n     public function errorAfip(string $mensaje, int $idempresa = null, int $idventa = null, Exception $exception = null): void\n     {\n         $contexto = ['funcion' => 'AFIPSDK'];\n-        \n+\n         if ($idempresa !== null) {\n             $contexto['idempresa'] = $idempresa;\n         }\n-        \n+\n         if ($idventa !== null) {\n             $contexto['idventa'] = $idventa;\n         }\n-        \n+\n         if ($exception !== null) {\n             $contexto['exception'] = $exception;\n         }\n-        \n+\n         $this->mostrarError($mensaje, false, $contexto);\n     }\n \n     /**\n"}, {"date": 1750445359303, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,27 +12,32 @@\n     private string $queueUrl;\n     private string $stage;\n     private bool $isDevelopment;\n \n-    // Constantes para configuración\n-    const EMAIL_QUEUE_URL = 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue';\n-    const MAIL_SERVIDOR = '<EMAIL>';\n-    const MAIL_DESARROLLO = '<EMAIL>';\n-    const URL_HOST = 'Lambda AFIP SDK';\n+    // Constantes para configuración - ahora obtenidas de variables de entorno\n+    private string $emailQueueUrl;\n+    private string $mailServidor;\n+    private string $mailDesarrollo;\n \n     private function __construct()\n     {\n         $this->stage = $_ENV['APP_ENV'] ?? 'dev';\n         $this->isDevelopment = $this->stage === 'dev' || $this->stage === 'desarrollo';\n-        $this->queueUrl = self::EMAIL_QUEUE_URL;\n+        \n+        // Obtener configuración de variables de entorno\n+        $this->emailQueueUrl = $_ENV['AWS_SQS_EMAIL_QUEUE'] ?? 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue';\n+        $this->mailServidor = $_ENV['MAIL_SERVIDOR'] ?? '<EMAIL>';\n+        $this->mailDesarrollo = $_ENV['MAIL_DESARROLLO'] ?? '<EMAIL>';\n+        \n+        $this->queueUrl = $this->emailQueueUrl;\n \n         // Inicializar cliente SQS\n         $this->sqsClient = new SqsClient([\n             'version' => 'latest',\n             'region'  => $_ENV['AWS_REGION'] ?? 'sa-east-1',\n             'credentials' => [\n-                'key'    => $_ENV['AWS_SQS_QUEUER_KEY'] ?? '********************',\n-                'secret' => $_ENV['AWS_SQS_QUEUER_SECRET'] ?? 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO',\n+                'key'    => $_ENV['AWS_SQS_QUEUER_KEY'] ?? '',\n+                'secret' => $_ENV['AWS_SQS_QUEUER_SECRET'] ?? '',\n             ],\n         ]);\n     }\n \n@@ -117,13 +122,13 @@\n                     'StringValue' => \"ERROR en Lambda AFIP SDK - Stage: {$this->stage}\"\n                 ],\n                 \"To\" => [\n                     'DataType' => \"String\",\n-                    'StringValue' => self::MAIL_DESARROLLO\n+                    'StringValue' => $this->mailDesarrollo\n                 ],\n                 \"From\" => [\n                     'DataType' => \"String\",\n-                    'StringValue' => self::MAIL_SERVIDOR\n+                    'StringValue' => $this->mailServidor\n                 ],\n                 \"FromName\" => [\n                     'DataType' => \"String\",\n                     'StringValue' => 'Lambda AFIP SDK Error Handler'\n@@ -132,9 +137,9 @@\n \n             $params = [\n                 'QueueUrl' => $this->queueUrl,\n                 'DelaySeconds' => 0, // Enviar inmediatamente para errores\n-                'MessageBody' => $mensaje . '<br><br><hr>Enviado desde ' . self::URL_HOST,\n+                'MessageBody' => $mensaje . '<br><br><hr>Enviado desde Lambda AFIP SDK',\n                 'MessageAttributes' => $messageAttributes,\n             ];\n \n             $this->sqsClient->sendMessage($params);\n"}, {"date": 1750457732482, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,16 +8,13 @@\n class ErrorHandler\n {\n     private static $instance = null;\n     private SqsClient $sqsClient;\n-    private string $queueUrl;\n-    private string $stage;\n-    private bool $isDevelopment;\n-\n-    // Constantes para configuración - ahora obtenidas de variables de entorno\n     private string $emailQueueUrl;\n     private string $mailServidor;\n     private string $mailDesarrollo;\n+    private string $stage;\n+    private bool $isDevelopment;\n \n     private function __construct()\n     {\n         $this->stage = $_ENV['APP_ENV'] ?? 'dev';\n@@ -26,10 +23,8 @@\n         // Obtener configuración de variables de entorno\n         $this->emailQueueUrl = $_ENV['AWS_SQS_EMAIL_QUEUE'] ?? 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue';\n         $this->mailServidor = $_ENV['MAIL_SERVIDOR'] ?? '<EMAIL>';\n         $this->mailDesarrollo = $_ENV['MAIL_DESARROLLO'] ?? '<EMAIL>';\n-        \n-        $this->queueUrl = $this->emailQueueUrl;\n \n         // Inicializar cliente SQS\n         $this->sqsClient = new SqsClient([\n             'version' => 'latest',\n@@ -41,68 +36,49 @@\n         ]);\n     }\n \n     /**\n-     * Función principal para manejar errores\n-     *\n+     * Método principal para manejar todos los errores\n+     * \n      * @param string $mensaje Mensaje de error\n+     * @param array $contexto Contexto adicional (idempresa, idventa, función, exception, etc.)\n      * @param bool $continuar Si debe continuar la ejecución después del error\n-     * @param array $contexto Contexto adicional del error (idempresa, idventa, etc.)\n      */\n-    public function mostrarError(string $mensaje, bool $continuar = false, array $contexto = []): void\n+    public function error(string $mensaje, array $contexto = [], bool $continuar = false): void\n     {\n         $textoCompleto = $this->construirMensajeError($mensaje, $contexto);\n \n         if ($this->isDevelopment) {\n-            // En desarrollo, mostrar el error directamente\n-            echo \"\\n\\nERROR: \" . $textoCompleto . \"\\n\\n\";\n-            if (!$continuar) {\n-                exit(1);\n-            }\n+            echo \"\\n\\nERROR: \" . strip_tags($textoCompleto) . \"\\n\\n\";\n         } else {\n-            // En producción, enviar por email\n             $this->enviarEmailError($textoCompleto);\n-            if (!$continuar) {\n-                exit(1);\n-            }\n         }\n+\n+        if (!$continuar) {\n+            exit(1);\n+        }\n     }\n \n     /**\n      * Construye el mensaje de error con toda la información de contexto\n      */\n     private function construirMensajeError(string $mensaje, array $contexto = []): string\n     {\n         $texto = 'ERROR: ' . $mensaje . '<br /><br />';\n-\n-        // Información del entorno\n         $texto .= 'stage: ' . $this->stage . '<br />';\n-        $texto .= 'función: Lambda AFIP SDK<br />';\n+        $texto .= 'función: ' . ($contexto['funcion'] ?? 'Lambda AFIP SDK') . '<br />';\n         $texto .= 'fecha: ' . date('Y-m-d H:i:s') . '<br />';\n \n-        // Información de contexto si se proporciona\n-        if (isset($contexto['idempresa'])) {\n-            $texto .= 'idempresa: ' . $contexto['idempresa'] . '<br />';\n+        // Agregar contexto dinámico\n+        foreach ($contexto as $clave => $valor) {\n+            if ($clave === 'exception' && $valor instanceof Exception) {\n+                $texto .= '<br />Exception: ' . $valor->getMessage() . '<br />';\n+                $texto .= 'Stack trace:<br />' . nl2br($valor->getTraceAsString()) . '<br />';\n+            } elseif ($clave !== 'funcion') {\n+                $texto .= $clave . ': ' . $valor . '<br />';\n+            }\n         }\n \n-        if (isset($contexto['idventa'])) {\n-            $texto .= 'idventa: ' . $contexto['idventa'] . '<br />';\n-        }\n-\n-        if (isset($contexto['cuit'])) {\n-            $texto .= 'cuit: ' . $contexto['cuit'] . '<br />';\n-        }\n-\n-        if (isset($contexto['funcion'])) {\n-            $texto .= 'función: ' . $contexto['funcion'] . '<br />';\n-        }\n-\n-        // Stack trace si hay una excepción en el contexto\n-        if (isset($contexto['exception']) && $contexto['exception'] instanceof Exception) {\n-            $texto .= '<br />Stack trace:<br />';\n-            $texto .= nl2br($contexto['exception']->getTraceAsString()) . '<br />';\n-        }\n-\n         return $texto;\n     }\n \n     /**\n@@ -110,118 +86,47 @@\n      */\n     private function enviarEmailError(string $mensaje): bool\n     {\n         try {\n-            // Verificar que el mensaje no sea demasiado largo\n-            if (strlen($mensaje) > 260000) { // 260KB limit\n-                $mensaje = substr($mensaje, 0, 260000) . '<br /><br />... (mensaje truncado por tamaño)';\n+            if (strlen($mensaje) > 260000) {\n+                $mensaje = substr($mensaje, 0, 260000) . '<br />... (mensaje truncado)';\n             }\n \n-            $messageAttributes = [\n-                \"Subject\" => [\n-                    'DataType' => \"String\",\n-                    'StringValue' => \"ERROR en Lambda AFIP SDK - Stage: {$this->stage}\"\n-                ],\n-                \"To\" => [\n-                    'DataType' => \"String\",\n-                    'StringValue' => $this->mailDesarrollo\n-                ],\n-                \"From\" => [\n-                    'DataType' => \"String\",\n-                    'StringValue' => $this->mailServidor\n-                ],\n-                \"FromName\" => [\n-                    'DataType' => \"String\",\n-                    'StringValue' => 'Lambda AFIP SDK Error Handler'\n-                ],\n-            ];\n-\n             $params = [\n-                'QueueUrl' => $this->queueUrl,\n-                'DelaySeconds' => 0, // Enviar inmediatamente para errores\n+                'QueueUrl' => $this->emailQueueUrl,\n+                'DelaySeconds' => 0,\n                 'MessageBody' => $mensaje . '<br><br><hr>Enviado desde Lambda AFIP SDK',\n-                'MessageAttributes' => $messageAttributes,\n+                'MessageAttributes' => [\n+                    \"Subject\" => [\n+                        'DataType' => \"String\",\n+                        'StringValue' => \"ERROR en Lambda AFIP SDK - Stage: {$this->stage}\"\n+                    ],\n+                    \"To\" => [\n+                        'DataType' => \"String\",\n+                        'StringValue' => $this->mailDesarrollo\n+                    ],\n+                    \"From\" => [\n+                        'DataType' => \"String\",\n+                        'StringValue' => $this->mailServidor\n+                    ],\n+                    \"FromName\" => [\n+                        'DataType' => \"String\",\n+                        'StringValue' => 'Lambda AFIP SDK Error Handler'\n+                    ],\n+                ],\n             ];\n \n             $this->sqsClient->sendMessage($params);\n-\n             return true;\n \n         } catch (Exception $e) {\n-            // Si falla el envío del email, al menos loguear el error original\n-            error_log(\"Error al enviar email de error: \" . $e->getMessage());\n-            error_log(\"Mensaje original del error: \" . strip_tags($mensaje));\n+            error_log(\"Error al enviar email: \" . $e->getMessage());\n+            error_log(\"Mensaje original: \" . strip_tags($mensaje));\n             return false;\n         }\n     }\n \n     /**\n-     * Método de conveniencia para errores de base de datos\n-     */\n-    public function errorBaseDatos(string $mensaje, int $idempresa = null, Exception $exception = null): void\n-    {\n-        $contexto = ['funcion' => 'Base de Datos'];\n-\n-        if ($idempresa !== null) {\n-            $contexto['idempresa'] = $idempresa;\n-        }\n-\n-        if ($exception !== null) {\n-            $contexto['exception'] = $exception;\n-        }\n-\n-        $this->mostrarError($mensaje, false, $contexto);\n-    }\n-\n-    /**\n-     * Método de conveniencia para errores de S3\n-     */\n-    public function errorS3(string $mensaje, string $cuit = null, Exception $exception = null): void\n-    {\n-        $contexto = ['funcion' => 'AWS S3'];\n-\n-        if ($cuit !== null) {\n-            $contexto['cuit'] = $cuit;\n-        }\n-\n-        if ($exception !== null) {\n-            $contexto['exception'] = $exception;\n-        }\n-\n-        $this->mostrarError($mensaje, false, $contexto);\n-    }\n-\n-    /**\n-     * Método de conveniencia para errores de AFIP\n-     */\n-    public function errorAfip(string $mensaje, int $idempresa = null, int $idventa = null, Exception $exception = null): void\n-    {\n-        $contexto = ['funcion' => 'AFIPSDK'];\n-\n-        if ($idempresa !== null) {\n-            $contexto['idempresa'] = $idempresa;\n-        }\n-\n-        if ($idventa !== null) {\n-            $contexto['idventa'] = $idventa;\n-        }\n-\n-        if ($exception !== null) {\n-            $contexto['exception'] = $exception;\n-        }\n-\n-        $this->mostrarError($mensaje, false, $contexto);\n-    }\n-\n-    /**\n-     * Método de conveniencia para errores generales que no deben detener la ejecución\n-     */\n-    public function logError(string $mensaje, array $contexto = []): void\n-    {\n-        $this->mostrarError($mensaje, true, $contexto);\n-    }\n-\n-    /**\n      * Obtener la instancia singleton\n      */\n     public static function getInstance(): ErrorHandler\n     {\n"}, {"date": 1750460644926, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,8 +16,9 @@\n     private bool $isDevelopment;\n \n     private function __construct()\n     {\n+        // El stage se pasa automáticamente desde serverless.yml como APP_ENV\n         $this->stage = $_ENV['APP_ENV'] ?? 'dev';\n         $this->isDevelopment = $this->stage === 'dev' || $this->stage === 'desarrollo';\n         \n         // Obtener configuración de variables de entorno\n"}, {"date": 1750702208913, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,9 +19,9 @@\n     {\n         // El stage se pasa automáticamente desde serverless.yml como APP_ENV\n         $this->stage = $_ENV['APP_ENV'] ?? 'dev';\n         $this->isDevelopment = $this->stage === 'dev' || $this->stage === 'desarrollo';\n-        \n+\n         // Obtener configuración de variables de entorno\n         $this->emailQueueUrl = $_ENV['AWS_SQS_EMAIL_QUEUE'] ?? 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue';\n         $this->mailServidor = $_ENV['MAIL_SERVIDOR'] ?? '<EMAIL>';\n         $this->mailDesarrollo = $_ENV['MAIL_DESARROLLO'] ?? '<EMAIL>';\n@@ -38,9 +38,9 @@\n     }\n \n     /**\n      * Método principal para manejar todos los errores\n-     * \n+     *\n      * @param string $mensaje Mensaje de error\n      * @param array $contexto Contexto adicional (idempresa, idventa, función, exception, etc.)\n      * @param bool $continuar Si debe continuar la ejecución después del error\n      */\n@@ -65,9 +65,9 @@\n     private function construirMensajeError(string $mensaje, array $contexto = []): string\n     {\n         $texto = 'ERROR: ' . $mensaje . '<br /><br />';\n         $texto .= 'stage: ' . $this->stage . '<br />';\n-        $texto .= 'función: ' . ($contexto['funcion'] ?? 'Lambda AFIP SDK') . '<br />';\n+        $texto .= 'función: ' . ($contexto['funcion'] ?? 'Lambda Function') . '<br />';\n         $texto .= 'fecha: ' . date('Y-m-d H:i:s') . '<br />';\n \n         // Agregar contexto dinámico\n         foreach ($contexto as $clave => $valor) {\n@@ -94,13 +94,13 @@\n \n             $params = [\n                 'QueueUrl' => $this->emailQueueUrl,\n                 'DelaySeconds' => 0,\n-                'MessageBody' => $mensaje . '<br><br><hr>Enviado desde Lambda AFIP SDK',\n+                'MessageBody' => $mensaje . '<br><br><hr>Enviado desde Lambda Function',\n                 'MessageAttributes' => [\n                     \"Subject\" => [\n                         'DataType' => \"String\",\n-                        'StringValue' => \"ERROR en Lambda AFIP SDK - Stage: {$this->stage}\"\n+                        'StringValue' => \"ERROR en Lambda Function - Stage: {$this->stage}\"\n                     ],\n                     \"To\" => [\n                         'DataType' => \"String\",\n                         'StringValue' => $this->mailDesarrollo\n@@ -110,9 +110,9 @@\n                         'StringValue' => $this->mailServidor\n                     ],\n                     \"FromName\" => [\n                         'DataType' => \"String\",\n-                        'StringValue' => 'Lambda AFIP SDK Error Handler'\n+                        'StringValue' => 'Lambda Function Error Handler'\n                     ],\n                 ],\n             ];\n \n"}, {"date": 1750711841749, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,9 +48,9 @@\n     {\n         $textoCompleto = $this->construirMensajeError($mensaje, $contexto);\n \n         if ($this->isDevelopment) {\n-            echo \"\\n\\nERROR: \" . strip_tags($textoCompleto) . \"\\n\\n\";\n+            echo \"\\n\\nDEV ERROR: \" . strip_tags($textoCompleto) . \"\\n\\n\";\n         } else {\n             $this->enviarEmailError($textoCompleto);\n         }\n \n"}, {"date": 1750715525213, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,9 +48,9 @@\n     {\n         $textoCompleto = $this->construirMensajeError($mensaje, $contexto);\n \n         if ($this->isDevelopment) {\n-            echo \"\\n\\nDEV ERROR: \" . strip_tags($textoCompleto) . \"\\n\\n\";\n+            echo \"\\n\\nERROR: \" . strip_tags($textoCompleto) . \"\\n\\n\";\n         } else {\n             $this->enviarEmailError($textoCompleto);\n         }\n \n"}], "date": 1750286265139, "name": "Commit-0", "content": "<?php\n\nnamespace FuncionesComunes\\ErrorHandler;\n\nuse Aws\\Sqs\\SqsClient;\nuse Exception;\n\nclass ErrorHandler\n{\n    private static $instance = null;\n    private SqsClient $sqsClient;\n    private string $queueUrl;\n    private string $stage;\n    private bool $isDevelopment;\n    \n    // Constantes para configuración\n    const EMAIL_QUEUE_URL = 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue';\n    const MAIL_SERVIDOR = '<EMAIL>';\n    const MAIL_DESARROLLO = '<EMAIL>';\n    const URL_HOST = 'Lambda AFIP SDK';\n\n    private function __construct()\n    {\n        $this->stage = $_ENV['APP_ENV'] ?? 'dev';\n        $this->isDevelopment = $this->stage === 'dev' || $this->stage === 'desarrollo';\n        $this->queueUrl = self::EMAIL_QUEUE_URL;\n        \n        // Inicializar cliente SQS\n        $this->sqsClient = new SqsClient([\n            'version' => 'latest',\n            'region'  => $_ENV['AWS_REGION'] ?? 'sa-east-1',\n            'credentials' => [\n                'key'    => $_ENV['AWS_SQS_QUEUER_KEY'] ?? '********************',\n                'secret' => $_ENV['AWS_SQS_QUEUER_SECRET'] ?? 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO',\n            ],\n        ]);\n    }\n\n    /**\n     * Función principal para manejar errores\n     * \n     * @param string $mensaje Mensaje de error\n     * @param bool $continuar Si debe continuar la ejecución después del error\n     * @param array $contexto Contexto adicional del error (idempresa, idventa, etc.)\n     */\n    public function mostrarError(string $mensaje, bool $continuar = false, array $contexto = []): void\n    {\n        $textoCompleto = $this->construirMensajeError($mensaje, $contexto);\n        \n        if ($this->isDevelopment) {\n            // En desarrollo, mostrar el error directamente\n            echo \"\\n\\nERROR: \" . $textoCompleto . \"\\n\\n\";\n            if (!$continuar) {\n                exit(1);\n            }\n        } else {\n            // En producción, enviar por email\n            $this->enviarEmailError($textoCompleto);\n            if (!$continuar) {\n                exit(1);\n            }\n        }\n    }\n\n    /**\n     * Construye el mensaje de error con toda la información de contexto\n     */\n    private function construirMensajeError(string $mensaje, array $contexto = []): string\n    {\n        $texto = 'ERROR: ' . $mensaje . '<br /><br />';\n        \n        // Información del entorno\n        $texto .= 'stage: ' . $this->stage . '<br />';\n        $texto .= 'función: Lambda AFIP SDK<br />';\n        $texto .= 'fecha: ' . date('Y-m-d H:i:s') . '<br />';\n        \n        // Información de contexto si se proporciona\n        if (isset($contexto['idempresa'])) {\n            $texto .= 'idempresa: ' . $contexto['idempresa'] . '<br />';\n        }\n        \n        if (isset($contexto['idventa'])) {\n            $texto .= 'idventa: ' . $contexto['idventa'] . '<br />';\n        }\n        \n        if (isset($contexto['cuit'])) {\n            $texto .= 'cuit: ' . $contexto['cuit'] . '<br />';\n        }\n        \n        if (isset($contexto['funcion'])) {\n            $texto .= 'función: ' . $contexto['funcion'] . '<br />';\n        }\n        \n        // Variables de entorno relevantes\n        $texto .= '<br />Variables de entorno:<br />';\n        $texto .= 'AWS_REGION: ' . ($_ENV['AWS_REGION'] ?? 'no definida') . '<br />';\n        $texto .= 'APP_ENV: ' . ($_ENV['APP_ENV'] ?? 'no definida') . '<br />';\n        \n        // Stack trace si hay una excepción en el contexto\n        if (isset($contexto['exception']) && $contexto['exception'] instanceof Exception) {\n            $texto .= '<br />Stack trace:<br />';\n            $texto .= nl2br($contexto['exception']->getTraceAsString()) . '<br />';\n        }\n        \n        return $texto;\n    }\n\n    /**\n     * Envía el email de error a través de SQS\n     */\n    private function enviarEmailError(string $mensaje): bool\n    {\n        try {\n            // Verificar que el mensaje no sea demasiado largo\n            if (strlen($mensaje) > 260000) { // 260KB limit\n                $mensaje = substr($mensaje, 0, 260000) . '<br /><br />... (mensaje truncado por tamaño)';\n            }\n            \n            $messageAttributes = [\n                \"Subject\" => [\n                    'DataType' => \"String\",\n                    'StringValue' => \"ERROR en Lambda AFIP SDK - Stage: {$this->stage}\"\n                ],\n                \"To\" => [\n                    'DataType' => \"String\",\n                    'StringValue' => self::MAIL_DESARROLLO\n                ],\n                \"From\" => [\n                    'DataType' => \"String\",\n                    'StringValue' => self::MAIL_SERVIDOR\n                ],\n                \"FromName\" => [\n                    'DataType' => \"String\",\n                    'StringValue' => 'Lambda AFIP SDK Error Handler'\n                ],\n            ];\n\n            $params = [\n                'QueueUrl' => $this->queueUrl,\n                'DelaySeconds' => 0, // Enviar inmediatamente para errores\n                'MessageBody' => $mensaje . '<br><br><hr>Enviado desde ' . self::URL_HOST,\n                'MessageAttributes' => $messageAttributes,\n            ];\n\n            $this->sqsClient->sendMessage($params);\n            \n            return true;\n            \n        } catch (Exception $e) {\n            // Si falla el envío del email, al menos loguear el error original\n            error_log(\"Error al enviar email de error: \" . $e->getMessage());\n            error_log(\"Mensaje original del error: \" . strip_tags($mensaje));\n            return false;\n        }\n    }\n\n    /**\n     * Método de conveniencia para errores de base de datos\n     */\n    public function errorBaseDatos(string $mensaje, int $idempresa = null, Exception $exception = null): void\n    {\n        $contexto = ['funcion' => 'Base de Datos'];\n        \n        if ($idempresa !== null) {\n            $contexto['idempresa'] = $idempresa;\n        }\n        \n        if ($exception !== null) {\n            $contexto['exception'] = $exception;\n        }\n        \n        $this->mostrarError($mensaje, false, $contexto);\n    }\n\n    /**\n     * Método de conveniencia para errores de S3\n     */\n    public function errorS3(string $mensaje, string $cuit = null, Exception $exception = null): void\n    {\n        $contexto = ['funcion' => 'AWS S3'];\n        \n        if ($cuit !== null) {\n            $contexto['cuit'] = $cuit;\n        }\n        \n        if ($exception !== null) {\n            $contexto['exception'] = $exception;\n        }\n        \n        $this->mostrarError($mensaje, false, $contexto);\n    }\n\n    /**\n     * Método de conveniencia para errores de AFIP\n     */\n    public function errorAfip(string $mensaje, int $idempresa = null, int $idventa = null, Exception $exception = null): void\n    {\n        $contexto = ['funcion' => 'AFIPSDK'];\n        \n        if ($idempresa !== null) {\n            $contexto['idempresa'] = $idempresa;\n        }\n        \n        if ($idventa !== null) {\n            $contexto['idventa'] = $idventa;\n        }\n        \n        if ($exception !== null) {\n            $contexto['exception'] = $exception;\n        }\n        \n        $this->mostrarError($mensaje, false, $contexto);\n    }\n\n    /**\n     * Método de conveniencia para errores generales que no deben detener la ejecución\n     */\n    public function logError(string $mensaje, array $contexto = []): void\n    {\n        $this->mostrarError($mensaje, true, $contexto);\n    }\n\n    /**\n     * Obtener la instancia singleton\n     */\n    public static function getInstance(): ErrorHandler\n    {\n        if (self::$instance === null) {\n            self::$instance = new ErrorHandler();\n        }\n        return self::$instance;\n    }\n\n    /**\n     * Verificar si estamos en modo desarrollo\n     */\n    public function isDevelopment(): bool\n    {\n        return $this->isDevelopment;\n    }\n\n    /**\n     * Obtener el stage actual\n     */\n    public function getStage(): string\n    {\n        return $this->stage;\n    }\n}\n"}]}