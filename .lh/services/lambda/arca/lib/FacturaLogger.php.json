{"sourceFile": "services/lambda/arca/lib/FacturaLogger.php", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750517305269, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750517305269, "name": "Commit-0", "content": "<?php declare(strict_types=1);\n\nnamespace FuncionesComunes\\FacturaLogger;\n\nuse Aws\\CloudWatchLogs\\CloudWatchLogsClient;\n\nclass FacturaLogger\n{\n    private ?CloudWatchLogsClient $cloudWatchClient = null;\n    private string $stage;\n    private string $logGroupName = 'afip-facturas-log';\n    private string $logStreamName;\n    private bool $isDev;\n    private string $localLogFile;\n\n    public function __construct(string $stage)\n    {\n        $this->stage = $stage;\n        $this->isDev = $stage === 'dev';\n        $this->logStreamName = 'afip-facturas-' . date('Y-m-d');\n        $this->localLogFile = __DIR__ . '/../afip-facturas.log';\n\n        if (!$this->isDev) {\n            $this->cloudWatchClient = new CloudWatchLogsClient([\n                'version' => 'latest',\n                'region'  => $_ENV['AWS_REGION'] ?? 'sa-east-1',\n            ]);\n        }\n    }\n\n    /**\n     * Registra un intento de facturación\n     */\n    public function logFactura(array $datos): void\n    {\n        $logEntry = [\n            'timestamp' => date('Y-m-d H:i:s'),\n            'stage' => $this->stage,\n            'idempresa' => $datos['idempresa'] ?? null,\n            'idventa' => $datos['idventa'] ?? null,\n            'estado' => $datos['estado'] ?? 'ERROR',\n            'error' => $datos['error'] ?? null,\n            'idtipoventa' => $datos['idtipoventa'] ?? null,\n            'numero' => $datos['numero'] ?? null,\n            'fecha_venta' => $datos['fecha_venta'] ?? null,\n            'total' => $datos['total'] ?? null,\n            'cuit' => $datos['cuit'] ?? null,\n            'dni' => $datos['dni'] ?? null,\n            'cae' => $datos['cae'] ?? null,\n            'obscae' => $datos['obscae'] ?? null,\n        ];\n\n        $logMessage = json_encode($logEntry, JSON_UNESCAPED_UNICODE);\n\n        if ($this->isDev) {\n            $this->logToFile($logMessage);\n        } else {\n            $this->logToCloudWatch($logMessage);\n        }\n    }\n\n    /**\n     * Guarda el log en archivo local (desarrollo)\n     */\n    private function logToFile(string $message): void\n    {\n        $logLine = date('Y-m-d H:i:s') . \" | \" . $message . \"\\n\";\n        file_put_contents($this->localLogFile, $logLine, FILE_APPEND | LOCK_EX);\n    }\n\n    /**\n     * Guarda el log en CloudWatch (alfa, beta, prod)\n     */\n    private function logToCloudWatch(string $message): void\n    {\n        try {\n            // Verificar si el grupo de logs existe, si no, crearlo\n            $this->ensureLogGroupExists();\n\n            // Verificar si el stream de logs existe, si no, crearlo\n            $this->ensureLogStreamExists();\n\n            // Obtener el sequence token si existe\n            $sequenceToken = $this->getSequenceToken();\n\n            // Preparar el evento de log\n            $logEvent = [\n                'timestamp' => round(microtime(true) * 1000), // timestamp en milisegundos\n                'message' => $message,\n            ];\n\n            // Preparar los parámetros para putLogEvents\n            $params = [\n                'logGroupName' => $this->logGroupName,\n                'logStreamName' => $this->logStreamName,\n                'logEvents' => [$logEvent],\n            ];\n\n            if ($sequenceToken) {\n                $params['sequenceToken'] = $sequenceToken;\n            }\n\n            // Enviar el evento de log\n            $this->cloudWatchClient->putLogEvents($params);\n\n        } catch (\\Exception $e) {\n            // Si falla CloudWatch, al menos registrar en error_log\n            error_log(\"Error enviando log a CloudWatch: \" . $e->getMessage());\n            error_log(\"Mensaje original: \" . $message);\n        }\n    }\n\n    /**\n     * Asegura que el grupo de logs exista\n     */\n    private function ensureLogGroupExists(): void\n    {\n        try {\n            $this->cloudWatchClient->describeLogGroups([\n                'logGroupNamePrefix' => $this->logGroupName,\n            ]);\n        } catch (\\Exception $e) {\n            // Si no existe, crearlo\n            try {\n                $this->cloudWatchClient->createLogGroup([\n                    'logGroupName' => $this->logGroupName,\n                ]);\n            } catch (\\Exception $createException) {\n                // Podría fallar si ya existe, ignorar\n            }\n        }\n    }\n\n    /**\n     * Asegura que el stream de logs exista\n     */\n    private function ensureLogStreamExists(): void\n    {\n        try {\n            $this->cloudWatchClient->describeLogStreams([\n                'logGroupName' => $this->logGroupName,\n                'logStreamNamePrefix' => $this->logStreamName,\n            ]);\n        } catch (\\Exception $e) {\n            // Si no existe, crearlo\n            try {\n                $this->cloudWatchClient->createLogStream([\n                    'logGroupName' => $this->logGroupName,\n                    'logStreamName' => $this->logStreamName,\n                ]);\n            } catch (\\Exception $createException) {\n                // Podría fallar si ya existe, ignorar\n            }\n        }\n    }\n\n    /**\n     * Obtiene el sequence token del último evento\n     */\n    private function getSequenceToken(): ?string\n    {\n        try {\n            $result = $this->cloudWatchClient->describeLogStreams([\n                'logGroupName' => $this->logGroupName,\n                'logStreamNamePrefix' => $this->logStreamName,\n            ]);\n\n            if (!empty($result['logStreams'])) {\n                return $result['logStreams'][0]['uploadSequenceToken'] ?? null;\n            }\n        } catch (\\Exception $e) {\n            // Ignorar errores al obtener sequence token\n        }\n\n        return null;\n    }\n}\n"}]}