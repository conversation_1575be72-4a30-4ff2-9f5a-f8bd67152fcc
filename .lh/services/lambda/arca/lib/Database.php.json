{"sourceFile": "services/lambda/arca/lib/Database.php", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750460644895, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750460644895, "name": "Commit-0", "content": "<?php\n\nnamespace FuncionesComunes\\Database;\n\nuse Dotenv\\Dotenv;\nuse Exception;\nuse PDO;\nuse PDOException;\n\nclass Database\n{\n    private static $instance = null;\n    private $idempresa;\n    private $bd_saasargentina;\n    private $bd_link;\n    private $stage;\n    private $read = false;\n    private $beta = false;\n    private $alfa = false;\n\n    private function __construct(?array $config = null)\n    {\n        if (file_exists(__DIR__ . '/../.env')) {\n            $dotenv = Dotenv::createImmutable(__DIR__ . '/..');\n            $dotenv->load();\n        }\n\n        // El stage se pasa automáticamente desde serverless.yml como APP_ENV\n        $this->stage = $_ENV['APP_ENV'] ?? 'dev';\n        $this->conectarBdSaas();\n    }\n\n    private function mostrarError($mensaje) {\n        $texto = 'ERROR: '.$mensaje.'<br /><br />\n            idempresa: '.$this->idempresa.'\">'.$this->idempresa.'<br />\n            stage: '.$this->stage.'<br />';\n        echo $texto;\n        // email_queue(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ERROR en API', $texto, false);\n    }\n\n    private function conectarBdSaas(): void\n    {\n        try {\n            $host = $_ENV['BD_HOST'];\n            $user = $_ENV['BD_USER'];\n            $pass = $_ENV['BD_PASS'];\n            $port = $_ENV['BD_PORT'];\n\n            $dsn = \"mysql:host={$host};dbname=saasargentina;charset=utf8mb4;port={$port}\";\n            $this->bd_saasargentina = new PDO($dsn, $user, $pass, [\n                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC\n            ]);\n        } catch (PDOException $e) {\n            $this->mostrarError(\"Error conectando a la base de datos principal: \" . $e->getMessage());\n        }\n    }\n\n    public function conectarById(int $idempresa, bool $read = false): bool\n    {\n        try {\n\n            if (!is_numeric($idempresa)) {\n                throw new Exception(\"idempresa no es un número\");\n                return false;\n            }\n\n            $query = \"SELECT empresas.idempresa,\n                servidores.BD_HOST, servidores.BD_HOST_RO, servidores.BD_USER,\n                servidores.BD_PASS, servidores.BD_BD, servidores.BD_PORT,\n                servidores.BD_SOCKET, servidores.version\n            FROM empresas\n                LEFT JOIN servidores ON empresas.idservidor = servidores.idservidor\n            WHERE idempresa = :idempresa\n                AND estado IN ('prueba', 'activada', 'demo')\n                AND idsistema != 4\n            LIMIT 1\";\n\n            $stmt = $this->bd_saasargentina->prepare($query);\n            $stmt->execute(['idempresa' => $idempresa]);\n            $datos_servidor = $stmt->fetch();\n\n            if ($datos_servidor && $datos_servidor['idempresa'] == $idempresa) {\n                $this->idempresa = $datos_servidor['idempresa'];\n                $this->read = $read;\n\n                $this->conectarBd($datos_servidor);\n                return true;\n\n            } else {\n                throw new Exception(\"No se encontró la empresa\");\n                return false;\n            }\n        } catch (PDOException $e) {\n            $this->mostrarError(\"Error en la consulta: \" . $e->getMessage());\n            return false;\n        }\n    }\n\n    private function conectarBd(array $datos_servidor): bool\n    {\n        try {\n            $host = $this->read ? $datos_servidor['BD_HOST_RO'] : $datos_servidor['BD_HOST'];\n            $database = $datos_servidor['BD_BD'] . $datos_servidor['idempresa'];\n            $username = $datos_servidor['BD_USER'] . $datos_servidor['idempresa'];\n            $password = md5($datos_servidor['BD_PASS'] . $datos_servidor['idempresa']);\n\n            $dsn = \"mysql:host={$host};dbname={$database};charset=utf8mb4\";\n            if (!empty($datos_servidor['BD_PORT'])) {\n                $dsn .= \";port={$datos_servidor['BD_PORT']}\";\n            }\n            if (!empty($datos_servidor['BD_SOCKET'])) {\n                $dsn .= \";unix_socket={$datos_servidor['BD_SOCKET']}\";\n            }\n\n            $this->bd_link = new PDO($dsn, $username, $password, [\n                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,\n                PDO::ATTR_PERSISTENT => true\n            ]);\n\n            return true;\n\n        } catch (PDOException $e) {\n            $this->mostrarError(\"Error conectando a la base de datos de la empresa: \" . $e->getMessage());\n            return false;\n        }\n    }\n\n    public function conectarBdByIUE($iue, $read = false)\n    {\n        if (!$iue)\n            throw new Exception(\"No se recibió un IUE\");\n\n        $this->getIdByIUE($iue);\n        $this->conectarById($this->idempresa, $read);\n    }\n\n    public function conectarBdByMl($user_id, $read = false)\n    {\n        if (!$user_id)\n            throw new Exception(\"No se recibió un ML_user_id\");\n\n        $this->getIdByMl($user_id);\n        $this->conectarById($this->idempresa, $read);\n    }\n\n    private function getIdByIUE($iue)\n    {\n        $query = \"SELECT idempresa FROM empresas WHERE iue = :iue LIMIT 1\";\n        $stmt = $this->bd_saasargentina->prepare($query);\n        $stmt->execute(['iue' => $iue]);\n        $this->idempresa = $stmt->fetchColumn();\n    }\n\n    private function getIdByMl($user_id)\n    {\n        $query = \"SELECT idempresa FROM tiendas WHERE ML_user_id = :user_id LIMIT 1\";\n        $stmt = $this->bd_saasargentina->prepare($query);\n        $stmt->execute(['user_id' => $user_id]);\n        $this->idempresa = $stmt->fetchColumn();\n    }\n\n    public function consultaSql(string $sql, array $params = []): array\n    {\n        try {\n            $stmt = $this->bd_link->prepare($sql);\n            $stmt->execute($params);\n            return $stmt->fetchAll();\n        } catch (PDOException $e) {\n            $this->mostrarError(\"Error en la consulta: \" . $e->getMessage());\n            // Relanzar la excepción para que sea manejada por el código que llama\n            throw new Exception(\"Error en la consulta SQL: \" . $e->getMessage(), 0, $e);\n        }\n    }\n\n    public static function getInstance(): Database\n    {\n        if (self::$instance === null) {\n            self::$instance = new Database();\n        }\n        return self::$instance;\n    }\n\n    public function getBdLink(): PDO\n    {\n        return $this->bd_Link;\n    }\n\n    public function setReadMode(bool $read): void\n    {\n        $this->read = $read;\n    }\n\n    public function getIdEmpresa(): ?int\n    {\n        return $this->idempresa;\n    }\n\n    public function isBeta(): bool\n    {\n        return $this->beta;\n    }\n\n    public function isAlfa(): bool\n    {\n        return $this->alfa;\n    }\n\n    public function getStage(): string\n    {\n        return $this->stage;\n    }\n}\n"}]}