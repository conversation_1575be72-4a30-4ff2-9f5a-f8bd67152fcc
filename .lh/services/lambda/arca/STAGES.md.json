{"sourceFile": "services/lambda/arca/STAGES.md", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1750460644895, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750460705187, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,72 +0,0 @@\n-# Manejo de Stages en AFIP SDK Lambda\n-\n-## ¿Por qué existe APP_ENV?\n-\n-La variable `APP_ENV` se usa para identificar el ambiente actual y es **automáticamente configurada** por Serverless Framework.\n-\n-## Cómo funciona\n-\n-### 1. Deploy con stage\n-```bash\n-# Deploy a desarrollo\n-serverless deploy --stage=dev\n-\n-# Deploy a alfa  \n-serverless deploy --stage=alfa\n-\n-# Deploy a beta\n-serverless deploy --stage=beta\n-\n-# Deploy a producción\n-serverless deploy --stage=prod\n-```\n-\n-### 2. Configuración automática en serverless.yml\n-```yaml\n-provider:\n-    stage: ${opt:stage, 'dev'}  # Toma el stage del comando\n-    environment:\n-        APP_ENV: ${self:provider.stage}  # Pasa el stage como variable de entorno\n-```\n-\n-### 3. Uso en el código PHP\n-```php\n-// Automáticamente recibe el valor del deploy\n-$this->stage = $_ENV['APP_ENV'];  // 'dev', 'alfa', 'beta', o 'prod'\n-\n-// Se usa para:\n-// - Seleccionar bucket S3 correcto\n-// - Configurar ambiente AFIP (homologación vs producción)  \n-// - Logs y debugging\n-```\n-\n-## Ventajas de este enfoque\n-\n-✅ **Un solo comando**: `serverless deploy --stage=alfa`\n-✅ **Sin configuración manual**: El stage se pasa automáticamente\n-✅ **Consistente**: El mismo stage se usa en toda la aplicación\n-✅ **Escalable**: Fácil agregar nuevos ambientes\n-\n-## Ejemplo de flujo completo\n-\n-```bash\n-# 1. Developer hace cambios en código\n-# 2. Deploy a alfa para testing\n-serverless deploy --stage=alfa\n-\n-# 3. Testing OK, deploy a beta para UAT  \n-serverless deploy --stage=beta\n-\n-# 4. UAT OK, deploy a producción\n-serverless deploy --stage=prod\n-```\n-\n-Cada deploy automáticamente:\n-- Crea/actualiza la función Lambda correspondiente (`arca-alfa-afipsdk`, `arca-beta-afipsdk`, etc.)\n-- Setea `APP_ENV` con el stage correspondiente\n-- Conecta a la cola SQS del ambiente correcto\n-- Usa el bucket S3 del ambiente correcto\n-\n-## Para desarrollo local\n-\n-El archivo `.env` se usa solo para desarrollo local y contiene `APP_ENV=dev` como fallback.\n\\ No newline at end of file\n"}], "date": 1750460644895, "name": "Commit-0", "content": "# Manejo de Stages en AFIP SDK Lambda\n\n## ¿Por qué existe APP_ENV?\n\nLa variable `APP_ENV` se usa para identificar el ambiente actual y es **automáticamente configurada** por Serverless Framework.\n\n## Cómo funciona\n\n### 1. Deploy con stage\n```bash\n# Deploy a desarrollo\nserverless deploy --stage=dev\n\n# Deploy a alfa  \nserverless deploy --stage=alfa\n\n# Deploy a beta\nserverless deploy --stage=beta\n\n# Deploy a producción\nserverless deploy --stage=prod\n```\n\n### 2. Configuración automática en serverless.yml\n```yaml\nprovider:\n    stage: ${opt:stage, 'dev'}  # Toma el stage del comando\n    environment:\n        APP_ENV: ${self:provider.stage}  # Pasa el stage como variable de entorno\n```\n\n### 3. Uso en el código PHP\n```php\n// Automáticamente recibe el valor del deploy\n$this->stage = $_ENV['APP_ENV'];  // 'dev', 'alfa', 'beta', o 'prod'\n\n// Se usa para:\n// - Seleccionar bucket S3 correcto\n// - Configurar ambiente AFIP (homologación vs producción)  \n// - Logs y debugging\n```\n\n## Ventajas de este enfoque\n\n✅ **Un solo comando**: `serverless deploy --stage=alfa`\n✅ **Sin configuración manual**: El stage se pasa automáticamente\n✅ **Consistente**: El mismo stage se usa en toda la aplicación\n✅ **Escalable**: Fácil agregar nuevos ambientes\n\n## Ejemplo de flujo completo\n\n```bash\n# 1. Developer hace cambios en código\n# 2. Deploy a alfa para testing\nserverless deploy --stage=alfa\n\n# 3. Testing OK, deploy a beta para UAT  \nserverless deploy --stage=beta\n\n# 4. UAT OK, deploy a producción\nserverless deploy --stage=prod\n```\n\nCada deploy automáticamente:\n- Crea/actualiza la función Lambda correspondiente (`arca-alfa-afipsdk`, `arca-beta-afipsdk`, etc.)\n- Setea `APP_ENV` con el stage correspondiente\n- Conecta a la cola SQS del ambiente correcto\n- Usa el bucket S3 del ambiente correcto\n\n## Para desarrollo local\n\nEl archivo `.env` se usa solo para desarrollo local y contiene `APP_ENV=dev` como fallback.\n"}]}