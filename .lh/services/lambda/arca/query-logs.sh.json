{"sourceFile": "services/lambda/arca/query-logs.sh", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750517307525, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750517307525, "name": "Commit-0", "content": "#!/bin/bash\n\n# Script de ayuda para consultar logs de facturas AFIP\n# Uso: ./query-logs.sh [opción] [parámetros]\n\nLOG_GROUP=\"afip-facturas-log\"\nREGION=\"sa-east-1\"\n\nshow_help() {\n    echo \"🔍 Script de consulta de logs de facturas AFIP\"\n    echo \"\"\n    echo \"Uso: $0 [opción] [parámetros]\"\n    echo \"\"\n    echo \"Opciones:\"\n    echo \"  recent [N]           - Últimos N logs (por defecto 20)\"\n    echo \"  today               - Logs de hoy\"\n    echo \"  yesterday           - Logs de ayer\"\n    echo \"  date YYYY-MM-DD     - Logs de una fecha específica\"\n    echo \"  stage STAGE         - Logs de un stage (dev/alfa/beta/prod)\"\n    echo \"  empresa ID          - Logs de una empresa específica\"\n    echo \"  estado ESTADO       - Logs por estado (APROBADO/ERROR/PAUSADO)\"\n    echo \"  errors              - Solo errores\"\n    echo \"  follow              - Seguir logs en tiempo real\"\n    echo \"  help                - Mostrar esta ayuda\"\n    echo \"\"\n    echo \"Ejemplos:\"\n    echo \"  $0 recent 50        - Últimos 50 logs\"\n    echo \"  $0 stage prod       - Logs de producción\"\n    echo \"  $0 empresa 161      - Logs de empresa 161\"\n    echo \"  $0 date 2024-01-15  - Logs del 15 de enero\"\n    echo \"  $0 errors           - Solo errores\"\n}\n\n# Función para mostrar logs recientes\nrecent_logs() {\n    local limit=${1:-20}\n    echo \"📋 Últimos $limit logs:\"\n    aws logs filter-log-events \\\n        --log-group-name \"$LOG_GROUP\" \\\n        --limit \"$limit\" \\\n        --region \"$REGION\" \\\n        --query 'events[*].[timestamp,message]' \\\n        --output table\n}\n\n# Función para logs de hoy\ntoday_logs() {\n    local start_time=$(date -d 'today 00:00:00' +%s)000\n    echo \"📅 Logs de hoy:\"\n    aws logs filter-log-events \\\n        --log-group-name \"$LOG_GROUP\" \\\n        --start-time \"$start_time\" \\\n        --region \"$REGION\" \\\n        --query 'events[*].[timestamp,message]' \\\n        --output table\n}\n\n# Función para logs de ayer\nyesterday_logs() {\n    local start_time=$(date -d 'yesterday 00:00:00' +%s)000\n    local end_time=$(date -d 'today 00:00:00' +%s)000\n    echo \"📅 Logs de ayer:\"\n    aws logs filter-log-events \\\n        --log-group-name \"$LOG_GROUP\" \\\n        --start-time \"$start_time\" \\\n        --end-time \"$end_time\" \\\n        --region \"$REGION\" \\\n        --query 'events[*].[timestamp,message]' \\\n        --output table\n}\n\n# Función para logs de una fecha específica\ndate_logs() {\n    local date_str=\"$1\"\n    if [[ ! \"$date_str\" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]]; then\n        echo \"❌ Error: Formato de fecha inválido. Usar YYYY-MM-DD\"\n        exit 1\n    fi\n\n    local start_time=$(date -d \"$date_str 00:00:00\" +%s)000\n    local end_time=$(date -d \"$date_str 23:59:59\" +%s)000\n    echo \"📅 Logs del $date_str:\"\n    aws logs filter-log-events \\\n        --log-group-name \"$LOG_GROUP\" \\\n        --start-time \"$start_time\" \\\n        --end-time \"$end_time\" \\\n        --region \"$REGION\" \\\n        --query 'events[*].[timestamp,message]' \\\n        --output table\n}\n\n# Función para filtrar por stage\nstage_logs() {\n    local stage=\"$1\"\n    echo \"🏷️  Logs del stage '$stage':\"\n    aws logs filter-log-events \\\n        --log-group-name \"$LOG_GROUP\" \\\n        --filter-pattern \"\\\"stage\\\":\\\"$stage\\\"\" \\\n        --region \"$REGION\" \\\n        --query 'events[*].[timestamp,message]' \\\n        --output table\n}\n\n# Función para filtrar por empresa\nempresa_logs() {\n    local empresa=\"$1\"\n    echo \"🏢 Logs de la empresa $empresa:\"\n    aws logs filter-log-events \\\n        --log-group-name \"$LOG_GROUP\" \\\n        --filter-pattern \"\\\"idempresa\\\":$empresa\" \\\n        --region \"$REGION\" \\\n        --query 'events[*].[timestamp,message]' \\\n        --output table\n}\n\n# Función para filtrar por estado\nestado_logs() {\n    local estado=\"$1\"\n    echo \"📊 Logs con estado '$estado':\"\n    aws logs filter-log-events \\\n        --log-group-name \"$LOG_GROUP\" \\\n        --filter-pattern \"\\\"estado\\\":\\\"$estado\\\"\" \\\n        --region \"$REGION\" \\\n        --query 'events[*].[timestamp,message]' \\\n        --output table\n}\n\n# Función para solo errores\nerror_logs() {\n    echo \"❌ Solo errores:\"\n    aws logs filter-log-events \\\n        --log-group-name \"$LOG_GROUP\" \\\n        --filter-pattern '\"estado\":\"ERROR\"' \\\n        --region \"$REGION\" \\\n        --query 'events[*].[timestamp,message]' \\\n        --output table\n}\n\n# Función para seguir logs en tiempo real\nfollow_logs() {\n    echo \"👁️  Siguiendo logs en tiempo real (Ctrl+C para salir):\"\n    aws logs tail \"$LOG_GROUP\" --follow --region \"$REGION\"\n}\n\n# Verificar que AWS CLI esté configurado\nif ! command -v aws &> /dev/null; then\n    echo \"❌ Error: AWS CLI no está instalado\"\n    exit 1\nfi\n\nif ! aws sts get-caller-identity &> /dev/null; then\n    echo \"❌ Error: Credenciales de AWS no configuradas\"\n    exit 1\nfi\n\n# Procesar argumentos\ncase \"${1:-help}\" in\n    \"recent\")\n        recent_logs \"$2\"\n        ;;\n    \"today\")\n        today_logs\n        ;;\n    \"yesterday\")\n        yesterday_logs\n        ;;\n    \"date\")\n        if [ -z \"$2\" ]; then\n            echo \"❌ Error: Especifica una fecha (YYYY-MM-DD)\"\n            exit 1\n        fi\n        date_logs \"$2\"\n        ;;\n    \"stage\")\n        if [ -z \"$2\" ]; then\n            echo \"❌ Error: Especifica un stage (dev/alfa/beta/prod)\"\n            exit 1\n        fi\n        stage_logs \"$2\"\n        ;;\n    \"empresa\")\n        if [ -z \"$2\" ]; then\n            echo \"❌ Error: Especifica un ID de empresa\"\n            exit 1\n        fi\n        empresa_logs \"$2\"\n        ;;\n    \"estado\")\n        if [ -z \"$2\" ]; then\n            echo \"❌ Error: Especifica un estado (APROBADO/ERROR/PAUSADO)\"\n            exit 1\n        fi\n        estado_logs \"$2\"\n        ;;\n    \"errors\")\n        error_logs\n        ;;\n    \"follow\")\n        follow_logs\n        ;;\n    \"help\"|*)\n        show_help\n        ;;\nesac\n"}]}