{"sourceFile": "services/lambda/arca/setup-cloudwatch.sh", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750517306248, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750517306248, "name": "Commit-0", "content": "#!/bin/bash\n\n# Script para configurar CloudWatch para logging de facturas AFIP\n# Uso: ./setup-cloudwatch.sh\n\nset -e\n\nLOG_GROUP_NAME=\"afip-facturas-log\"\nREGION=\"sa-east-1\"\nRETENTION_DAYS=30\n\necho \"🚀 Configurando CloudWatch para logging de facturas AFIP...\"\n\n# Verificar si AWS CLI está instalado\nif ! command -v aws &> /dev/null; then\n    echo \"❌ Error: AWS CLI no está instalado\"\n    echo \"   Instala con: sudo apt install awscli\"\n    exit 1\nfi\n\n# Verificar credenciales de AWS\nif ! aws sts get-caller-identity &> /dev/null; then\n    echo \"❌ Error: Credenciales de AWS no configuradas\"\n    echo \"   Configura con: aws configure\"\n    exit 1\nfi\n\necho \"✅ AWS CLI configurado correctamente\"\n\n# Verificar si el log group ya existe\nif aws logs describe-log-groups --log-group-name-prefix \"$LOG_GROUP_NAME\" --region \"$REGION\" --query 'logGroups[?logGroupName==`'$LOG_GROUP_NAME'`]' --output text | grep -q \"$LOG_GROUP_NAME\"; then\n    echo \"ℹ️  Log group '$LOG_GROUP_NAME' ya existe\"\nelse\n    echo \"📝 Creando log group '$LOG_GROUP_NAME'...\"\n    aws logs create-log-group --log-group-name \"$LOG_GROUP_NAME\" --region \"$REGION\"\n    echo \"✅ Log group creado exitosamente\"\nfi\n\n# Configurar retención\necho \"⏰ Configurando retención de logs a $RETENTION_DAYS días...\"\naws logs put-retention-policy \\\n    --log-group-name \"$LOG_GROUP_NAME\" \\\n    --retention-in-days \"$RETENTION_DAYS\" \\\n    --region \"$REGION\"\n\necho \"✅ Retención configurada exitosamente\"\n\n# Mostrar información del log group\necho \"\"\necho \"📊 Información del log group:\"\naws logs describe-log-groups \\\n    --log-group-name-prefix \"$LOG_GROUP_NAME\" \\\n    --region \"$REGION\" \\\n    --query 'logGroups[0].{Nombre:logGroupName,Creación:creationTime,Retención:retentionInDays}' \\\n    --output table\n\necho \"\"\necho \"🎉 Configuración completada!\"\necho \"\"\necho \"💡 Comandos útiles:\"\necho \"   Ver logs: aws logs filter-log-events --log-group-name $LOG_GROUP_NAME --limit 20 --region $REGION\"\necho \"   Filtrar errores: aws logs filter-log-events --log-group-name $LOG_GROUP_NAME --filter-pattern '\\\"estado\\\":\\\"ERROR\\\"' --region $REGION\"\necho \"   Ver en tiempo real: aws logs tail $LOG_GROUP_NAME --follow --region $REGION\"\n"}]}