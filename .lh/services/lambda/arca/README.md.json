{"sourceFile": "services/lambda/arca/README.md", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1748981472177, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750184761643, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,23 +13,8 @@\n - La función Lambda actualiza el estado de la factura en la base de datos\n \n ## Arquitectura\n \n-```\n-┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐\n-│  Aplicación │    │  Cola SQS   │    │   Lambda    │    │    AFIP     │\n-│  Principal  │───►│ afipsdk-queue│───►│   afipsdk   │───►│    API     │\n-└─────────────┘    └─────────────┘    └──────┬──────┘    └─────────────┘\n-                                             │\n-                                             ▼\n-                                      ┌─────────────┐\n-                                      │  Base de    │\n-                                      │   Datos     │\n-                                      └─────────────┘\n-```\n-\n-## Componentes\n-\n ### 1. Cola SQS\n \n - **Nombre**: afipsdk-queue-{stage} (donde {stage} es dev, alfa, beta o prod)\n - **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-{stage}\n@@ -96,9 +81,9 @@\n # Probar con un evento específico\n serverless bref:local --function afipsdk --data '{\"Records\":[{\"body\":\"161|3567\"}]}'\n \n # O usando el script de ayuda\n-./test-local.sh 161 3567\n+php ./test.php 161 3567\n ```\n \n ### Pruebas en AWS\n \n"}, {"date": 1750445358754, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,30 +11,256 @@\n - La función Lambda obtiene los datos de la factura de la base de datos\n - La función Lambda envía los datos a AFIP y recibe la respuesta\n - La función Lambda actualiza el estado de la factura en la base de datos\n \n+## Configuración de Variables de Entorno\n+\n+### Variables Requeridas en AWS Lambda\n+\n+Estas variables deben configurarse en la consola de AWS Lambda para cada ambiente:\n+\n+#### Base de Datos\n+```bash\n+BD_HOST=127.0.0.1\n+BD_USER=yosoyroot\n+BD_PASS=8des4rollo\n+BD_BD=saasargentina\n+BD_PORT=3306\n+```\n+\n+#### AWS General\n+```bash\n+AWS_REGION=sa-east-1\n+AWS_ACCOUNT_ID=************\n+```\n+\n+#### AWS SQS - Colas para AFIP SDK\n+```bash\n+AWS_SQS_AFIPSDK_QUEUE_DEV=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\n+AWS_SQS_AFIPSDK_QUEUE_ALFA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\n+AWS_SQS_AFIPSDK_QUEUE_BETA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\n+AWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\n+```\n+\n+#### AWS SQS - Cola para emails\n+```bash\n+AWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\n+```\n+\n+#### AWS SQS - Credenciales\n+```bash\n+AWS_SQS_QUEUER_KEY=********************\n+AWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n+```\n+\n+#### AWS S3 - Buckets por ambiente\n+```bash\n+AWS_S3_WSFE_BUCKET_DEV=saasargentina-wsfe-dev\n+AWS_S3_WSFE_BUCKET_ALFA=saasargentina-wsfe-alfa\n+AWS_S3_WSFE_BUCKET_BETA=saasargentina-wsfe-beta\n+AWS_S3_WSFE_BUCKET_PROD=saasargentina-wsfe\n+```\n+\n+#### AWS S3 - Credenciales\n+```bash\n+AWS_S3_KEY=YOUR_S3_ACCESS_KEY\n+AWS_S3_SECRET=YOUR_S3_SECRET_KEY\n+```\n+\n+#### AFIP - Configuración de ambiente\n+```bash\n+AFIP_PRODUCTION=false  # true para producción, false para homologación\n+```\n+\n+#### Email - Configuración de errores\n+```bash\n+MAIL_SERVIDOR=<EMAIL>\n+MAIL_DESARROLLO=<EMAIL>\n+```\n+\n+#### Ambiente de aplicación\n+```bash\n+APP_ENV=dev  # Valores: dev, alfa, beta, prod\n+```\n+\n+## Manejo de Ambientes\n+\n+### Configuración por Ambiente\n+\n+El sistema maneja 4 ambientes principales:\n+\n+#### 1. Desarrollo (dev)\n+- **Uso**: Desarrollo local y pruebas\n+- **Cola SQS**: `afipsdk-queue-dev`\n+- **Bucket S3**: `saasargentina-wsfe-dev`\n+- **AFIP**: Ambiente de homologación\n+- **Deploy**: No se despliega automáticamente\n+\n+#### 2. Alfa (alfa)\n+- **Uso**: Testing interno y pruebas de integración\n+- **Cola SQS**: `afipsdk-queue-alfa`\n+- **Bucket S3**: `saasargentina-wsfe-alfa`\n+- **AFIP**: Ambiente de homologación\n+- **Deploy**: `serverless deploy --stage=alfa`\n+\n+#### 3. Beta (beta)\n+- **Uso**: Testing de usuarios y pruebas finales\n+- **Cola SQS**: `afipsdk-queue-beta`\n+- **Bucket S3**: `saasargentina-wsfe-beta`\n+- **AFIP**: Ambiente de homologación\n+- **Deploy**: `serverless deploy --stage=beta`\n+\n+#### 4. Producción (prod)\n+- **Uso**: Ambiente de producción\n+- **Cola SQS**: `afipsdk-queue-prod`\n+- **Bucket S3**: `saasargentina-wsfe`\n+- **AFIP**: Ambiente de producción\n+- **Deploy**: `serverless deploy --stage=prod`\n+\n+### Detección de Ambiente\n+\n+El sistema determina el ambiente basándose en:\n+\n+1. **Variable de entorno `APP_ENV`** (prioritaria)\n+2. **Detección automática por stage de Serverless**\n+\n+### Enrutamiento de Mensajes\n+\n+La función `afipsdk_lambda()` en `funciones_aws.php` determina automáticamente la cola correcta basándose en la variable de sesión `$_SESSION['servidor_version']`:\n+\n+- `$_SESSION['servidor_version'] == 'ALFA'` → Cola ALFA\n+- `$_SESSION['servidor_version'] == 'BETA'` → Cola BETA\n+- `$_SESSION['servidor_version'] == ''` o no definida → Cola PROD\n+\n+### Configuración AFIP (Homologación vs Producción)\n+\n+La configuración de AFIP se determina por:\n+\n+1. **Variable `AFIP_PRODUCTION`**: Controla si se usa el ambiente de producción de AFIP\n+2. **Ambiente de aplicación**: Por defecto, solo `prod` usa producción de AFIP\n+\n ## Arquitectura\n \n-### 1. Cola SQS\n+### 1. Colas SQS por Ambiente\n \n-- **Nombre**: afipsdk-queue-{stage} (donde {stage} es dev, alfa, beta o prod)\n-- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-{stage}\n-- **Formato del mensaje**: idempresa|idventa (ambos deben ser números enteros positivos)\n+#### Desarrollo\n+- **Nombre**: afipsdk-queue-dev\n+- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\n \n-### 2. Función Lambda\n+#### Alfa\n+- **Nombre**: afipsdk-queue-alfa\n+- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\n \n-- **Nombre**: arca-{stage}-afipsdk\n-- **Descripción**: Procesador de facturas para AFIP\n+#### Beta\n+- **Nombre**: afipsdk-queue-beta\n+- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\n+\n+#### Producción\n+- **Nombre**: afipsdk-queue-prod\n+- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\n+\n+**Formato del mensaje**: idempresa|idventa (ambos deben ser números enteros positivos)\n+\n+### 2. Funciones Lambda por Ambiente\n+\n+- **Desarrollo**: arca-dev-afipsdk\n+- **Alfa**: arca-alfa-afipsdk\n+- **Beta**: arca-beta-afipsdk\n+- **Producción**: arca-prod-afipsdk\n+\n+**Configuración común**:\n - **Runtime**: PHP 8.3\n - **Código fuente**: services/lambda/arca/public/afipsdk.php\n - **Configuración**: services/lambda/arca/serverless.yml\n \n-### 3. Base de Datos\n+### 3. Buckets S3 por Ambiente\n \n-- Se utiliza la clase Database de services/lambda/api/lib/Database.php\n-- Conecta a la base de datos de la empresa correspondiente\n-- Obtiene los datos de la factura y actualiza su estado\n+- **Desarrollo**: saasargentina-wsfe-dev\n+- **Alfa**: saasargentina-wsfe-alfa\n+- **Beta**: saasargentina-wsfe-beta\n+- **Producción**: saasargentina-wsfe\n \n+## Configuración en AWS\n+\n+### 1. Configurar Variables de Entorno en Lambda\n+\n+Para cada función Lambda (dev, alfa, beta, prod), configurar las variables de entorno correspondientes en la consola de AWS:\n+\n+1. Ir a AWS Lambda → Funciones → [nombre-función]\n+2. Ir a Configuración → Variables de entorno\n+3. Agregar todas las variables listadas arriba\n+4. Ajustar los valores según el ambiente\n+\n+### 2. Crear Colas SQS\n+\n+```bash\n+# Crear cola para desarrollo\n+aws sqs create-queue --queue-name afipsdk-queue-dev --region sa-east-1\n+\n+# Crear cola para alfa\n+aws sqs create-queue --queue-name afipsdk-queue-alfa --region sa-east-1\n+\n+# Crear cola para beta\n+aws sqs create-queue --queue-name afipsdk-queue-beta --region sa-east-1\n+\n+# Crear cola para producción\n+aws sqs create-queue --queue-name afipsdk-queue-prod --region sa-east-1\n+```\n+\n+### 3. Crear Buckets S3\n+\n+```bash\n+# Crear bucket para desarrollo\n+aws s3 mb s3://saasargentina-wsfe-dev --region sa-east-1\n+\n+# Crear bucket para alfa\n+aws s3 mb s3://saasargentina-wsfe-alfa --region sa-east-1\n+\n+# Crear bucket para beta\n+aws s3 mb s3://saasargentina-wsfe-beta --region sa-east-1\n+\n+# Bucket de producción ya existe: saasargentina-wsfe\n+```\n+\n+### 4. Configurar Triggers SQS\n+\n+Para cada función Lambda, configurar el trigger de SQS correspondiente:\n+\n+1. Ir a AWS Lambda → Funciones → [nombre-función]\n+2. Ir a Configuración → Triggers\n+3. Agregar trigger → SQS\n+4. Seleccionar la cola correspondiente al ambiente\n+\n+## Comandos de Deploy\n+\n+### Deploy por Ambiente\n+\n+```bash\n+# Navegar al directorio del proyecto\n+cd services/lambda/arca\n+\n+# Deploy en desarrollo\n+serverless deploy --stage=dev\n+\n+# Deploy en alfa\n+serverless deploy --stage=alfa\n+\n+# Deploy en beta\n+serverless deploy --stage=beta\n+\n+# Deploy en producción\n+serverless deploy --stage=prod\n+```\n+\n+### Deploy con Variables de Entorno\n+\n+```bash\n+# Deploy con variables específicas\n+serverless deploy --stage=prod \\\n+  --param=\"awsRegion=sa-east-1\" \\\n+  --param=\"afipProduction=true\"\n+```\n+\n ## Flujo de Procesamiento\n \n 1. **Recepción del mensaje**:\n    - La función Lambda recibe un mensaje de la cola SQS\n@@ -117,18 +343,21 @@\n ```\n \n ## Código de Ejemplo\n \n-### Enviar un mensaje desde PHP\n+### Enviar un mensaje desde PHP (Actualizado)\n \n ```php\n function afipsdk_lambda($idempresa, $idventa)\n {\n     try {\n         $sqs = conectar_aws('sqs');\n \n+        // Determinar la cola según el ambiente de la empresa\n+        $queueUrl = obtener_cola_afipsdk($idempresa);\n+\n         $params = [\n-            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE,\n+            'QueueUrl' => $queueUrl,\n             'DelaySeconds' => 10,\n             'MessageBody' => \"$idempresa|$idventa\",\n         ];\n \n@@ -141,18 +370,56 @@\n         return false;\n     }\n     return true;\n }\n+\n+/**\n+ * Obtiene la URL de la cola SQS según el ambiente de la empresa\n+ */\n+function obtener_cola_afipsdk($idempresa)\n+{\n+    // Determinar el ambiente basado en la versión del servidor de la sesión\n+    $ambiente = 'prod'; // Por defecto producción\n+    \n+    if (isset($_SESSION['servidor_version'])) {\n+        if ($_SESSION['servidor_version'] == 'BETA') {\n+            $ambiente = 'beta';\n+        } elseif ($_SESSION['servidor_version'] == 'ALFA') {\n+            $ambiente = 'alfa';\n+        }\n+    }\n+    \n+    // Determinar la cola según el ambiente\n+    switch ($ambiente) {\n+        case 'alfa':\n+            return AWS_URL_AFIPSDK_QUEUE_ALFA;\n+        case 'beta':\n+            return AWS_URL_AFIPSDK_QUEUE_BETA;\n+        case 'prod':\n+        default:\n+            return AWS_URL_AFIPSDK_QUEUE_PROD;\n+    }\n+}\n ```\n \n ## Solución de Problemas\n \n ### El Lambda no procesa los mensajes\n \n-Verifica que la cola SQS tenga mensajes:\n+Verifica que la cola SQS tenga mensajes (ajusta la URL según el ambiente):\n \n ```bash\n+# Para desarrollo\n aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev --attribute-names ApproximateNumberOfMessages\n+\n+# Para alfa\n+aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa --attribute-names ApproximateNumberOfMessages\n+\n+# Para beta\n+aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta --attribute-names ApproximateNumberOfMessages\n+\n+# Para producción\n+aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod --attribute-names ApproximateNumberOfMessages\n ```\n \n Verifica que el Lambda tenga los permisos correctos:\n \n@@ -165,15 +432,30 @@\n ```bash\n aws lambda list-event-source-mappings --function-name arca-dev-afipsdk\n ```\n \n+### Problemas de Variables de Entorno\n+\n+1. **Verificar configuración**:\n+```bash\n+# Ver variables de entorno configuradas\n+aws lambda get-function-configuration --function-name arca-dev-afipsdk --query 'Environment.Variables'\n+```\n+\n+2. **Variables faltantes**: Verificar que todas las variables requeridas estén configuradas\n+3. **Valores incorrectos**: Verificar que los valores de URLs y credenciales sean correctos\n+\n ### Errores en el Lambda\n \n - Revisa los logs para ver el error específico:\n \n ```bash\n-serverless logs -f afipsdk\n+serverless logs -f afipsdk --stage=dev\n+serverless logs -f afipsdk --stage=alfa\n+serverless logs -f afipsdk --stage=beta\n+serverless logs -f afipsdk --stage=prod\n ```\n+```\n \n - Verifica que la conexión a la base de datos funcione correctamente\n - Verifica que los datos de la factura sean correctos\n - Verifica que la conexión con AFIP funcione correctamente\n"}, {"date": 1750459767548, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -51,16 +51,8 @@\n AWS_SQS_QUEUER_KEY=********************\n AWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n ```\n \n-#### AWS S3 - Buckets por ambiente\n-```bash\n-AWS_S3_WSFE_BUCKET_DEV=saasargentina-wsfe-dev\n-AWS_S3_WSFE_BUCKET_ALFA=saasargentina-wsfe-alfa\n-AWS_S3_WSFE_BUCKET_BETA=saasargentina-wsfe-beta\n-AWS_S3_WSFE_BUCKET_PROD=saasargentina-wsfe\n-```\n-\n #### AWS S3 - Credenciales\n ```bash\n AWS_S3_KEY=YOUR_S3_ACCESS_KEY\n AWS_S3_SECRET=YOUR_S3_SECRET_KEY\n@@ -378,17 +370,17 @@\n function obtener_cola_afipsdk($idempresa)\n {\n     // Determinar el ambiente basado en la versión del servidor de la sesión\n     $ambiente = 'prod'; // Por defecto producción\n-    \n+\n     if (isset($_SESSION['servidor_version'])) {\n         if ($_SESSION['servidor_version'] == 'BETA') {\n             $ambiente = 'beta';\n         } elseif ($_SESSION['servidor_version'] == 'ALFA') {\n             $ambiente = 'alfa';\n         }\n     }\n-    \n+\n     // Determinar la cola según el ambiente\n     switch ($ambiente) {\n         case 'alfa':\n             return AWS_URL_AFIPSDK_QUEUE_ALFA;\n"}, {"date": 1750460703803, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -466,4 +466,78 @@\n - Agregar manejo de errores más robusto\n - Implementar reintentos para casos de fallo\n - Agregar notificaciones para errores críticos\n - Implementar monitoreo y alertas\n+\n+\n+# Manejo de Stages en AFIP SDK Lambda\n+\n+## ¿Por qué existe APP_ENV?\n+\n+La variable `APP_ENV` se usa para identificar el ambiente actual y es **automáticamente configurada** por Serverless Framework.\n+\n+## Cómo funciona\n+\n+### 1. Deploy con stage\n+```bash\n+# Deploy a desarrollo\n+serverless deploy --stage=dev\n+\n+# Deploy a alfa  \n+serverless deploy --stage=alfa\n+\n+# Deploy a beta\n+serverless deploy --stage=beta\n+\n+# Deploy a producción\n+serverless deploy --stage=prod\n+```\n+\n+### 2. Configuración automática en serverless.yml\n+```yaml\n+provider:\n+    stage: ${opt:stage, 'dev'}  # Toma el stage del comando\n+    environment:\n+        APP_ENV: ${self:provider.stage}  # Pasa el stage como variable de entorno\n+```\n+\n+### 3. Uso en el código PHP\n+```php\n+// Automáticamente recibe el valor del deploy\n+$this->stage = $_ENV['APP_ENV'];  // 'dev', 'alfa', 'beta', o 'prod'\n+\n+// Se usa para:\n+// - Seleccionar bucket S3 correcto\n+// - Configurar ambiente AFIP (homologación vs producción)  \n+// - Logs y debugging\n+```\n+\n+## Ventajas de este enfoque\n+\n+✅ **Un solo comando**: `serverless deploy --stage=alfa`\n+✅ **Sin configuración manual**: El stage se pasa automáticamente\n+✅ **Consistente**: El mismo stage se usa en toda la aplicación\n+✅ **Escalable**: Fácil agregar nuevos ambientes\n+\n+## Ejemplo de flujo completo\n+\n+```bash\n+# 1. Developer hace cambios en código\n+# 2. Deploy a alfa para testing\n+serverless deploy --stage=alfa\n+\n+# 3. Testing OK, deploy a beta para UAT  \n+serverless deploy --stage=beta\n+\n+# 4. UAT OK, deploy a producción\n+serverless deploy --stage=prod\n+```\n+\n+Cada deploy automáticamente:\n+- Crea/actualiza la función Lambda correspondiente (`arca-alfa-afipsdk`, `arca-beta-afipsdk`, etc.)\n+- Setea `APP_ENV` con el stage correspondiente\n+- Conecta a la cola SQS del ambiente correcto\n+- Usa el bucket S3 del ambiente correcto\n+\n+## Para desarrollo local\n+\n+El archivo `.env` se usa solo para desarrollo local y contiene `APP_ENV=dev` como fallback.\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,39 +20,21 @@\n \n #### Base de Datos\n ```bash\n BD_HOST=127.0.0.1\n-BD_USER=yosoyroot\n-BD_PASS=8des4rollo\n+BD_USER=root\n+BD_PASS=pass\n BD_BD=saasargentina\n BD_PORT=3306\n ```\n \n-#### AWS General\n-```bash\n-AWS_REGION=sa-east-1\n-AWS_ACCOUNT_ID=************\n-```\n-\n-#### AWS SQS - Colas para AFIP SDK\n-```bash\n-AWS_SQS_AFIPSDK_QUEUE_DEV=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\n-AWS_SQS_AFIPSDK_QUEUE_ALFA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\n-AWS_SQS_AFIPSDK_QUEUE_BETA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\n-AWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\n-```\n-\n #### AWS SQS - Cola para emails\n ```bash\n AWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\n+AWS_SQS_QUEUER_KEY=YOUR_AWS_SQS_QUEUER_KEY\n+AWS_SQS_QUEUER_SECRET=YOUR_AWS_SQS_QUEUER_SECRET\n ```\n \n-#### AWS SQS - Credenciales\n-```bash\n-AWS_SQS_QUEUER_KEY=********************\n-AWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n-```\n-\n #### AWS S3 - Credenciales\n ```bash\n AWS_S3_KEY=YOUR_S3_ACCESS_KEY\n AWS_S3_SECRET=YOUR_S3_SECRET_KEY\n@@ -70,47 +52,17 @@\n ```\n \n #### Ambiente de aplicación\n ```bash\n-APP_ENV=dev  # Valores: dev, alfa, beta, prod\n+APP_ENV=dev  # Valores: dev, alfa, beta, prod y se auto completa según el deploy de serverless\n ```\n \n ## Manejo de Ambientes\n \n ### Configuración por Ambiente\n \n-El sistema maneja 4 ambientes principales:\n+El sistema maneja 4 ambientes principales: dev, alfa, beta, prod\n \n-#### 1. Desarrollo (dev)\n-- **Uso**: Desarrollo local y pruebas\n-- **Cola SQS**: `afipsdk-queue-dev`\n-- **Bucket S3**: `saasargentina-wsfe-dev`\n-- **AFIP**: Ambiente de homologación\n-- **Deploy**: No se despliega automáticamente\n-\n-#### 2. Alfa (alfa)\n-- **Uso**: Testing interno y pruebas de integración\n-- **Cola SQS**: `afipsdk-queue-alfa`\n-- **Bucket S3**: `saasargentina-wsfe-alfa`\n-- **AFIP**: Ambiente de homologación\n-- **Deploy**: `serverless deploy --stage=alfa`\n-\n-#### 3. Beta (beta)\n-- **Uso**: Testing de usuarios y pruebas finales\n-- **Cola SQS**: `afipsdk-queue-beta`\n-- **Bucket S3**: `saasargentina-wsfe-beta`\n-- **AFIP**: Ambiente de homologación\n-- **Deploy**: `serverless deploy --stage=beta`\n-\n-#### 4. Producción (prod)\n-- **Uso**: Ambiente de producción\n-- **Cola SQS**: `afipsdk-queue-prod`\n-- **Bucket S3**: `saasargentina-wsfe`\n-- **AFIP**: Ambiente de producción\n-- **Deploy**: `serverless deploy --stage=prod`\n-\n-### Detección de Ambiente\n-\n El sistema determina el ambiente basándose en:\n \n 1. **Variable de entorno `APP_ENV`** (prioritaria)\n 2. **Detección automática por stage de Serverless**\n@@ -126,52 +78,17 @@\n ### Configuración AFIP (Homologación vs Producción)\n \n La configuración de AFIP se determina por:\n \n-1. **Variable `AFIP_PRODUCTION`**: Controla si se usa el ambiente de producción de AFIP\n+1. **Variable `AFIP_PRODUCTION`**: Controla si se usa el ambiente de producción de AFIP. La idea es poder cambiarlo desde la variable de entorno de Lambda\n 2. **Ambiente de aplicación**: Por defecto, solo `prod` usa producción de AFIP\n \n-## Arquitectura\n-\n ### 1. Colas SQS por Ambiente\n \n-#### Desarrollo\n-- **Nombre**: afipsdk-queue-dev\n-- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\n+- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-${stage}\n \n-#### Alfa\n-- **Nombre**: afipsdk-queue-alfa\n-- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\n-\n-#### Beta\n-- **Nombre**: afipsdk-queue-beta\n-- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\n-\n-#### Producción\n-- **Nombre**: afipsdk-queue-prod\n-- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\n-\n **Formato del mensaje**: idempresa|idventa (ambos deben ser números enteros positivos)\n \n-### 2. Funciones Lambda por Ambiente\n-\n-- **Desarrollo**: arca-dev-afipsdk\n-- **Alfa**: arca-alfa-afipsdk\n-- **Beta**: arca-beta-afipsdk\n-- **Producción**: arca-prod-afipsdk\n-\n-**Configuración común**:\n-- **Runtime**: PHP 8.3\n-- **Código fuente**: services/lambda/arca/public/afipsdk.php\n-- **Configuración**: services/lambda/arca/serverless.yml\n-\n-### 3. Buckets S3 por Ambiente\n-\n-- **Desarrollo**: saasargentina-wsfe-dev\n-- **Alfa**: saasargentina-wsfe-alfa\n-- **Beta**: saasargentina-wsfe-beta\n-- **Producción**: saasargentina-wsfe\n-\n ## Configuración en AWS\n \n ### 1. Configurar Variables de Entorno en Lambda\n \n@@ -181,48 +98,8 @@\n 2. Ir a Configuración → Variables de entorno\n 3. Agregar todas las variables listadas arriba\n 4. Ajustar los valores según el ambiente\n \n-### 2. Crear Colas SQS\n-\n-```bash\n-# Crear cola para desarrollo\n-aws sqs create-queue --queue-name afipsdk-queue-dev --region sa-east-1\n-\n-# Crear cola para alfa\n-aws sqs create-queue --queue-name afipsdk-queue-alfa --region sa-east-1\n-\n-# Crear cola para beta\n-aws sqs create-queue --queue-name afipsdk-queue-beta --region sa-east-1\n-\n-# Crear cola para producción\n-aws sqs create-queue --queue-name afipsdk-queue-prod --region sa-east-1\n-```\n-\n-### 3. Crear Buckets S3\n-\n-```bash\n-# Crear bucket para desarrollo\n-aws s3 mb s3://saasargentina-wsfe-dev --region sa-east-1\n-\n-# Crear bucket para alfa\n-aws s3 mb s3://saasargentina-wsfe-alfa --region sa-east-1\n-\n-# Crear bucket para beta\n-aws s3 mb s3://saasargentina-wsfe-beta --region sa-east-1\n-\n-# Bucket de producción ya existe: saasargentina-wsfe\n-```\n-\n-### 4. Configurar Triggers SQS\n-\n-Para cada función Lambda, configurar el trigger de SQS correspondiente:\n-\n-1. Ir a AWS Lambda → Funciones → [nombre-función]\n-2. Ir a Configuración → Triggers\n-3. Agregar trigger → SQS\n-4. Seleccionar la cola correspondiente al ambiente\n-\n ## Comandos de Deploy\n \n ### Deploy por Ambiente\n \n@@ -273,25 +150,8 @@\n 5. **Actualización de la base de datos**:\n    - Actualiza el estado de la factura con el resultado de AFIP\n    - Guarda el CAE y otros datos relevantes\n \n-## Comandos\n-\n-### Despliegue\n-\n-```bash\n-# Navegar al directorio del proyecto\n-cd services/lambda/arca\n-\n-# Desplegar en el entorno de desarrollo\n-serverless deploy --stage=dev\n-\n-# Desplegar en otros entornos\n-serverless deploy --stage=alfa\n-serverless deploy --stage=beta\n-serverless deploy --stage=prod\n-```\n-\n ### Pruebas locales\n \n Para probar la función localmente sin necesidad de enviar mensajes a la cola SQS:\n \n@@ -333,67 +193,8 @@\n # Purgar la cola (eliminar todos los mensajes)\n aws sqs purge-queue --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\n ```\n \n-## Código de Ejemplo\n-\n-### Enviar un mensaje desde PHP (Actualizado)\n-\n-```php\n-function afipsdk_lambda($idempresa, $idventa)\n-{\n-    try {\n-        $sqs = conectar_aws('sqs');\n-\n-        // Determinar la cola según el ambiente de la empresa\n-        $queueUrl = obtener_cola_afipsdk($idempresa);\n-\n-        $params = [\n-            'QueueUrl' => $queueUrl,\n-            'DelaySeconds' => 10,\n-            'MessageBody' => \"$idempresa|$idventa\",\n-        ];\n-\n-        $sqs->sendMessage($params);\n-\n-    } catch (Exception $e) {\n-        mostrar_error('No se envió un mensaje con SQS.<br>'\n-            .'Error: '.$e->getMessage().'<br>'\n-            .'Parametros: '.json_encode($params), true);\n-        return false;\n-    }\n-    return true;\n-}\n-\n-/**\n- * Obtiene la URL de la cola SQS según el ambiente de la empresa\n- */\n-function obtener_cola_afipsdk($idempresa)\n-{\n-    // Determinar el ambiente basado en la versión del servidor de la sesión\n-    $ambiente = 'prod'; // Por defecto producción\n-\n-    if (isset($_SESSION['servidor_version'])) {\n-        if ($_SESSION['servidor_version'] == 'BETA') {\n-            $ambiente = 'beta';\n-        } elseif ($_SESSION['servidor_version'] == 'ALFA') {\n-            $ambiente = 'alfa';\n-        }\n-    }\n-\n-    // Determinar la cola según el ambiente\n-    switch ($ambiente) {\n-        case 'alfa':\n-            return AWS_URL_AFIPSDK_QUEUE_ALFA;\n-        case 'beta':\n-            return AWS_URL_AFIPSDK_QUEUE_BETA;\n-        case 'prod':\n-        default:\n-            return AWS_URL_AFIPSDK_QUEUE_PROD;\n-    }\n-}\n-```\n-\n ## Solución de Problemas\n \n ### El Lambda no procesa los mensajes\n \n@@ -424,19 +225,8 @@\n ```bash\n aws lambda list-event-source-mappings --function-name arca-dev-afipsdk\n ```\n \n-### Problemas de Variables de Entorno\n-\n-1. **Verificar configuración**:\n-```bash\n-# Ver variables de entorno configuradas\n-aws lambda get-function-configuration --function-name arca-dev-afipsdk --query 'Environment.Variables'\n-```\n-\n-2. **Variables faltantes**: Verificar que todas las variables requeridas estén configuradas\n-3. **Valores incorrectos**: Verificar que los valores de URLs y credenciales sean correctos\n-\n ### Errores en el Lambda\n \n - Revisa los logs para ver el error específico:\n \n@@ -447,97 +237,5 @@\n serverless logs -f afipsdk --stage=prod\n ```\n ```\n \n-- Verifica que la conexión a la base de datos funcione correctamente\n-- Verifica que los datos de la factura sean correctos\n-- Verifica que la conexión con AFIP funcione correctamente\n-\n-### Problemas de conexión a la base de datos\n-\n-Si ves errores como \"Connection refused\" o \"Call to a member function prepare() on null\", verifica:\n-\n-1. Que la función Lambda esté configurada para ejecutarse dentro de la VPC correcta\n-2. Que los grupos de seguridad permitan la conexión desde la Lambda a la base de datos\n-3. Que las credenciales de la base de datos sean correctas\n-\n-## Próximos Pasos\n-\n-- Implementar la integración real con la API de AFIP usando la librería afipsdk/afip.php\n-- Agregar manejo de errores más robusto\n-- Implementar reintentos para casos de fallo\n-- Agregar notificaciones para errores críticos\n-- Implementar monitoreo y alertas\n-\n-\n-# Manejo de Stages en AFIP SDK Lambda\n-\n-## ¿Por qué existe APP_ENV?\n-\n-La variable `APP_ENV` se usa para identificar el ambiente actual y es **automáticamente configurada** por Serverless Framework.\n-\n-## Cómo funciona\n-\n-### 1. Deploy con stage\n-```bash\n-# Deploy a desarrollo\n-serverless deploy --stage=dev\n-\n-# Deploy a alfa\n-serverless deploy --stage=alfa\n-\n-# Deploy a beta\n-serverless deploy --stage=beta\n-\n-# Deploy a producción\n-serverless deploy --stage=prod\n-```\n-\n-### 2. Configuración automática en serverless.yml\n-```yaml\n-provider:\n-    stage: ${opt:stage, 'dev'}  # Toma el stage del comando\n-    environment:\n-        APP_ENV: ${self:provider.stage}  # Pasa el stage como variable de entorno\n-```\n-\n-### 3. Uso en el código PHP\n-```php\n-// Automáticamente recibe el valor del deploy\n-$this->stage = $_ENV['APP_ENV'];  // 'dev', 'alfa', 'beta', o 'prod'\n-\n-// Se usa para:\n-// - Seleccionar bucket S3 correcto\n-// - Configurar ambiente AFIP (homologación vs producción)\n-// - Logs y debugging\n-```\n-\n-## Ventajas de este enfoque\n-\n-✅ **Un solo comando**: `serverless deploy --stage=alfa`\n-✅ **Sin configuración manual**: El stage se pasa automáticamente\n-✅ **Consistente**: El mismo stage se usa en toda la aplicación\n-✅ **Escalable**: Fácil agregar nuevos ambientes\n-\n-## Ejemplo de flujo completo\n-\n-```bash\n-# 1. Developer hace cambios en código\n-# 2. Deploy a alfa para testing\n-serverless deploy --stage=alfa\n-\n-# 3. Testing OK, deploy a beta para UAT\n-serverless deploy --stage=beta\n-\n-# 4. UAT OK, deploy a producción\n-serverless deploy --stage=prod\n-```\n-\n-Cada deploy automáticamente:\n-- Crea/actualiza la función Lambda correspondiente (`arca-alfa-afipsdk`, `arca-beta-afipsdk`, etc.)\n-- Setea `APP_ENV` con el stage correspondiente\n-- Conecta a la cola SQS del ambiente correcto\n-- Usa el bucket S3 del ambiente correcto\n-\n-## Para desarrollo local\n-\n-El archivo `.env` se usa solo para desarrollo local y contiene `APP_ENV=dev` como fallback.\n+- Verifica que la conexión a la base de dato\n\\ No newline at end of file\n"}, {"date": 1750515077751, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,294 @@\n+# AFIP SDK Lambda Function\n+\n+Esta función Lambda procesa mensajes de la cola SQS para generar facturas electrónicas usando la API de AFIPSDK.\n+\n+## Descripción General\n+\n+El sistema utiliza AWS Lambda para procesar facturas electrónicas a través de la API de AFIPSDK. El flujo de trabajo es el siguiente:\n+\n+- La aplicación principal envía un mensaje a una cola SQS con el formato `idempresa|idventa`\n+- La función Lambda afipsdk recibe el mensaje y procesa la factura\n+- La función Lambda obtiene los datos de la factura de la base de datos\n+- La función Lambda envía los datos a AFIP y recibe la respuesta\n+- La función Lambda actualiza el estado de la factura en la base de datos\n+\n+## Configuración de Variables de Entorno\n+\n+### Variables Requeridas en AWS Lambda\n+\n+Estas variables deben configurarse en la consola de AWS Lambda para cada ambiente:\n+\n+#### Base de Datos\n+```bash\n+BD_HOST=127.0.0.1\n+BD_USER=root\n+BD_PASS=pass\n+BD_BD=saasargentina\n+BD_PORT=3306\n+```\n+\n+#### AWS SQS - Cola para emails\n+```bash\n+AWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\n+AWS_SQS_QUEUER_KEY=YOUR_AWS_SQS_QUEUER_KEY\n+AWS_SQS_QUEUER_SECRET=YOUR_AWS_SQS_QUEUER_SECRET\n+```\n+\n+#### AWS S3 - Credenciales\n+```bash\n+AWS_S3_KEY=YOUR_S3_ACCESS_KEY\n+AWS_S3_SECRET=YOUR_S3_SECRET_KEY\n+```\n+\n+#### AFIP - Configuración de ambiente\n+```bash\n+AFIP_PRODUCTION=false  # true para producción, false para homologación\n+```\n+\n+#### Email - Configuración de errores\n+```bash\n+MAIL_SERVIDOR=<EMAIL>\n+MAIL_DESARROLLO=<EMAIL>\n+```\n+\n+#### Ambiente de aplicación\n+```bash\n+APP_ENV=dev  # Valores: dev, alfa, beta, prod y se auto completa según el deploy de serverless\n+```\n+\n+## Manejo de Ambientes\n+\n+### Configuración por Ambiente\n+\n+El sistema maneja 4 ambientes principales: dev, alfa, beta, prod\n+\n+El sistema determina el ambiente basándose en:\n+\n+1. **Variable de entorno `APP_ENV`** (prioritaria)\n+2. **Detección automática por stage de Serverless**\n+\n+### Enrutamiento de Mensajes\n+\n+La función `afipsdk_lambda()` en `funciones_aws.php` determina automáticamente la cola correcta basándose en la variable de sesión `$_SESSION['servidor_version']`:\n+\n+- `$_SESSION['servidor_version'] == 'ALFA'` → Cola ALFA\n+- `$_SESSION['servidor_version'] == 'BETA'` → Cola BETA\n+- `$_SESSION['servidor_version'] == ''` o no definida → Cola PROD\n+\n+### Configuración AFIP (Homologación vs Producción)\n+\n+La configuración de AFIP se determina por:\n+\n+1. **Variable `AFIP_PRODUCTION`**: Controla si se usa el ambiente de producción de AFIP. La idea es poder cambiarlo desde la variable de entorno de Lambda\n+2. **Ambiente de aplicación**: Por defecto, solo `prod` usa producción de AFIP\n+\n+### 1. Colas SQS por Ambiente\n+\n+- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-${stage}\n+\n+**Formato del mensaje**: idempresa|idventa (ambos deben ser números enteros positivos)\n+\n+## Configuración en AWS\n+\n+### 1. Configurar Variables de Entorno en Lambda\n+\n+Para cada función Lambda (dev, alfa, beta, prod), configurar las variables de entorno correspondientes en la consola de AWS:\n+\n+1. Ir a AWS Lambda → Funciones → [nombre-función]\n+2. Ir a Configuración → Variables de entorno\n+3. Agregar todas las variables listadas arriba\n+4. Ajustar los valores según el ambiente\n+\n+## Comandos de Deploy\n+\n+### Deploy por Ambiente\n+\n+```bash\n+# Navegar al directorio del proyecto\n+cd services/lambda/arca\n+\n+# Deploy en desarrollo\n+serverless deploy --stage=dev\n+\n+# Deploy en alfa\n+serverless deploy --stage=alfa\n+\n+# Deploy en beta\n+serverless deploy --stage=beta\n+\n+# Deploy en producción\n+serverless deploy --stage=prod\n+```\n+\n+### Deploy con Variables de Entorno\n+\n+```bash\n+# Deploy con variables específicas\n+serverless deploy --stage=prod \\\n+  --param=\"awsRegion=sa-east-1\" \\\n+  --param=\"afipProduction=true\"\n+```\n+\n+## Flujo de Procesamiento\n+\n+1. **Recepción del mensaje**:\n+   - La función Lambda recibe un mensaje de la cola SQS\n+   - Valida que el mensaje tenga el formato correcto (idempresa|idventa)\n+\n+2. **Obtención de datos**:\n+   - Conecta a la base de datos de la empresa\n+   - Obtiene los datos de la venta y sus detalles\n+\n+3. **Preparación de datos para AFIP**:\n+   - Formatea los datos según los requerimientos de la API de AFIP\n+   - Genera el JSON para la solicitud\n+\n+4. **Envío a AFIP**:\n+   - Envía los datos a la API de AFIP\n+   - Recibe la respuesta (CAE, resultado, etc.)\n+\n+5. **Actualización de la base de datos**:\n+   - Actualiza el estado de la factura con el resultado de AFIP\n+   - Guarda el CAE y otros datos relevantes\n+\n+### Pruebas locales\n+\n+Para probar la función localmente sin necesidad de enviar mensajes a la cola SQS:\n+\n+```bash\n+# Probar con un evento específico\n+serverless bref:local --function afipsdk --data '{\"Records\":[{\"body\":\"161|3567\"}]}'\n+\n+# O usando el script de ayuda\n+php ./test.php 161 3567\n+```\n+\n+### Pruebas en AWS\n+\n+Para probar la función en AWS:\n+\n+```bash\n+# Enviar un mensaje a la cola SQS\n+aws sqs send-message --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev --message-body \"161|3567\"\n+\n+# Ver los logs de la función\n+serverless logs -f afipsdk\n+\n+# Ver los logs en tiempo real\n+serverless logs -f afipsdk -t\n+\n+# Ver los logs con AWS CLI\n+aws logs filter-log-events --log-group-name /aws/lambda/arca-dev-afipsdk --limit 20\n+```\n+\n+### Gestión de la Cola SQS\n+\n+```bash\n+# Ver atributos de la cola\n+aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev --attribute-names All\n+\n+# Ver mensajes en la cola\n+aws sqs receive-message --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev --max-number-of-messages 10\n+\n+# Purgar la cola (eliminar todos los mensajes)\n+aws sqs purge-queue --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\n+```\n+\n+## Solución de Problemas\n+\n+### El Lambda no procesa los mensajes\n+\n+Verifica que la cola SQS tenga mensajes (ajusta la URL según el ambiente):\n+\n+```bash\n+# Para desarrollo\n+aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev --attribute-names ApproximateNumberOfMessages\n+\n+# Para alfa\n+aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa --attribute-names ApproximateNumberOfMessages\n+\n+# Para beta\n+aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta --attribute-names ApproximateNumberOfMessages\n+\n+# Para producción\n+aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod --attribute-names ApproximateNumberOfMessages\n+```\n+\n+Verifica que el Lambda tenga los permisos correctos:\n+\n+```bash\n+aws lambda get-policy --function-name arca-dev-afipsdk\n+```\n+\n+Verifica que el mapeo de eventos esté configurado correctamente:\n+\n+```bash\n+aws lambda list-event-source-mappings --function-name arca-dev-afipsdk\n+```\n+\n+### Errores en el Lambda\n+\n+- Revisa los logs para ver el error específico:\n+\n+```bash\n+serverless logs -f afipsdk --stage=dev\n+serverless logs -f afipsdk --stage=alfa\n+serverless logs -f afipsdk --stage=beta\n+serverless logs -f afipsdk --stage=prod\n+```\n+\n+## Gestión de Pausa por Empresa\n+\n+### Variable de Entorno\n+\n+Agregar a las variables de entorno de Lambda (opcional):\n+\n+```bash\n+IDEMPRESAS_PAUSADAS=   # Vacío = sin pausas (comportamiento por defecto)\n+```\n+\n+### Opciones de pausa\n+\n+#### Pausar todas las empresas\n+```bash\n+IDEMPRESAS_PAUSADAS=*\n+```\n+\n+#### Pausar empresas específicas\n+```bash\n+IDEMPRESAS_PAUSADAS=161,162,163\n+```\n+\n+#### Pausar una sola empresa\n+```bash\n+IDEMPRESAS_PAUSADAS=161\n+```\n+\n+### Comandos AWS CLI\n+\n+#### Pausar todas las empresas\n+```bash\n+aws lambda update-function-configuration \\\n+  --function-name arca-dev-afipsdk \\\n+  --environment Variables='{\"IDEMPRESAS_PAUSADAS\":\"*\",\"BD_HOST\":\"127.0.0.1\",...}'\n+```\n+\n+#### Pausar empresas específicas\n+```bash\n+aws lambda update-function-configuration \\\n+  --function-name arca-dev-afipsdk \\\n+  --environment Variables='{\"IDEMPRESAS_PAUSADAS\":\"161,162\",\"BD_HOST\":\"127.0.0.1\",...}'\n+```\n+\n+#### Quitar todas las pausas\n+```bash\n+aws lambda update-function-configuration \\\n+  --function-name arca-dev-afipsdk \\\n+  --environment Variables='{\"IDEMPRESAS_PAUSADAS\":\"\",\"BD_HOST\":\"127.0.0.1\",...}'\n+```\n+\n+### Comportamiento durante la pausa\n+\n+- **Mensajes pausados**: Permanecen en SQS y se procesan cuando se quite la pausa\n+- **Sin modificación en BD**: Las ventas no se tocan durante la pausa\n+- **Log visible**: Se muestra qué empresa fue pausada en los logs de Lambda\n\\ No newline at end of file\n"}], "date": 1748981472177, "name": "Commit-0", "content": "# AFIP SDK Lambda Function\n\nEsta función Lambda procesa mensajes de la cola SQS para generar facturas electrónicas usando la API de AFIPSDK.\n\n## Descripción General\n\nEl sistema utiliza AWS Lambda para procesar facturas electrónicas a través de la API de AFIPSDK. El flujo de trabajo es el siguiente:\n\n- La aplicación principal envía un mensaje a una cola SQS con el formato `idempresa|idventa`\n- La función Lambda afipsdk recibe el mensaje y procesa la factura\n- La función Lambda obtiene los datos de la factura de la base de datos\n- La función Lambda envía los datos a AFIP y recibe la respuesta\n- La función Lambda actualiza el estado de la factura en la base de datos\n\n## Arquitectura\n\n```\n┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐\n│  Aplicación │    │  Cola SQS   │    │   Lambda    │    │    AFIP     │\n│  Principal  │───►│ afipsdk-queue│───►│   afipsdk   │───►│    API     │\n└─────────────┘    └─────────────┘    └──────┬──────┘    └─────────────┘\n                                             │\n                                             ▼\n                                      ┌─────────────┐\n                                      │  Base de    │\n                                      │   Datos     │\n                                      └─────────────┘\n```\n\n## Componentes\n\n### 1. Cola SQS\n\n- **Nombre**: afipsdk-queue-{stage} (donde {stage} es dev, alfa, beta o prod)\n- **URL**: https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-{stage}\n- **Formato del mensaje**: idempresa|idventa (ambos deben ser números enteros positivos)\n\n### 2. Función Lambda\n\n- **Nombre**: arca-{stage}-afipsdk\n- **Descripción**: Procesador de facturas para AFIP\n- **Runtime**: PHP 8.3\n- **Código fuente**: services/lambda/arca/public/afipsdk.php\n- **Configuración**: services/lambda/arca/serverless.yml\n\n### 3. Base de Datos\n\n- Se utiliza la clase Database de services/lambda/api/lib/Database.php\n- Conecta a la base de datos de la empresa correspondiente\n- Obtiene los datos de la factura y actualiza su estado\n\n## Flujo de Procesamiento\n\n1. **Recepción del mensaje**:\n   - La función Lambda recibe un mensaje de la cola SQS\n   - Valida que el mensaje tenga el formato correcto (idempresa|idventa)\n\n2. **Obtención de datos**:\n   - Conecta a la base de datos de la empresa\n   - Obtiene los datos de la venta y sus detalles\n\n3. **Preparación de datos para AFIP**:\n   - Formatea los datos según los requerimientos de la API de AFIP\n   - Genera el JSON para la solicitud\n\n4. **Envío a AFIP**:\n   - Envía los datos a la API de AFIP\n   - Recibe la respuesta (CAE, resultado, etc.)\n\n5. **Actualización de la base de datos**:\n   - Actualiza el estado de la factura con el resultado de AFIP\n   - Guarda el CAE y otros datos relevantes\n\n## Comandos\n\n### Despliegue\n\n```bash\n# Navegar al directorio del proyecto\ncd services/lambda/arca\n\n# Desplegar en el entorno de desarrollo\nserverless deploy --stage=dev\n\n# Desplegar en otros entornos\nserverless deploy --stage=alfa\nserverless deploy --stage=beta\nserverless deploy --stage=prod\n```\n\n### Pruebas locales\n\nPara probar la función localmente sin necesidad de enviar mensajes a la cola SQS:\n\n```bash\n# Probar con un evento específico\nserverless bref:local --function afipsdk --data '{\"Records\":[{\"body\":\"161|3567\"}]}'\n\n# O usando el script de ayuda\n./test-local.sh 161 3567\n```\n\n### Pruebas en AWS\n\nPara probar la función en AWS:\n\n```bash\n# Enviar un mensaje a la cola SQS\naws sqs send-message --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev --message-body \"161|3567\"\n\n# Ver los logs de la función\nserverless logs -f afipsdk\n\n# Ver los logs en tiempo real\nserverless logs -f afipsdk -t\n\n# Ver los logs con AWS CLI\naws logs filter-log-events --log-group-name /aws/lambda/arca-dev-afipsdk --limit 20\n```\n\n### Gestión de la Cola SQS\n\n```bash\n# Ver atributos de la cola\naws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev --attribute-names All\n\n# Ver mensajes en la cola\naws sqs receive-message --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev --max-number-of-messages 10\n\n# Purgar la cola (eliminar todos los mensajes)\naws sqs purge-queue --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\n```\n\n## Código de Ejemplo\n\n### Enviar un mensaje desde PHP\n\n```php\nfunction afipsdk_lambda($idempresa, $idventa)\n{\n    try {\n        $sqs = conectar_aws('sqs');\n\n        $params = [\n            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE,\n            'DelaySeconds' => 10,\n            'MessageBody' => \"$idempresa|$idventa\",\n        ];\n\n        $sqs->sendMessage($params);\n\n    } catch (Exception $e) {\n        mostrar_error('No se envió un mensaje con SQS.<br>'\n            .'Error: '.$e->getMessage().'<br>'\n            .'Parametros: '.json_encode($params), true);\n        return false;\n    }\n    return true;\n}\n```\n\n## Solución de Problemas\n\n### El Lambda no procesa los mensajes\n\nVerifica que la cola SQS tenga mensajes:\n\n```bash\naws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev --attribute-names ApproximateNumberOfMessages\n```\n\nVerifica que el Lambda tenga los permisos correctos:\n\n```bash\naws lambda get-policy --function-name arca-dev-afipsdk\n```\n\nVerifica que el mapeo de eventos esté configurado correctamente:\n\n```bash\naws lambda list-event-source-mappings --function-name arca-dev-afipsdk\n```\n\n### Errores en el Lambda\n\n- Revisa los logs para ver el error específico:\n\n```bash\nserverless logs -f afipsdk\n```\n\n- Verifica que la conexión a la base de datos funcione correctamente\n- Verifica que los datos de la factura sean correctos\n- Verifica que la conexión con AFIP funcione correctamente\n\n### Problemas de conexión a la base de datos\n\nSi ves errores como \"Connection refused\" o \"Call to a member function prepare() on null\", verifica:\n\n1. Que la función Lambda esté configurada para ejecutarse dentro de la VPC correcta\n2. Que los grupos de seguridad permitan la conexión desde la Lambda a la base de datos\n3. Que las credenciales de la base de datos sean correctas\n\n## Próximos Pasos\n\n- Implementar la integración real con la API de AFIP usando la librería afipsdk/afip.php\n- Agregar manejo de errores más robusto\n- Implementar reintentos para casos de fallo\n- Agregar notificaciones para errores críticos\n- Implementar monitoreo y alertas\n"}]}