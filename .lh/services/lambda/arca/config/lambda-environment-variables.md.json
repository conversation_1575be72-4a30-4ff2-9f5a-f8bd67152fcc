{"sourceFile": "services/lambda/arca/config/lambda-environment-variables.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750457293655, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750457293655, "name": "Commit-0", "content": "# Variables Críticas para AWS Lambda Environment Variables\n\n## ⚠️  VARIABLES QUE DEBES CONFIGURAR EN AWS LAMBDA\n\nLas siguientes variables **SÍ O SÍ** deben configurarse en AWS Lambda Environment Variables para los ambientes de producción (beta y prod):\n\n### 🔴 Variables Críticas Obligatorias\n\n```bash\n# Ambiente\nAPP_ENV=prod  # o beta, alfa según corresponda\n\n# Base de datos\nBD_HOST=tu-host-de-bd\nBD_USER=tu-usuario-bd  \nBD_PASS=tu-password-bd\nBD_BD=saasargentina\nBD_PORT=3306\n\n# AWS S3 - Credenciales\nAWS_S3_KEY=tu-access-key-real\nAWS_S3_SECRET=tu-secret-key-real\n\n# AWS SQS - Credenciales  \nAWS_SQS_QUEUER_KEY=tu-sqs-access-key\nAWS_SQS_QUEUER_SECRET=tu-sqs-secret-key\n\n# AFIP - Ambiente\nAFIP_PRODUCTION=true  # Solo en PROD, false en beta/alfa\n```\n\n### 🟡 Variables Opcionales (pueden usar fallback desde .env)\n\nEstas variables pueden quedarse en el archivo `.env` como fallback, pero es recomendable configurarlas también en Lambda para mayor control:\n\n```bash\n# AWS General\nAWS_REGION=sa-east-1\n\n# URLs SQS (raramente cambian)\nAWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-prod\nAWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue\n\n# Email\nMAIL_SERVIDOR=<EMAIL>\nMAIL_DESARROLLO=<EMAIL>\n```\n\n## 🔧 Cómo Configurar en AWS Lambda\n\n### Opción 1: Consola AWS\n\n1. Ve a AWS Lambda → Tu función (ej: `arca-prod-afipsdk`)\n2. Configuración → Variables de entorno\n3. Agregar cada variable crítica\n4. Guardar\n\n### Opción 2: AWS CLI\n\n```bash\n# Configurar variables críticas\naws lambda update-function-configuration \\\n  --function-name arca-prod-afipsdk \\\n  --environment Variables='{\n    \"APP_ENV\":\"prod\",\n    \"BD_HOST\":\"tu-host\",\n    \"BD_USER\":\"tu-user\",\n    \"BD_PASS\":\"tu-pass\",\n    \"AWS_S3_KEY\":\"tu-key\",\n    \"AWS_S3_SECRET\":\"tu-secret\",\n    \"AWS_SQS_QUEUER_KEY\":\"tu-sqs-key\",\n    \"AWS_SQS_QUEUER_SECRET\":\"tu-sqs-secret\",\n    \"AFIP_PRODUCTION\":\"true\"\n  }'\n```\n\n### Opción 3: Serverless Framework\n\nEn `serverless.yml`:\n\n```yaml\nprovider:\n  environment:\n    APP_ENV: ${opt:stage}\n    BD_HOST: ${env:BD_HOST_PROD}\n    BD_USER: ${env:BD_USER_PROD}\n    BD_PASS: ${env:BD_PASS_PROD}\n    AWS_S3_KEY: ${env:AWS_S3_KEY}\n    AWS_S3_SECRET: ${env:AWS_S3_SECRET}\n    AWS_SQS_QUEUER_KEY: ${env:AWS_SQS_QUEUER_KEY}\n    AWS_SQS_QUEUER_SECRET: ${env:AWS_SQS_QUEUER_SECRET}\n    AFIP_PRODUCTION: true\n```\n\n## ✅ Verificación\n\nPara verificar que la configuración esté correcta:\n\n```bash\n# Ver variables configuradas\naws lambda get-function-configuration \\\n  --function-name arca-prod-afipsdk \\\n  --query 'Environment.Variables'\n\n# Probar función con variables\nserverless invoke --function afipsdk --data '{\"Records\":[{\"body\":\"161|3567\"}]}'\n```\n\n## 🚨 Qué NO hacer\n\n❌ **NO** dejes credenciales reales hardcodeadas en `.env` que se suba a Git\n❌ **NO** confíes solo en el archivo `.env` para producción\n❌ **NO** olvides configurar `AFIP_PRODUCTION=true` en prod\n\n## 🎯 Resumen\n\n- **Variables críticas** (credenciales, BD, AFIP): **OBLIGATORIO en Lambda Environment Variables**\n- **Variables de configuración** (URLs, emails): **Opcional, pueden usar fallback desde .env**\n- **Desarrollo local**: Usa `.env` sin problemas\n- **Producción**: Variables críticas en AWS Lambda + .env como fallback para lo demás\n"}]}