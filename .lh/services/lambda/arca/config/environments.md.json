{"sourceFile": "services/lambda/arca/config/environments.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": *************, "name": "Commit-0", "content": "# Configuración de Variables de Entorno por Ambiente\n\nEste documento detalla las configuraciones específicas de variables de entorno para cada ambiente del Lambda AFIP SDK.\n\n## Desarrollo (dev)\n\n```bash\n# Aplicación\nAPP_ENV=dev\n\n# AFIP\nAFIP_PRODUCTION=false\n\n# Base de datos\nBD_HOST=127.0.0.1\nBD_USER=yosoyroot\nBD_PASS=8des4rollo\nBD_BD=saasargentina\nBD_PORT=3306\n\n# AWS\nAWS_REGION=sa-east-1\nAWS_ACCOUNT_ID=************\n\n# SQS\nAWS_SQS_AFIPSDK_QUEUE_DEV=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\nAWS_SQS_AFIPSDK_QUEUE_ALFA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\nAWS_SQS_AFIPSDK_QUEUE_BETA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\nAWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\nAWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\nAWS_SQS_QUEUER_KEY=********************\nAWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n\n# S3\nAWS_S3_WSFE_BUCKET_DEV=saasargentina-wsfe-dev\nAWS_S3_WSFE_BUCKET_ALFA=saasargentina-wsfe-alfa\nAWS_S3_WSFE_BUCKET_BETA=saasargentina-wsfe-beta\nAWS_S3_WSFE_BUCKET_PROD=saasargentina-wsfe\nAWS_S3_KEY=YOUR_S3_ACCESS_KEY\nAWS_S3_SECRET=YOUR_S3_SECRET_KEY\n\n# Email\nMAIL_SERVIDOR=<EMAIL>\nMAIL_DESARROLLO=<EMAIL>\n```\n\n## Alfa (alfa)\n\n```bash\n# Aplicación\nAPP_ENV=alfa\n\n# AFIP\nAFIP_PRODUCTION=false\n\n# Base de datos - usar credenciales de alfa\nBD_HOST=alfa-db-host\nBD_USER=alfa_user\nBD_PASS=alfa_password\nBD_BD=saasargentina\nBD_PORT=3306\n\n# AWS (mismo que dev)\nAWS_REGION=sa-east-1\nAWS_ACCOUNT_ID=************\n\n# SQS (mismo que dev)\nAWS_SQS_AFIPSDK_QUEUE_DEV=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\nAWS_SQS_AFIPSDK_QUEUE_ALFA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\nAWS_SQS_AFIPSDK_QUEUE_BETA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\nAWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\nAWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\nAWS_SQS_QUEUER_KEY=********************\nAWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n\n# S3 (mismo que dev)\nAWS_S3_WSFE_BUCKET_DEV=saasargentina-wsfe-dev\nAWS_S3_WSFE_BUCKET_ALFA=saasargentina-wsfe-alfa\nAWS_S3_WSFE_BUCKET_BETA=saasargentina-wsfe-beta\nAWS_S3_WSFE_BUCKET_PROD=saasargentina-wsfe\nAWS_S3_KEY=YOUR_S3_ACCESS_KEY\nAWS_S3_SECRET=YOUR_S3_SECRET_KEY\n\n# Email (mismo que dev)\nMAIL_SERVIDOR=<EMAIL>\nMAIL_DESARROLLO=<EMAIL>\n```\n\n## Beta (beta)\n\n```bash\n# Aplicación\nAPP_ENV=beta\n\n# AFIP\nAFIP_PRODUCTION=false\n\n# Base de datos - usar credenciales de beta\nBD_HOST=beta-db-host\nBD_USER=beta_user\nBD_PASS=beta_password\nBD_BD=saasargentina\nBD_PORT=3306\n\n# AWS (mismo que dev)\nAWS_REGION=sa-east-1\nAWS_ACCOUNT_ID=************\n\n# SQS (mismo que dev)\nAWS_SQS_AFIPSDK_QUEUE_DEV=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\nAWS_SQS_AFIPSDK_QUEUE_ALFA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\nAWS_SQS_AFIPSDK_QUEUE_BETA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\nAWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\nAWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\nAWS_SQS_QUEUER_KEY=********************\nAWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n\n# S3 (mismo que dev)\nAWS_S3_WSFE_BUCKET_DEV=saasargentina-wsfe-dev\nAWS_S3_WSFE_BUCKET_ALFA=saasargentina-wsfe-alfa\nAWS_S3_WSFE_BUCKET_BETA=saasargentina-wsfe-beta\nAWS_S3_WSFE_BUCKET_PROD=saasargentina-wsfe\nAWS_S3_KEY=YOUR_S3_ACCESS_KEY\nAWS_S3_SECRET=YOUR_S3_SECRET_KEY\n\n# Email (mismo que dev)\nMAIL_SERVIDOR=<EMAIL>\nMAIL_DESARROLLO=<EMAIL>\n```\n\n## Producción (prod)\n\n```bash\n# Aplicación\nAPP_ENV=prod\n\n# AFIP - ¡IMPORTANTE! En producción usar el ambiente real de AFIP\nAFIP_PRODUCTION=true\n\n# Base de datos - usar credenciales de producción\nBD_HOST=prod-db-host\nBD_USER=prod_user\nBD_PASS=prod_password\nBD_BD=saasargentina\nBD_PORT=3306\n\n# AWS (mismo que dev)\nAWS_REGION=sa-east-1\nAWS_ACCOUNT_ID=************\n\n# SQS (mismo que dev)\nAWS_SQS_AFIPSDK_QUEUE_DEV=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev\nAWS_SQS_AFIPSDK_QUEUE_ALFA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-alfa\nAWS_SQS_AFIPSDK_QUEUE_BETA=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-beta\nAWS_SQS_AFIPSDK_QUEUE_PROD=https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-prod\nAWS_SQS_EMAIL_QUEUE=https://sqs.sa-east-1.amazonaws.com/************/email-queue\nAWS_SQS_QUEUER_KEY=********************\nAWS_SQS_QUEUER_SECRET=MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO\n\n# S3 (mismo que dev)\nAWS_S3_WSFE_BUCKET_DEV=saasargentina-wsfe-dev\nAWS_S3_WSFE_BUCKET_ALFA=saasargentina-wsfe-alfa\nAWS_S3_WSFE_BUCKET_BETA=saasargentina-wsfe-beta\nAWS_S3_WSFE_BUCKET_PROD=saasargentina-wsfe\nAWS_S3_KEY=YOUR_S3_ACCESS_KEY\nAWS_S3_SECRET=YOUR_S3_SECRET_KEY\n\n# Email\nMAIL_SERVIDOR=<EMAIL>\nMAIL_DESARROLLO=<EMAIL>\n```\n\n## Notas Importantes\n\n1. **Credenciales de Base de Datos**: Cada ambiente debe tener sus propias credenciales de BD\n2. **AFIP_PRODUCTION**: Solo debe ser `true` en el ambiente de producción\n3. **Buckets S3**: Cada ambiente usa su propio bucket para aislar los certificados\n4. **Colas SQS**: Aunque todas las colas están definidas en todas las configuraciones, cada ambiente enviará solo a su cola correspondiente\n5. **Credenciales AWS**: Se utilizan las mismas credenciales para SQS y S3 en todos los ambientes\n\n## Cómo Configurar en AWS Lambda\n\n1. Ir a la consola de AWS Lambda\n2. Seleccionar la función correspondiente (arca-{stage}-afipsdk)\n3. Ir a \"Configuración\" → \"Variables de entorno\"\n4. Agregar cada variable con su valor correspondiente\n5. Guardar los cambios\n\n## Verificación de Configuración\n\nPara verificar que las variables estén correctamente configuradas:\n\n```bash\n# Ver configuración actual de la función\naws lambda get-function-configuration --function-name arca-dev-afipsdk --query 'Environment.Variables'\n\n# Probar la función con un mensaje de prueba\naws sqs send-message --queue-url https://sqs.sa-east-1.amazonaws.com/************/afipsdk-queue-dev --message-body \"161|3567\"\n```\n"}]}