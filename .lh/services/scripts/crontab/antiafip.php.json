{"sourceFile": "services/scripts/crontab/antiafip.php", "activeCommit": 0, "commits": [{"activePatchIndex": 10, "patches": [{"date": 1724844320787, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1724844493916, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -180,13 +180,13 @@\n                     .'&idempresa='.$idempresa\n                     .'&idtipoventa='.$venta['idtipoventa']\n                     .'&numero_desde='.($venta['numero'] - 5)\n                     .'&numero_hasta='.($venta['numero'] + 10)\n-                    .'\">Probar con el recuperador de cae</a>\"'\n+                    .'\">Probar con el recuperador de cae</a>'\n                     .' - <a href=\"https://scripts.saasargentina.com/numeracion.php?a=anular'\n                     .'&idempresa='.$idempresa\n                     .'&idventa='.$venta['idventa']\n-                    .'\">Anular</a>\"'\n+                    .'\">Anular</a>'\n                     .PHP_EOL;\n             }\n \n             $i++;\n"}, {"date": 1732733101810, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,7 @@\n <?php\n /*\n-  Script para conseguir los CAEs cuando AFIP se cae y genera inconsistencias\n+  Script para conseguir los CAEs cuando ARCA se cae y genera inconsistencias\n */\n // ini_set('display_errors', '1');\n \n require __DIR__.'/../../acc/acc.php';\n"}, {"date": 1734130946341, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -164,8 +164,17 @@\n                 $resultado = 'SINCAE';\n                 continue;\n             }\n \n+            // Reviso que esté ok el tipodoc\n+            if (strlen($venta['cuit']) == 11 && $venta['tipodoc'] != 80) {\n+                $venta['tipodoc'] = 80;\n+                guardar_sql('ventas', ['tipodoc' => 80], $venta['idventa']);\n+            } else if (!$venta['dni']) {\n+                $venta['tipodoc'] = 99;\n+                guardar_sql('ventas', ['tipodoc' => 99], $venta['idventa']);\n+            }\n+\n             comprobantes_recalculando($venta['idventa']);\n             $resultado = rece1($venta['idventa'], $antiafip, $guardar_salida);\n \n             $reporte.= PHP_EOL.\"<br>\".$i.\" - Resultado de \".$venta['nombre'].\" \"\n"}, {"date": 1734473133071, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -168,9 +168,9 @@\n             // Reviso que esté ok el tipodoc\n             if (strlen($venta['cuit']) == 11 && $venta['tipodoc'] != 80) {\n                 $venta['tipodoc'] = 80;\n                 guardar_sql('ventas', ['tipodoc' => 80], $venta['idventa']);\n-            } else if (!$venta['dni']) {\n+            } else if (!$venta['cuit'] && !$venta['dni'] && $venta['tipodoc'] != 99) {\n                 $venta['tipodoc'] = 99;\n                 guardar_sql('ventas', ['tipodoc' => 99], $venta['idventa']);\n             }\n \n"}, {"date": 1747234027669, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -168,11 +168,14 @@\n             // Reviso que esté ok el tipodoc\n             if (strlen($venta['cuit']) == 11 && $venta['tipodoc'] != 80) {\n                 $venta['tipodoc'] = 80;\n                 guardar_sql('ventas', ['tipodoc' => 80], $venta['idventa']);\n-            } else if (!$venta['cuit'] && !$venta['dni'] && $venta['tipodoc'] != 99) {\n+            } else if (!$venta['dni'] && $venta['tipodoc'] != 99) {\n                 $venta['tipodoc'] = 99;\n                 guardar_sql('ventas', ['tipodoc' => 99], $venta['idventa']);\n+            } else if (strlen($venta['dni']) >= 5 && (!$venta['tipodoc'] || $venta['tipodoc'] == 99)) {\n+                $venta['tipodoc'] = 96;\n+                guardar_sql('ventas', ['tipodoc' => 96], $venta['idventa']);\n             }\n \n             comprobantes_recalculando($venta['idventa']);\n             $resultado = rece1($venta['idventa'], $antiafip, $guardar_salida);\n"}, {"date": 1747235792417, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -168,9 +168,9 @@\n             // Reviso que esté ok el tipodoc\n             if (strlen($venta['cuit']) == 11 && $venta['tipodoc'] != 80) {\n                 $venta['tipodoc'] = 80;\n                 guardar_sql('ventas', ['tipodoc' => 80], $venta['idventa']);\n-            } else if (!$venta['dni'] && $venta['tipodoc'] != 99) {\n+            } else if (!$venta['cuit'] && !$venta['dni'] && $venta['tipodoc'] != 99) {\n                 $venta['tipodoc'] = 99;\n                 guardar_sql('ventas', ['tipodoc' => 99], $venta['idventa']);\n             } else if (strlen($venta['dni']) >= 5 && (!$venta['tipodoc'] || $venta['tipodoc'] == 99)) {\n                 $venta['tipodoc'] = 96;\n"}, {"date": 1747236208531, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -171,9 +171,9 @@\n                 guardar_sql('ventas', ['tipodoc' => 80], $venta['idventa']);\n             } else if (!$venta['cuit'] && !$venta['dni'] && $venta['tipodoc'] != 99) {\n                 $venta['tipodoc'] = 99;\n                 guardar_sql('ventas', ['tipodoc' => 99], $venta['idventa']);\n-            } else if (strlen($venta['dni']) >= 5 && (!$venta['tipodoc'] || $venta['tipodoc'] == 99)) {\n+            } else if (!$venta['cuit'] && strlen($venta['dni']) >= 5 && (!$venta['tipodoc'] || $venta['tipodoc'] == 99)) {\n                 $venta['tipodoc'] = 96;\n                 guardar_sql('ventas', ['tipodoc' => 96], $venta['idventa']);\n             }\n \n"}, {"date": 1748431129641, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -156,9 +156,9 @@\n                 continue;\n             }\n \n             // Desactivo la valido que la factura se pueda aprobar\n-            if (false && !rece1_validar($venta['idventa'])) {\n+            if (!rece1_validar($venta['idventa'])) {\n                 $reporte.= PHP_EOL.\"<br>\".$i.\" - Venta con estadocae pendiente pero no se puede aprobar \".$venta['nombre'].\" \"\n                     .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])\n                     .\" (idtipoventa \".$venta['idtipoventa'].\" | idventa \".$venta['idventa'].\")\";\n                 $resultado = 'SINCAE';\n"}, {"date": 1749652885205, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -156,9 +156,9 @@\n                 continue;\n             }\n \n             // Desactivo la valido que la factura se pueda aprobar\n-            if (!rece1_validar($venta['idventa'])) {\n+            if (false && !rece1_validar($venta['idventa'])) {\n                 $reporte.= PHP_EOL.\"<br>\".$i.\" - Venta con estadocae pendiente pero no se puede aprobar \".$venta['nombre'].\" \"\n                     .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])\n                     .\" (idtipoventa \".$venta['idtipoventa'].\" | idventa \".$venta['idventa'].\")\";\n                 $resultado = 'SINCAE';\n"}, {"date": 1750186257420, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -156,9 +156,9 @@\n                 continue;\n             }\n \n             // Desactivo la valido que la factura se pueda aprobar\n-            if (false && !rece1_validar($venta['idventa'])) {\n+            if (false && !rece1_validar($venta['idventa'])) { // EN ALFA CAMBIA A validar_fe\n                 $reporte.= PHP_EOL.\"<br>\".$i.\" - Venta con estadocae pendiente pero no se puede aprobar \".$venta['nombre'].\" \"\n                     .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])\n                     .\" (idtipoventa \".$venta['idtipoventa'].\" | idventa \".$venta['idventa'].\")\";\n                 $resultado = 'SINCAE';\n"}], "date": 1724844320787, "name": "Commit-0", "content": "<?php\n/*\n  Script para conseguir los CAEs cuando AFIP se cae y genera inconsistencias\n*/\n// ini_set('display_errors', '1');\n\nrequire __DIR__.'/../../acc/acc.php';\nrequire PATH_BETA.'public/vendor/autoload.php';\n\n$idempresas = in_array('idempresas', $argv)\n    ? $argv[array_search('idempresas', $argv) + 1]\n    : false;\n$antiafip = true;\n$guardar_salida = in_array('salida', $argv);\n\nif ($idempresas) {\n    $empresas = explode(',', $idempresas);\n\n} else if (!is_readable(PATH_LOGS . 'sincae.csv')) {\n    // Si no existe el archivo, termino el script\n    exit('No existe o no es legible el archivo sincae.csv');\n\n} else {\n    $filas = file(PATH_LOGS . 'sincae.csv');\n    $empresas = array_unique($filas);\n    sort($empresas);\n}\n\n// Reseteo la sesión\nsession_unset();\nsession_destroy();\nsession_start();\n\n//Especifico el nombre del módulo en plural y singular para ser utilizado en varias partes de inclusiones\n$modulo = 'ventas';\n$modulo_singular = 'venta';\n\n// Empiezo el texto que voy a mandar por mail\n$reporte = \"<br /><br />\\r\\n\\r\\n<u><b>Informe del Antiafip</b></u><br>Iniciado a las \".date(\"d-m-Y H:i:s\").'<br>';\n\ntry {\n\n    $bd_saasargentina = mysqli_connect(BD_HOST, BD_USER, BD_PASS, BD_BD)\n    or mostrar_error('Se produjo un error conectandose a la base de datos de saasargentina.com en el antiafip.php');\n    mysqli_set_charset($bd_saasargentina, \"utf8mb4\");\n\n    // Busco las empresas que tienen caes pendientes y las recorro\n    $todas_ok = true; // Para controlar que todas terminen ok antes de borrar el log\n    $idservidor = 0;\n    $cantidades = ['empresas' => 0, 'ventas' => 0,\n        'empresas_resueltas' => 0, 'ventas_resueltas' => 0];\n    $empresas_resueltas_antes = array();\n    foreach ($empresas as $idempresa) {\n\n        $idempresa = (int)trim($idempresa);\n\n        if ($idempresa <= 0) {\n            $todas_ok = false; // Si alguna empresa no es númerica, algo anda mal\n            continue;\n        }\n\n        // Busco los datos de conexión, me hago pasar como que está logueada y conecto a su bd\n        $sql = \"SELECT empresas.idempresa, empresas.nombre AS empresa, empresas.idservidor,\n                servidores.BD_HOST, servidores.BD_USER, servidores.BD_PASS, servidores.BD_BD\n            FROM empresas\n                INNER JOIN servidores ON empresas.idservidor=servidores.idservidor\n            WHERE idempresa = '$idempresa' LIMIT 1\";\n        $datos_servidor = mysqli_fetch_assoc(mysqli_query($bd_saasargentina, $sql));\n\n        if (!$idservidor) {\n            $idservidor = $datos_servidor['idservidor'];\n\n            if ($idservidor == 7) {\n                require_once PATH_SAAS.'public/librerias/funciones_modelo.php';\n                require_once PATH_SAAS.'public/librerias/funciones_wsfe.php';\n                require_once PATH_SAAS.'public/librerias/funciones_comprobantes.php';\n                require_once PATH_SAAS.'public/librerias/funciones_aws.php';\n            } else {\n                require_once PATH_BETA.'public/librerias/funciones_modelo.php';\n                require_once PATH_BETA.'public/librerias/funciones_wsfe.php';\n                require_once PATH_BETA.'public/librerias/funciones_comprobantes.php';\n                require_once PATH_BETA.'public/librerias/funciones_aws.php';\n            }\n        }\n\n        // Sólo puedo procesar uno de beta y prod por separado cuando las librerías son incompatibles\n        if ($idservidor != $datos_servidor['idservidor'])\n            continue;\n\n        $_SESSION['logsueado'] = true;\n        $_SESSION['empresa_idempresa'] = $idempresa;\n        $_SESSION['servidor_host'] = $datos_servidor['BD_HOST'];\n        $_SESSION['servidor_user'] = $datos_servidor['BD_USER'];\n        $_SESSION['servidor_pass'] = $datos_servidor['BD_PASS'];\n        $_SESSION['servidor_bd'] = $datos_servidor['BD_BD'];\n\n        $bd_link = conectar_db();\n\n        $_SESSION['configuracion_cuit'] = campo_sql(consulta_sql(\n            \"SELECT cuit FROM configuraciones LIMIT 1\"));\n        $_SESSION['configuracion_discrimina'] = campo_sql(consulta_sql(\n            \"SELECT discrimina FROM tablas_condiciones WHERE idtipoiva =\n                (SELECT idtipoiva FROM configuraciones LIMIT 1) LIMIT 1\"));\n\n        $resultado_sql = consulta_sql(\n        \"SELECT ventas.*,\n            cv.ultimonumero, cv.letra, cv.nombre, cv.puntodeventa, cv.tipofacturacion\n        FROM ventas\n            LEFT JOIN categorias_ventas AS cv ON ventas.idtipoventa = cv.idtipoventa\n        WHERE estadocae = 'pendiente'\n            AND ventas.estado = 'cerrado'\n        ORDER BY idtipoventa, numero, idventa\");\n\n        $cantidad_ventas = contar_sql($resultado_sql);\n        if (!$cantidad_ventas) {\n            $empresas_resueltas_antes[] = $idempresa;\n            continue;\n        }\n\n        $reporte.= \"<br><hr><br>\".PHP_EOL;\n        $reporte.= \"Ventas para cerrar de $idempresa (CUIT {$_SESSION['configuracion_cuit']}): $cantidad_ventas<br>\".PHP_EOL;\n        $cantidades['empresas']++;\n        $cantidades['ventas']+= $cantidad_ventas;\n        $i = 1;\n        $resultado = 'OK'; // Si una venta de esta empresa falla, sigo con la próxima\n\n        while ($resultado == 'OK'\n            && ($venta = array_sql($resultado_sql))) {\n\n            $_SESSION['usuario_idusuario'] = $venta['idusuario'];\n\n            // Controlo que sea electrónica\n            if ($venta['tipofacturacion'] != 'electronico') {\n                $reporte.= PHP_EOL.\"<br>\".$i.\" - Venta con estadocae pendiente pero no electrónica PARA REVISAR \".$venta['nombre'].\" \"\n                .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])\n                .\" (idtipoventa \".$venta['idtipoventa'].\" | idventa \".$venta['idventa'].\")\";\n                unset($venta['observacion']);\n                mostrar_error('En ANTIAFIP hay una venta con estadocae pendiente pero no electrónica PARA REVISAR<br>'\n                    .'tipofacturacion: '.$venta['tipofacturacion'].'<br>'\n                    .'estado: '.$venta['estado'].'<br>'\n                    .'cae: '.$venta['cae'].'<br>'\n                    .'idempresa: '.$idempresa.':<br>'\n                    .'venta: '.json_encode($venta), true);\n                continue;\n            }\n\n            // Controlo si el CAE ya está ok\n            if (mb_strlen($venta['cae']) == 14) {\n                $cae_correcto = rece1_verificar($venta['idventa']);\n                if ($cae_correcto)\n                    guardar_sql('ventas', ['estadocae' => 'aprobado'], $venta['idventa']);\n\n                $reporte.= PHP_EOL.\"<br>\".$i.\" - Venta a APROBADO \".$venta['nombre'].\" \"\n                    .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])\n                    .\" (idtipoventa \".$venta['idtipoventa'].\" | idventa \".$venta['idventa'].\")\";\n                continue;\n            }\n\n            // Desactivo la valido que la factura se pueda aprobar\n            if (false && !rece1_validar($venta['idventa'])) {\n                $reporte.= PHP_EOL.\"<br>\".$i.\" - Venta con estadocae pendiente pero no se puede aprobar \".$venta['nombre'].\" \"\n                    .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])\n                    .\" (idtipoventa \".$venta['idtipoventa'].\" | idventa \".$venta['idventa'].\")\";\n                $resultado = 'SINCAE';\n                continue;\n            }\n\n            comprobantes_recalculando($venta['idventa']);\n            $resultado = rece1($venta['idventa'], $antiafip, $guardar_salida);\n\n            $reporte.= PHP_EOL.\"<br>\".$i.\" - Resultado de \".$venta['nombre'].\" \"\n                .numero_comprobante($venta['letra'], $venta['puntodeventa'], $venta['numero'])\n                .\" (idtipoventa \".$venta['idtipoventa'].\" | idventa \".$venta['idventa'].\"): \" . $resultado;\n\n            if ($resultado == 'OK') {\n                $cantidades['ventas_resueltas']++;\n            } else {\n                $todas_ok = false; // Marco que alguna empresa falló\n                $reporte.= '<a href=\"https://scripts.saasargentina.com/numeracion.php?a=cae'\n                    .'&idempresa='.$idempresa\n                    .'&idtipoventa='.$venta['idtipoventa']\n                    .'&numero_desde='.($venta['numero'] - 5)\n                    .'&numero_hasta='.($venta['numero'] + 10)\n                    .'\">Probar con el recuperador de cae</a>\"'\n                    .' - <a href=\"https://scripts.saasargentina.com/numeracion.php?a=anular'\n                    .'&idempresa='.$idempresa\n                    .'&idventa='.$venta['idventa']\n                    .'\">Anular</a>\"'\n                    .PHP_EOL;\n            }\n\n            $i++;\n            sleep(1);\n\n        }\n\n        if ($resultado == 'OK')\n            $cantidades['empresas_resueltas']++;\n\n        mysqli_close($bd_link);\n\n    }\n\n    if ($todas_ok && !$idempresas) {\n        // Limpio el log de empresas que tienen caes pendientes si no se llamó a una particular\n        unlink(PATH_LOGS . 'sincae.csv');\n        $reporte.= '<hr><br>Limpio log';\n\n    } else {\n        // Termino texto y envío mail\n        $reporte.= \"<hr><br>Terminado a las \".date(\"d-m-Y H:i:s\").'<br><br>'.PHP_EOL\n            .'Cantidad de empresas totales: '.$cantidades['empresas'].'<br>'.PHP_EOL\n            .'Cantidad de empresas resueltas: '.$cantidades['empresas_resueltas'].'<br>'.PHP_EOL\n            .'Cantidad de empresas pendientes: '.($cantidades['empresas'] - $cantidades['empresas_resueltas']).'<br><br>'.PHP_EOL\n            .'Cantidad de ventas totales: '.$cantidades['ventas'].'<br>'.PHP_EOL\n            .'Cantidad de ventas resueltas: '.$cantidades['ventas_resueltas'].'<br>'.PHP_EOL\n            .'Cantidad de ventas pendientes: '.($cantidades['ventas'] - $cantidades['ventas_resueltas']).'<br><br>'.PHP_EOL\n            .'Cantidad de empresas resueltas antes: '.count($empresas_resueltas_antes).'<br>'.PHP_EOL\n            .'Empresas resueltas antes: '.implode(', ', $empresas_resueltas_antes);\n\n        if (ESTADO != 'desarrollo') {\n            email_queue(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ANTIAFIP', $reporte, false);\n        }\n    }\n\n} catch(Exception $e) {\n    email_queue(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ERROR en ANTIAFIP', $reporte, false);\n\n}\n\n// Destruyo la sesión\nsession_unset();\nsession_destroy();\n\nexit($reporte);\n"}]}