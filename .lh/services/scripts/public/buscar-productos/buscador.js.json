{"sourceFile": "services/scripts/public/buscar-productos/buscador.js", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1749280008123, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1749280042640, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -25,9 +25,9 @@\n         var empresa = empresas[j].charAt(0).toUpperCase() + empresas[j].slice(1);\n         var url = 'https://api.saasargentina.com/v1/productos/'\n             + '?iue=' + iue\n             + '&busqueda=' + buscado\n-            + '&cantidad=' + 50;\n+            + '&cantidad=' + cantidad;\n \n         $.getJSON(url, null, function(data) {\n             if (data.resultados.length) {\n                 $.each(data.resultados, function(i, item) {\n"}, {"date": 1749280172925, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,9 @@\n function enter_buscar(e) {\n-    if (e.keyCode == 13)\n+    if (e.keyCode == 13) {\n         buscar();\n+        $(\"#buscado\").val(\"\");\n+    }\n }\n \n function buscar() {\n     var table = $('table');\n"}, {"date": 1749280643960, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,11 @@\n function enter_buscar(e) {\n     if (e.keyCode == 13) {\n         buscar();\n-        $(\"#buscado\").val(\"\");\n+        // Vaciar el campo de búsqueda solo si la opción \"Terminal de precios\" está marcada\n+        if ($(\"#terminalPrecios\").is(\":checked\")) {\n+            $(\"#buscado\").val(\"\");\n+        }\n     }\n }\n \n function buscar() {\n"}], "date": 1749280008123, "name": "Commit-0", "content": "function enter_buscar(e) {\n    if (e.keyCode == 13)\n        buscar();\n}\n\nfunction buscar() {\n    var table = $('table');\n    var buscado = $(\"#buscado\").val();\n\n    var cargando = '<tr><td style=\"text-align: center;\"><img src=\"https://app.saasargentina.com/estilos/estilo_1/images/cargando.gif\"></td></tr>';\n    table.html(cargando);\n\n    $.each(iues, function(j, iue) {\n\n        var thead = '<tr>'\n            + '<th>Empresa</th>'\n            + '<th>Código</th>'\n            + '<th>Nombre</th>'\n            + '<th>Precio</th>'\n            + '<th>Precio Final</th>'\n            + '<th>Stock</th>'\n            + '</tr>';\n        table.html(thead);\n\n        var empresa = empresas[j].charAt(0).toUpperCase() + empresas[j].slice(1);\n        var url = 'https://api.saasargentina.com/v1/productos/'\n            + '?iue=' + iue\n            + '&busqueda=' + buscado\n            + '&cantidad=' + 50;\n\n        $.getJSON(url, null, function(data) {\n            if (data.resultados.length) {\n                $.each(data.resultados, function(i, item) {\n                    var tr = '<tr>'\n                        + '<td>' + empresa + '</td>'\n                        + '<td>' + item.codigo + '</td>'\n                        + '<td>' + item.nombre + '</td>'\n                        + '<td>$ ' + item.precio + '</td>'\n                        + '<td>$ ' + item.preciofinal + '</td>'\n                        + '<td>' + item.stockactual + '</td>'\n                        + '</tr>';\n                    table.append(tr);\n                });\n\n            } else {\n                var tr = '<tr><td style=\"text-align: center;\" colspan=\"6\">No se encontraron productos con esta búsqueda en ' + empresa + '</tr></td>';\n                table.append(tr);\n            }\n        });\n\n    });\n}\n"}]}