{"sourceFile": "services/scripts/public/buscar-productos/index.php", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1749280039534, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1749280643902, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,8 +11,12 @@\n     <h1>Buscador de productos</h1>\n     <div class=\"form-group\">\n         <input type=\"text\" name=\"buscado\" id=\"buscado\" onkeypress=\"enter_buscar(event)\" class=\"form-control\" aria-describedby=\"buscar\"><br>\n         <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">\n+        <div class=\"form-check form-check-inline ml-2\">\n+            <input type=\"checkbox\" class=\"form-check-input\" id=\"terminalPrecios\" checked>\n+            <label class=\"form-check-label\" for=\"terminalPrecios\" title=\"Active esta opción si utiliza esta funcionalidad para una terminal de precios pública que no dispone de teclado o mouse\">Terminal de precios</label>\n+        </div>\n     </div>\n     <br>\n \n     <table class=\"table table-striped\">\n"}, {"date": 1749280675108, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,10 +10,10 @@\n \n     <h1>Buscador de productos</h1>\n     <div class=\"form-group\">\n         <input type=\"text\" name=\"buscado\" id=\"buscado\" onkeypress=\"enter_buscar(event)\" class=\"form-control\" aria-describedby=\"buscar\"><br>\n-        <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">\n         <div class=\"form-check form-check-inline ml-2\">\n+            <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">\n             <input type=\"checkbox\" class=\"form-check-input\" id=\"terminalPrecios\" checked>\n             <label class=\"form-check-label\" for=\"terminalPrecios\" title=\"Active esta opción si utiliza esta funcionalidad para una terminal de precios pública que no dispone de teclado o mouse\">Terminal de precios</label>\n         </div>\n     </div>\n"}, {"date": 1749280687908, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,9 +11,9 @@\n     <h1>Buscador de productos</h1>\n     <div class=\"form-group\">\n         <input type=\"text\" name=\"buscado\" id=\"buscado\" onkeypress=\"enter_buscar(event)\" class=\"form-control\" aria-describedby=\"buscar\"><br>\n         <div class=\"form-check form-check-inline ml-2\">\n-            <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">\n+            <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">&nbsp;\n             <input type=\"checkbox\" class=\"form-check-input\" id=\"terminalPrecios\" checked>\n             <label class=\"form-check-label\" for=\"terminalPrecios\" title=\"Active esta opción si utiliza esta funcionalidad para una terminal de precios pública que no dispone de teclado o mouse\">Terminal de precios</label>\n         </div>\n     </div>\n"}], "date": 1749280039534, "name": "Commit-0", "content": "<!DOCTYPE html>\n<html>\n<head>\n    <title>Buscador de productos</title>\n    <link rel=\"stylesheet\" href=\"bootstrap.min.css\">\n</head>\n<body>\n\n<div class=\"container\">\n\n    <h1>Buscador de productos</h1>\n    <div class=\"form-group\">\n        <input type=\"text\" name=\"buscado\" id=\"buscado\" onkeypress=\"enter_buscar(event)\" class=\"form-control\" aria-describedby=\"buscar\"><br>\n        <input type=\"submit\" name=\"buscar\" id=\"buscar\" value=\"Buscar\" onclick=\"buscar()\" class=\"btn btn-outline-secondary\">\n    </div>\n    <br>\n\n    <table class=\"table table-striped\">\n    </table>\n</div>\n\n<script src=\"jquery-2.2.4.min.js\"></script>\n<script src=\"buscador.js\"></script>\n<script>\n    var iues = '<?php echo $_GET['iue']; ?>'.split(',');\n    var empresas = '<?php echo $_GET['empresa']; ?>'.split(',');\n    var cantidad = '<?php echo is_numeric($_GET['cantidad']) ? $_GET['cantidad'] : 10; ?>';\n\n    $(function() {\n        $(\"#buscado\").focus();\n    });\n</script>\n\n</body>\n</html>"}]}