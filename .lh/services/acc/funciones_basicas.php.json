{"sourceFile": "services/acc/funciones_basicas.php", "activeCommit": 0, "commits": [{"activePatchIndex": 10, "patches": [{"date": 1726155751808, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726524573726, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -562,14 +562,14 @@\n }\n \n function tipoDocs($id = false, $sin_cuit = false) {\n     $tipoDocs = [\n+        ['id' => 99, 'valor' => 'Ninguno'],\n         ['id' => 80, 'valor' => 'CUIT'],\n         ['id' => 96, 'valor' => 'DNI'],\n         ['id' => 87, 'valor' => 'CDI'],\n         ['id' => 91, 'valor' => 'CI Extranjera'],\n         ['id' => 94, 'valor' => 'Pasaporte'],\n-        ['id' => 99, 'valor' => 'Otro']\n     ];\n \n     if ($id) {\n         foreach ($tipoDocs as $tipoDoc) {\n"}, {"date": 1726524594503, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -580,9 +580,9 @@\n     }\n \n     if ($sin_cuit) {\n         unset($tipoDocs[0]);\n-        unset($tipoDocs[5]);\n+        unset($tipoDocs[1]);\n     }\n \n     return $tipoDocs;\n }\n"}, {"date": 1726524720590, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -579,9 +579,8 @@\n         }\n     }\n \n     if ($sin_cuit) {\n-        unset($tipoDocs[0]);\n         unset($tipoDocs[1]);\n     }\n \n     return $tipoDocs;\n"}, {"date": 1729437637407, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -533,17 +533,17 @@\n function es_franquiciado($idempresa, $franquicia = false)\n {\n     switch ($franquicia) {\n         default: // Todos los franquiciados\n-            if (in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664]))\n+            if (in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12348]))\n                 return 874;\n             else\n                 return false;\n             break;\n \n         case 'gaming': // Franquicia de Gaming City\n         case 874:\n-            return in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664]);\n+            return in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12348]);\n             break;\n     }\n }\n \n"}, {"date": 1730065292296, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,587 @@\n+<?php\n+function recuperar_sesion()\n+{\n+    session_name(\"saasargentina\");\n+    session_set_cookie_params(0, '/', URL_COOKIE);\n+    session_start();\n+}\n+\n+function hash_session_token()\n+{\n+    return hash('sha256', $_SESSION['empresa_idempresa'].$_SESSION['usuario_idusuario'].session_id());\n+}\n+\n+function mostrar_error($valor, $continuar = false)\n+{\n+    global $modulo;\n+    global $a;\n+    global $id;\n+    global $boton;\n+    global $ventana;\n+    global $id_boton;\n+\n+    // Si estamos ejecutando por consola\n+    if (!isset($_SESSION['empresa_idempresa']) && in_array($modulo, ['script']))\n+        exit('ERROR: '.$valor);\n+\n+    // Si estamos ejecutando por crontab\n+    if (!isset($_SESSION['empresa_idempresa']) && in_array($modulo, ['crontab'])) {\n+        global $idempresa;\n+        global $script;\n+        $modulo = $script['idscript'];\n+\n+    } else {\n+        $idempresa = $_SESSION['empresa_idempresa'];\n+    }\n+\n+    $texto = 'ERROR: '.$valor.'<br /><br />\n+        empresa_idempresa: <a href=\"'.URL_SAAS.'/saas.php?a=verempresa&id='.$idempresa.'\">'.$idempresa.'</a><br />\n+        empresa_nombre: '.$_SESSION['empresa_nombre'].'<br />\n+        empresa_mail: '.$_SESSION['empresa_mail'].'<br />\n+        usuario_idusuario: <a href=\"'.URL_SAAS.'/saas.php?a=altaticket&idusuario='.$_SESSION['usuario_idusuario'].'\">'.$_SESSION['usuario_idusuario'].'</a><br />\n+        usuario_mail: '.$_SESSION['usuario_mail'].'<br />\n+        usuario_nombre: '.$_SESSION['usuario_nombre'].'<br />\n+        mail: '.$_SESSION['mail'].' ('.$_POST['mail'].')<br />\n+        <br />\n+        modulo: '.$modulo.'<br />\n+        a: '.$a.'<br />\n+        id: '.$id.'<br />\n+        boton: '.$boton.'<br />\n+        ventana: '.$ventana.'<br />\n+        id_boton: '.$id_boton.'<br />\n+        continuar: '.$continuar.'<br /><br />\n+        $_SESSION[a]: '.$_SESSION['a'].'<br /><br />\n+        $_REQUEST: '.json_encode($_REQUEST).'<br />';\n+\n+    if (ESTADO != 'desarrollo') {\n+        enviar_mail(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ERROR', $texto);\n+        if (!$continuar) {\n+            exit('<script>document.location = \"'.URL_ERROR.'\";</script>');\n+        }\n+\n+    } else {\n+        exit(\"<br /><br />\\r\\n\\r\\n\".$texto);\n+    }\n+}\n+\n+function enviar_mail($remitente, $destinatario, $asunto, $texto, $copia_remitente = false, $cuenta = 'default', $adjuntos = array())\n+{\n+    global $modulo;\n+\n+    if (ESTADO == 'desarrollo')\n+        return true;\n+\n+    if (!$asunto)\n+        return false;\n+\n+    // Revisar si $asunto tiene las palabras con las que se mandó SPAM\n+    if (preg_match('/bloqueo|urgente|sospecha/i', $asunto)) {\n+        mostrar_error('No se envió un mail porque el asunto contiene palabras prohibidas: '.$asunto, true);\n+        return false;\n+    }\n+\n+    if ($modulo != 'crontab' && $modulo != 'script'\n+        && isset($_SESSION['empresa_idempresa']) && $_SESSION['empresa_idempresa']) {\n+        // Empresas que bloqueamos el envío de mails porque hicieron SPAM\n+        if ($_SESSION['empresa_idempresa'] && in_array($_SESSION['empresa_idempresa'], [8902, 2630]))\n+            return false;\n+\n+        // Revisar si en la variable de sesion 'hora_ultimo_mail' se envió un mail hace menos de x segundos\n+        if ($_SESSION['hora_ultimo_mail'] && (time() - $_SESSION['hora_ultimo_mail']) < SEGUNDOS_ENTRE_MAILS) {\n+            mostrar_error(\"No se envió un mail porque fue hace menos de \".SEGUNDOS_ENTRE_MAILS.\" segundos. El asunto es: \".$asunto, true);\n+            return false;\n+        }\n+        $_SESSION['hora_ultimo_mail'] = time();\n+    }\n+\n+\n+    // Si es sqs lo mando a la cola\n+    if (($cuenta === 'sqs' || $cuenta === 'default')\n+        && function_exists('email_queue'))\n+        return email_queue($remitente, $destinatario, $asunto, $texto, $copia_remitente, $adjuntos);\n+\n+    if ($cuenta === 'default') // No existe funcion email_queue\n+        $cuenta = 'no-reply';\n+\n+\n+    // Voy con algún SMTP\n+    require_once PATH_TOOLS.'swiftmailer/lib/swift_required.php';\n+\n+    try {\n+\n+        switch ($cuenta) {\n+            case 'info':\n+                $cuenta = array(\n+                    'servidor' => 'smtp.gmail.com',\n+                    'puerto' => 465,\n+                    'seguridad' => 'ssl',\n+                    'autenticacion' => 0,\n+                    'user' => SMTP_INFO_USER,\n+                    'pass' => SMTP_INFO_PASS,\n+                    );\n+                $texto.= '<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>';\n+                break;\n+\n+            case 'no-reply':\n+                $cuenta = array(\n+                    'servidor' => 'smtp.gmail.com',\n+                    'puerto' => 465,\n+                    'seguridad' => 'ssl',\n+                    'autenticacion' => 0,\n+                    'user' => SMTP_NOREPLY_USER,\n+                    'pass' => SMTP_NOREPLY_PASS,\n+                    );\n+                $texto.= '<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>';\n+                break;\n+\n+            case 'ses':\n+                $cuenta = array(\n+                    'servidor' => 'email-smtp.sa-east-1.amazonaws.com',\n+                    'puerto' => 587,\n+                    'seguridad' => 'tls',\n+                    'autenticacion' => 0,\n+                    'user' => SMTP_SES_USER,\n+                    'pass' => SMTP_SES_PASS,\n+                    );\n+                $copia_remitente = false;\n+                if (is_array($remitente)\n+                    && count($remitente) > 1\n+                    && $remitente[0] == MAIL_SERVIDOR) {\n+                    $responder = $remitente[1];\n+                    $remitente = MAIL_SERVIDOR;\n+                } else {\n+                    $responder = $remitente;\n+                }\n+                $texto.= '<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>';\n+                break;\n+\n+            default:\n+                if (!(is_array($cuenta)\n+                    && isset($cuenta['servidor'])\n+                    && isset($cuenta['puerto'])\n+                    && isset($cuenta['seguridad'])\n+                    && isset($cuenta['autenticacion'])\n+                    && isset($cuenta['user'])\n+                    && isset($cuenta['pass'])\n+                    ))\n+                    mostrar_error('Función enviar_mail con parámetro cuenta incorrecto: '.json_encode($cuenta), true);\n+                break;\n+        }\n+\n+        // Si tengo más de un destinatario, el segundo va como copia y el resto lo borro\n+        // porque no queremos que se use como sistema de newsletter (tampoco mantengo el nombre)\n+        if (is_array($destinatario)\n+            && count($destinatario) > 1\n+            && $destinatario[0] != MAIL_INFO) {\n+            $copia = $destinatario[1];\n+            $destinatario = array($destinatario[0]);\n+        } else {\n+            $copia = false;\n+        }\n+\n+        if (!isset($responder))\n+            $responder = $remitente;\n+\n+        $transporte = Swift_SmtpTransport::newInstance($cuenta['servidor'], $cuenta['puerto'], $cuenta['seguridad'])\n+            -> setUsername($cuenta['user'])\n+            -> setPassword($cuenta['pass']);\n+\n+        $envio = Swift_Message::newInstance()\n+            -> setFrom($remitente)\n+            -> setSender($remitente)\n+            -> setReplyTo($responder)\n+            -> setTo($destinatario)\n+            -> setSubject($asunto)\n+            -> setContentType('text/html')\n+            -> setBody($texto, 'text/html');\n+        if ($copia)\n+            $envio -> setCc($copia);\n+        if ($copia_remitente)\n+            $envio -> setBcc($responder);\n+\n+        foreach ($adjuntos as $adjunto) {\n+            switch ($adjunto['tipo']) {\n+                default:\n+                case 'archivo':\n+                    $envio -> attach(Swift_Attachment::fromPath($adjunto['nombre']));\n+                    break;\n+\n+                case 'html':\n+                    $envio -> attach(Swift_Attachment::newInstance()\n+                        -> setFilename($adjunto['nombre'])\n+                        -> setContentType('text/html')\n+                        -> setBody($adjunto['valor'])\n+                        );\n+                    break;\n+\n+                case 'pdf':\n+                    $envio -> attach(Swift_Attachment::newInstance()\n+                        -> setFilename($adjunto['nombre'])\n+                        -> setContentType('application/pdf')\n+                        -> setBody($adjunto['valor'])\n+                        );\n+                    break;\n+\n+                case 'planilla':\n+                    $envio -> attach(Swift_Attachment::newInstance()\n+                        -> setFilename($adjunto['nombre'])\n+                        -> setContentType('application/vnd.ms-excel')\n+                        -> setBody($adjunto['valor'])\n+                        );\n+                    break;\n+            }\n+        }\n+\n+        return Swift_Mailer::newInstance($transporte) -> send($envio);\n+\n+    } catch (Exception $e) {\n+        $separador = ';';\n+        file_put_contents(PATH_LOGS . 'mail_caido.csv',\n+            date(\"Y-m-d H:i:s\") . $separador\n+            . 'ERROR' . $separador\n+            . $_SESSION['empresa_idempresa'] . $separador\n+            . $cuenta['user'] . $separador\n+            . str_replace($separador, ',', $e->getMessage()) . \"\\r\\n\"\n+            , FILE_APPEND);\n+    }\n+}\n+\n+function generar_random($tamaño = 50) {\n+    $chars = \"abcdefghijkmnopqrstuvwxyz023456789ABCDEFGHIJKMNOPQRSTUVWXYZ\";\n+    $random = '';\n+    for($i = 0; $i < $tamaño; $i++)\n+        $random.= $chars[rand(0, 58)];\n+\n+    return $random;\n+}\n+\n+function redondeo($numero)\n+{\n+    return (isset($_SESSION['control_formato_separador_miles']) && $_SESSION['control_formato_separador_miles'])\n+        ? number_format(round($numero, 2), 2, ',', '.')\n+        : number_format(round($numero, 2), 2, '.', '');\n+}\n+\n+function responder_json($estado = 'ok', $datos = array()) {\n+    $respuesta = array(\n+        'estado' => $estado\n+        );\n+    foreach ($datos as $key => $value) {\n+        $respuesta[$key] = $value;\n+    }\n+\n+    header('Content-type: application/json');\n+    if ($estado == 'error')\n+        http_response_code(400);\n+\n+    $callback = filter_input(INPUT_GET, 'callback');\n+    echo ($callback ? $callback . '(' . json_encode($respuesta) . ')' : json_encode($respuesta));\n+    exit();\n+}\n+\n+function listar_meses($desde, $hasta = false)\n+{\n+    if (!$hasta)\n+        $hasta = date(\"Y-m-d\");\n+    $meses = array();\n+    $desde_destripado = explode('-', substr($desde, 0, 7)); // Separo solo la fecha por si acaso trae la hora\n+    $hasta_destripado = explode('-', substr($hasta, 0, 7));\n+\n+    $desde_año = (int)$desde_destripado[0];\n+    $desde_mes = (int)$desde_destripado[1];\n+    $hasta_año = (int)$hasta_destripado[0];\n+    $hasta_mes = (int)$hasta_destripado[1];\n+\n+    while ($desde_año <= $hasta_año) {\n+        while (($desde_año < $hasta_año && $desde_mes < 13)\n+            || ($desde_año == $hasta_año && $desde_mes <= $hasta_mes && $desde_mes < 13)) {\n+            $meses[] = completar_numero($desde_mes, 2) . '-' . completar_numero($desde_año, 4);\n+            $desde_mes++;\n+        }\n+        $desde_mes = 1;\n+        $desde_año++;\n+    }\n+\n+    return $meses;\n+}\n+\n+function vacios($cantidad) {\n+    $temp = '';\n+    for ($i = 0; $i < $cantidad; $i++) {\n+        $temp.= ' ';\n+    }\n+    return $temp;\n+}\n+\n+function ceros($cant)\n+{\n+    $return = '';\n+    for ($i = 0; $i < $cant; $i++)\n+        $return.= '0';\n+    return $return;\n+}\n+\n+function completar_texto($valor, $digitos)\n+{\n+    $return = '';\n+    $largo_valor = mb_strlen($valor, 'UTF-8');\n+    if ($largo_valor > $digitos) {\n+        $return = mb_substr($valor, 0, $digitos);\n+    } else {\n+        $return = $valor;\n+        for ($i = 0; $i < ($digitos - $largo_valor); $i++) {\n+            $return.= ' ';\n+        }\n+    }\n+    return $return;\n+}\n+\n+function completar_numero($valor, $digitos)\n+{\n+    if ($valor == 0) {\n+        $return = '';\n+        for($j = 0; $j < $digitos; $j++)\n+            $return.='0';\n+\n+        return $return;\n+    }\n+\n+    $negativo = $valor < 0;\n+\n+    if ($negativo) {\n+        $valor = $valor * -1;\n+        $digitos--;\n+    }\n+\n+    $valor = \"$valor\";\n+    $return = '';\n+    $largo_valor = strlen($valor);\n+    if ($largo_valor > $digitos) {\n+        $return = substr($valor, 0, $digitos);\n+    } else {\n+        $return = $valor;\n+        for ($i = 0; $i < ($digitos - $largo_valor); $i++) {\n+            $return = '0'.$return;\n+        }\n+    }\n+    return ($negativo ? '-' : '') . $return;\n+}\n+\n+function numero_comprobante($letra, $puntodeventa, $numero)\n+{\n+    return $letra.completar_numero($puntodeventa, 5).'-'.completar_numero($numero, 8);\n+}\n+\n+function url_amigable($valor) {\n+    $separador = '-';\n+    $valor = strtolower($valor);\n+    $valor = str_replace(\n+        array('á', 'é', 'í', 'ó', 'ú', 'ñ', '&', '+', '-', '_', \"\\r\", \"\\r\\n\", \"\\n\"),\n+        array('a', 'e', 'i', 'o', 'u', 'n', 'y', 'y', ' ', ' ', ' ', ' ', ' '),\n+        $valor);\n+    $valor = trim(preg_replace(\"/[^ a-z0-9]/\", \"\", $valor));\n+    $valor = str_replace(' ', $separador, $valor);\n+\n+    return $valor;\n+}\n+\n+\n+function sanear_string($string)\n+{\n+\n+    $string = trim($string);\n+\n+    $string = str_replace(\n+        array('á', 'à', 'ä', 'â', 'ª', 'Á', 'À', 'Â', 'Ä'),\n+        array('a', 'a', 'a', 'a', 'a', 'A', 'A', 'A', 'A'),\n+        $string\n+    );\n+\n+    $string = str_replace(\n+        array('é', 'è', 'ë', 'ê', 'É', 'È', 'Ê', 'Ë'),\n+        array('e', 'e', 'e', 'e', 'E', 'E', 'E', 'E'),\n+        $string\n+    );\n+\n+    $string = str_replace(\n+        array('í', 'ì', 'ï', 'î', 'Í', 'Ì', 'Ï', 'Î'),\n+        array('i', 'i', 'i', 'i', 'I', 'I', 'I', 'I'),\n+        $string\n+    );\n+\n+    $string = str_replace(\n+        array('ó', 'ò', 'ö', 'ô', 'Ó', 'Ò', 'Ö', 'Ô'),\n+        array('o', 'o', 'o', 'o', 'O', 'O', 'O', 'O'),\n+        $string\n+    );\n+\n+    $string = str_replace(\n+        array('ú', 'ù', 'ü', 'û', 'Ú', 'Ù', 'Û', 'Ü'),\n+        array('u', 'u', 'u', 'u', 'U', 'U', 'U', 'U'),\n+        $string\n+    );\n+\n+    $string = str_replace(\n+        array('ñ', 'Ñ', 'ç', 'Ç'),\n+        array('n', 'N', 'c', 'C',),\n+        $string\n+    );\n+\n+    //Esta parte se encarga de eliminar cualquier caracter extraño\n+    $string = str_replace(\n+        array(\"¨\", \"º\", \"~\",\n+             \"#\", \"@\", \"|\", \"!\",\n+             \"·\", \"$\", \"%\", \"&\", \"/\",\n+             \"?\", \"'\", \"¡\",\n+             \"¿\", \"[\", \"^\", \"<code>\", \"]\",\n+             \"+\", \"}\", \"{\", \"¨\", \"´\",\n+             \">\", \"< \", \";\", \",\", \":\",\n+             \".\"),\n+        '',\n+        $string\n+    );\n+\n+    return $string;\n+}\n+\n+function calcular_cuit($dni, $codigo_sexo) {\n+\n+    $dni = completar_numero($dni, 8);\n+    $multiplicadores = Array('3', '2', '7','6', '5', '4', '3', '2');\n+    $calculo = (substr($codigo_sexo, 0, 1) * 5) + (substr($codigo_sexo, 1, 1) * 4);\n+\n+    for ( $i=0 ; $i<8 ; $i++ ) {\n+        $calculo += substr($dni,$i,1) * $multiplicadores[$i];\n+    }\n+\n+    $resto = ($calculo) % 11;\n+\n+    if (($codigo_sexo != '30') && ($resto <= 1)) {\n+        if ($resto == 0) {\n+            $ultimo_digito_cuit = '0';\n+        } else {\n+            if ($codigo_sexo == 20) {\n+                $ultimo_digito_cuit = '9';\n+            } else {\n+                $ultimo_digito_cuit = '4';\n+            }\n+        }\n+        $codigo_sexo = '23';\n+    } else {\n+        $ultimo_digito_cuit = 11 - $resto;\n+    }\n+\n+    return $codigo_sexo . $dni . $ultimo_digito_cuit;\n+}\n+\n+function escape_no_sql($unescaped) {\n+    $replacements = array(\n+        \"\\x00\"=>'\\x00',\n+        \"\\n\"=>'\\n',\n+        \"\\r\"=>'\\r',\n+        \"\\\\\"=>'\\\\\\\\',\n+        \"'\"=>\"\\'\",\n+        '\"'=>'\\\"',\n+        \"\\x1a\"=>'\\x1a',\n+    );\n+    return strtr($unescaped, $replacements);\n+}\n+\n+function queue_ml($url, $body)\n+{\n+    file_put_contents(PATH_LOGS.'notificaciones/'.date(\"Y-m-d_H-i\").'.log',\n+        date(\"Y-m-d_H-i-s\")\n+        . '|||'\n+        . $url\n+        . '|||'\n+        . $body\n+        . \"\\r\\n\"\n+        , FILE_APPEND);\n+}\n+\n+function en_rfce($cuit)\n+{\n+    $cuit = (string)$cuit;\n+    if (strlen($cuit) != 11)\n+        return false;\n+\n+    $rfce_listado = file_get_contents(__DIR__.'/rfce_listado.txt');\n+    return strpos($rfce_listado, $cuit) === false\n+        ? false\n+        : true;\n+}\n+\n+function mes($idmes)\n+{\n+    $mes = array(\n+        1 => 'Enero',\n+        2 => 'Febrero',\n+        3 => 'Marzo',\n+        4 => 'Abril',\n+        5 => 'Mayo',\n+        6 => 'Junio',\n+        7 => 'Julio',\n+        8 => 'Agosto',\n+        9 => 'Septiembre',\n+        10 => 'Octubre',\n+        11 => 'Noviembre',\n+        12 => 'Diciembre'\n+        );\n+    return $mes[$idmes];\n+}\n+\n+function es_franquiciado($idempresa, $franquicia = false)\n+{\n+    switch ($franquicia) {\n+        default: // Todos los franquiciados\n+            if (in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12348, 12355]))\n+                return 874;\n+            else\n+                return false;\n+            break;\n+\n+        case 'gaming': // Franquicia de Gaming City\n+        case 874:\n+            return in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12348, 12355]);\n+            break;\n+    }\n+}\n+\n+function sin_control_numeracion($idempresa)\n+{\n+    // Empresas anteriores que ya arreglaron la numeración\n+    // 8142, 7398, 2443, 301, 798, 5914, 8828, 10008\n+    // Empresas excluidas del control de numeración porque usaron otro sistema\n+    return in_array($idempresa, [8905, 11597]);\n+}\n+\n+function sin_fullsearch($idempresa)\n+{\n+    $empresas_sin_fullsearch = [];\n+    return in_array($idempresa, $empresas_sin_fullsearch);\n+}\n+\n+function tipoDocs($id = false, $sin_cuit = false) {\n+    $tipoDocs = [\n+        ['id' => 99, 'valor' => 'Ninguno'],\n+        ['id' => 80, 'valor' => 'CUIT'],\n+        ['id' => 96, 'valor' => 'DNI'],\n+        ['id' => 87, 'valor' => 'CDI'],\n+        ['id' => 91, 'valor' => 'CI Extranjera'],\n+        ['id' => 94, 'valor' => 'Pasaporte'],\n+    ];\n+\n+    if ($id) {\n+        foreach ($tipoDocs as $tipoDoc) {\n+            if ($tipoDoc['id'] == $id) {\n+                return $tipoDoc['valor'];\n+            }\n+        }\n+    }\n+\n+    if ($sin_cuit) {\n+        unset($tipoDocs[1]);\n+    }\n+\n+    return $tipoDocs;\n+}\n"}, {"date": 1747141257048, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -551,9 +551,9 @@\n {\n     // Empresas anteriores que ya arreglaron la numeración\n     // 8142, 7398, 2443, 301, 798, 5914, 8828, 10008\n     // Empresas excluidas del control de numeración porque usaron otro sistema\n-    return in_array($idempresa, [8905, 11597]);\n+    return in_array($idempresa, [8905, 11597, 11854]);\n }\n \n function sin_fullsearch($idempresa)\n {\n@@ -584,591 +584,4 @@\n     }\n \n     return $tipoDocs;\n }\n-<?php\n-function recuperar_sesion()\n-{\n-    session_name(\"saasargentina\");\n-    session_set_cookie_params(0, '/', URL_COOKIE);\n-    session_start();\n-}\n-\n-function hash_session_token()\n-{\n-    return hash('sha256', $_SESSION['empresa_idempresa'].$_SESSION['usuario_idusuario'].session_id());\n-}\n-\n-function mostrar_error($valor, $continuar = false)\n-{\n-    global $modulo;\n-    global $a;\n-    global $id;\n-    global $boton;\n-    global $ventana;\n-    global $id_boton;\n-\n-    // Si estamos ejecutando por consola\n-    if (!isset($_SESSION['empresa_idempresa']) && in_array($modulo, ['script']))\n-        exit('ERROR: '.$valor);\n-\n-    // Si estamos ejecutando por crontab\n-    if (!isset($_SESSION['empresa_idempresa']) && in_array($modulo, ['crontab'])) {\n-        global $idempresa;\n-        global $script;\n-        $modulo = $script['idscript'];\n-\n-    } else {\n-        $idempresa = $_SESSION['empresa_idempresa'];\n-    }\n-\n-    $texto = 'ERROR: '.$valor.'<br /><br />\n-        empresa_idempresa: <a href=\"'.URL_SAAS.'/saas.php?a=verempresa&id='.$idempresa.'\">'.$idempresa.'</a><br />\n-        empresa_nombre: '.$_SESSION['empresa_nombre'].'<br />\n-        empresa_mail: '.$_SESSION['empresa_mail'].'<br />\n-        usuario_idusuario: <a href=\"'.URL_SAAS.'/saas.php?a=altaticket&idusuario='.$_SESSION['usuario_idusuario'].'\">'.$_SESSION['usuario_idusuario'].'</a><br />\n-        usuario_mail: '.$_SESSION['usuario_mail'].'<br />\n-        usuario_nombre: '.$_SESSION['usuario_nombre'].'<br />\n-        mail: '.$_SESSION['mail'].' ('.$_POST['mail'].')<br />\n-        <br />\n-        modulo: '.$modulo.'<br />\n-        a: '.$a.'<br />\n-        id: '.$id.'<br />\n-        boton: '.$boton.'<br />\n-        ventana: '.$ventana.'<br />\n-        id_boton: '.$id_boton.'<br />\n-        continuar: '.$continuar.'<br /><br />\n-        $_SESSION[a]: '.$_SESSION['a'].'<br /><br />\n-        $_REQUEST: '.json_encode($_REQUEST).'<br />';\n-\n-    if (ESTADO != 'desarrollo') {\n-        enviar_mail(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ERROR', $texto);\n-        if (!$continuar) {\n-            exit('<script>document.location = \"'.URL_ERROR.'\";</script>');\n-        }\n-\n-    } else {\n-        exit(\"<br /><br />\\r\\n\\r\\n\".$texto);\n-    }\n-}\n-\n-function enviar_mail($remitente, $destinatario, $asunto, $texto, $copia_remitente = false, $cuenta = 'default', $adjuntos = array())\n-{\n-    global $modulo;\n-\n-    if (ESTADO == 'desarrollo')\n-        return true;\n-\n-    if (!$asunto)\n-        return false;\n-\n-    // Revisar si $asunto tiene las palabras con las que se mandó SPAM\n-    if (preg_match('/bloqueo|urgente|sospecha/i', $asunto)) {\n-        mostrar_error('No se envió un mail porque el asunto contiene palabras prohibidas: '.$asunto, true);\n-        return false;\n-    }\n-\n-    if ($modulo != 'crontab' && $modulo != 'script'\n-        && isset($_SESSION['empresa_idempresa']) && $_SESSION['empresa_idempresa']) {\n-        // Empresas que bloqueamos el envío de mails porque hicieron SPAM\n-        if ($_SESSION['empresa_idempresa'] && in_array($_SESSION['empresa_idempresa'], [8902, 2630]))\n-            return false;\n-\n-        // Revisar si en la variable de sesion 'hora_ultimo_mail' se envió un mail hace menos de x segundos\n-        if ($_SESSION['hora_ultimo_mail'] && (time() - $_SESSION['hora_ultimo_mail']) < SEGUNDOS_ENTRE_MAILS) {\n-            mostrar_error(\"No se envió un mail porque fue hace menos de \".SEGUNDOS_ENTRE_MAILS.\" segundos. El asunto es: \".$asunto, true);\n-            return false;\n-        }\n-        $_SESSION['hora_ultimo_mail'] = time();\n-    }\n-\n-\n-    // Si es sqs lo mando a la cola\n-    if (($cuenta === 'sqs' || $cuenta === 'default')\n-        && function_exists('email_queue'))\n-        return email_queue($remitente, $destinatario, $asunto, $texto, $copia_remitente, $adjuntos);\n-\n-    if ($cuenta === 'default') // No existe funcion email_queue\n-        $cuenta = 'no-reply';\n-\n-\n-    // Voy con algún SMTP\n-    require_once PATH_TOOLS.'swiftmailer/lib/swift_required.php';\n-\n-    try {\n-\n-        switch ($cuenta) {\n-            case 'info':\n-                $cuenta = array(\n-                    'servidor' => 'smtp.gmail.com',\n-                    'puerto' => 465,\n-                    'seguridad' => 'ssl',\n-                    'autenticacion' => 0,\n-                    'user' => SMTP_INFO_USER,\n-                    'pass' => SMTP_INFO_PASS,\n-                    );\n-                $texto.= '<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>';\n-                break;\n-\n-            case 'no-reply':\n-                $cuenta = array(\n-                    'servidor' => 'smtp.gmail.com',\n-                    'puerto' => 465,\n-                    'seguridad' => 'ssl',\n-                    'autenticacion' => 0,\n-                    'user' => SMTP_NOREPLY_USER,\n-                    'pass' => SMTP_NOREPLY_PASS,\n-                    );\n-                $texto.= '<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>';\n-                break;\n-\n-            case 'ses':\n-                $cuenta = array(\n-                    'servidor' => 'email-smtp.sa-east-1.amazonaws.com',\n-                    'puerto' => 587,\n-                    'seguridad' => 'tls',\n-                    'autenticacion' => 0,\n-                    'user' => SMTP_SES_USER,\n-                    'pass' => SMTP_SES_PASS,\n-                    );\n-                $copia_remitente = false;\n-                if (is_array($remitente)\n-                    && count($remitente) > 1\n-                    && $remitente[0] == MAIL_SERVIDOR) {\n-                    $responder = $remitente[1];\n-                    $remitente = MAIL_SERVIDOR;\n-                } else {\n-                    $responder = $remitente;\n-                }\n-                $texto.= '<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>';\n-                break;\n-\n-            default:\n-                if (!(is_array($cuenta)\n-                    && isset($cuenta['servidor'])\n-                    && isset($cuenta['puerto'])\n-                    && isset($cuenta['seguridad'])\n-                    && isset($cuenta['autenticacion'])\n-                    && isset($cuenta['user'])\n-                    && isset($cuenta['pass'])\n-                    ))\n-                    mostrar_error('Función enviar_mail con parámetro cuenta incorrecto: '.json_encode($cuenta), true);\n-                break;\n-        }\n-\n-        // Si tengo más de un destinatario, el segundo va como copia y el resto lo borro\n-        // porque no queremos que se use como sistema de newsletter (tampoco mantengo el nombre)\n-        if (is_array($destinatario)\n-            && count($destinatario) > 1\n-            && $destinatario[0] != MAIL_INFO) {\n-            $copia = $destinatario[1];\n-            $destinatario = array($destinatario[0]);\n-        } else {\n-            $copia = false;\n-        }\n-\n-        if (!isset($responder))\n-            $responder = $remitente;\n-\n-        $transporte = Swift_SmtpTransport::newInstance($cuenta['servidor'], $cuenta['puerto'], $cuenta['seguridad'])\n-            -> setUsername($cuenta['user'])\n-            -> setPassword($cuenta['pass']);\n-\n-        $envio = Swift_Message::newInstance()\n-            -> setFrom($remitente)\n-            -> setSender($remitente)\n-            -> setReplyTo($responder)\n-            -> setTo($destinatario)\n-            -> setSubject($asunto)\n-            -> setContentType('text/html')\n-            -> setBody($texto, 'text/html');\n-        if ($copia)\n-            $envio -> setCc($copia);\n-        if ($copia_remitente)\n-            $envio -> setBcc($responder);\n-\n-        foreach ($adjuntos as $adjunto) {\n-            switch ($adjunto['tipo']) {\n-                default:\n-                case 'archivo':\n-                    $envio -> attach(Swift_Attachment::fromPath($adjunto['nombre']));\n-                    break;\n-\n-                case 'html':\n-                    $envio -> attach(Swift_Attachment::newInstance()\n-                        -> setFilename($adjunto['nombre'])\n-                        -> setContentType('text/html')\n-                        -> setBody($adjunto['valor'])\n-                        );\n-                    break;\n-\n-                case 'pdf':\n-                    $envio -> attach(Swift_Attachment::newInstance()\n-                        -> setFilename($adjunto['nombre'])\n-                        -> setContentType('application/pdf')\n-                        -> setBody($adjunto['valor'])\n-                        );\n-                    break;\n-\n-                case 'planilla':\n-                    $envio -> attach(Swift_Attachment::newInstance()\n-                        -> setFilename($adjunto['nombre'])\n-                        -> setContentType('application/vnd.ms-excel')\n-                        -> setBody($adjunto['valor'])\n-                        );\n-                    break;\n-            }\n-        }\n-\n-        return Swift_Mailer::newInstance($transporte) -> send($envio);\n-\n-    } catch (Exception $e) {\n-        $separador = ';';\n-        file_put_contents(PATH_LOGS . 'mail_caido.csv',\n-            date(\"Y-m-d H:i:s\") . $separador\n-            . 'ERROR' . $separador\n-            . $_SESSION['empresa_idempresa'] . $separador\n-            . $cuenta['user'] . $separador\n-            . str_replace($separador, ',', $e->getMessage()) . \"\\r\\n\"\n-            , FILE_APPEND);\n-    }\n-}\n-\n-function generar_random($tamaño = 50) {\n-    $chars = \"abcdefghijkmnopqrstuvwxyz023456789ABCDEFGHIJKMNOPQRSTUVWXYZ\";\n-    $random = '';\n-    for($i = 0; $i < $tamaño; $i++)\n-        $random.= $chars[rand(0, 58)];\n-\n-    return $random;\n-}\n-\n-function redondeo($numero)\n-{\n-    return (isset($_SESSION['control_formato_separador_miles']) && $_SESSION['control_formato_separador_miles'])\n-        ? number_format(round($numero, 2), 2, ',', '.')\n-        : number_format(round($numero, 2), 2, '.', '');\n-}\n-\n-function responder_json($estado = 'ok', $datos = array()) {\n-    $respuesta = array(\n-        'estado' => $estado\n-        );\n-    foreach ($datos as $key => $value) {\n-        $respuesta[$key] = $value;\n-    }\n-\n-    header('Content-type: application/json');\n-    if ($estado == 'error')\n-        http_response_code(400);\n-\n-    $callback = filter_input(INPUT_GET, 'callback');\n-    echo ($callback ? $callback . '(' . json_encode($respuesta) . ')' : json_encode($respuesta));\n-    exit();\n-}\n-\n-function listar_meses($desde, $hasta = false)\n-{\n-    if (!$hasta)\n-        $hasta = date(\"Y-m-d\");\n-    $meses = array();\n-    $desde_destripado = explode('-', substr($desde, 0, 7)); // Separo solo la fecha por si acaso trae la hora\n-    $hasta_destripado = explode('-', substr($hasta, 0, 7));\n-\n-    $desde_año = (int)$desde_destripado[0];\n-    $desde_mes = (int)$desde_destripado[1];\n-    $hasta_año = (int)$hasta_destripado[0];\n-    $hasta_mes = (int)$hasta_destripado[1];\n-\n-    while ($desde_año <= $hasta_año) {\n-        while (($desde_año < $hasta_año && $desde_mes < 13)\n-            || ($desde_año == $hasta_año && $desde_mes <= $hasta_mes && $desde_mes < 13)) {\n-            $meses[] = completar_numero($desde_mes, 2) . '-' . completar_numero($desde_año, 4);\n-            $desde_mes++;\n-        }\n-        $desde_mes = 1;\n-        $desde_año++;\n-    }\n-\n-    return $meses;\n-}\n-\n-function vacios($cantidad) {\n-    $temp = '';\n-    for ($i = 0; $i < $cantidad; $i++) {\n-        $temp.= ' ';\n-    }\n-    return $temp;\n-}\n-\n-function ceros($cant)\n-{\n-    $return = '';\n-    for ($i = 0; $i < $cant; $i++)\n-        $return.= '0';\n-    return $return;\n-}\n-\n-function completar_texto($valor, $digitos)\n-{\n-    $return = '';\n-    $largo_valor = mb_strlen($valor, 'UTF-8');\n-    if ($largo_valor > $digitos) {\n-        $return = mb_substr($valor, 0, $digitos);\n-    } else {\n-        $return = $valor;\n-        for ($i = 0; $i < ($digitos - $largo_valor); $i++) {\n-            $return.= ' ';\n-        }\n-    }\n-    return $return;\n-}\n-\n-function completar_numero($valor, $digitos)\n-{\n-    if ($valor == 0) {\n-        $return = '';\n-        for($j = 0; $j < $digitos; $j++)\n-            $return.='0';\n-\n-        return $return;\n-    }\n-\n-    $negativo = $valor < 0;\n-\n-    if ($negativo) {\n-        $valor = $valor * -1;\n-        $digitos--;\n-    }\n-\n-    $valor = \"$valor\";\n-    $return = '';\n-    $largo_valor = strlen($valor);\n-    if ($largo_valor > $digitos) {\n-        $return = substr($valor, 0, $digitos);\n-    } else {\n-        $return = $valor;\n-        for ($i = 0; $i < ($digitos - $largo_valor); $i++) {\n-            $return = '0'.$return;\n-        }\n-    }\n-    return ($negativo ? '-' : '') . $return;\n-}\n-\n-function numero_comprobante($letra, $puntodeventa, $numero)\n-{\n-    return $letra.completar_numero($puntodeventa, 5).'-'.completar_numero($numero, 8);\n-}\n-\n-function url_amigable($valor) {\n-    $separador = '-';\n-    $valor = strtolower($valor);\n-    $valor = str_replace(\n-        array('á', 'é', 'í', 'ó', 'ú', 'ñ', '&', '+', '-', '_', \"\\r\", \"\\r\\n\", \"\\n\"),\n-        array('a', 'e', 'i', 'o', 'u', 'n', 'y', 'y', ' ', ' ', ' ', ' ', ' '),\n-        $valor);\n-    $valor = trim(preg_replace(\"/[^ a-z0-9]/\", \"\", $valor));\n-    $valor = str_replace(' ', $separador, $valor);\n-\n-    return $valor;\n-}\n-\n-\n-function sanear_string($string)\n-{\n-\n-    $string = trim($string);\n-\n-    $string = str_replace(\n-        array('á', 'à', 'ä', 'â', 'ª', 'Á', 'À', 'Â', 'Ä'),\n-        array('a', 'a', 'a', 'a', 'a', 'A', 'A', 'A', 'A'),\n-        $string\n-    );\n-\n-    $string = str_replace(\n-        array('é', 'è', 'ë', 'ê', 'É', 'È', 'Ê', 'Ë'),\n-        array('e', 'e', 'e', 'e', 'E', 'E', 'E', 'E'),\n-        $string\n-    );\n-\n-    $string = str_replace(\n-        array('í', 'ì', 'ï', 'î', 'Í', 'Ì', 'Ï', 'Î'),\n-        array('i', 'i', 'i', 'i', 'I', 'I', 'I', 'I'),\n-        $string\n-    );\n-\n-    $string = str_replace(\n-        array('ó', 'ò', 'ö', 'ô', 'Ó', 'Ò', 'Ö', 'Ô'),\n-        array('o', 'o', 'o', 'o', 'O', 'O', 'O', 'O'),\n-        $string\n-    );\n-\n-    $string = str_replace(\n-        array('ú', 'ù', 'ü', 'û', 'Ú', 'Ù', 'Û', 'Ü'),\n-        array('u', 'u', 'u', 'u', 'U', 'U', 'U', 'U'),\n-        $string\n-    );\n-\n-    $string = str_replace(\n-        array('ñ', 'Ñ', 'ç', 'Ç'),\n-        array('n', 'N', 'c', 'C',),\n-        $string\n-    );\n-\n-    //Esta parte se encarga de eliminar cualquier caracter extraño\n-    $string = str_replace(\n-        array(\"¨\", \"º\", \"~\",\n-             \"#\", \"@\", \"|\", \"!\",\n-             \"·\", \"$\", \"%\", \"&\", \"/\",\n-             \"?\", \"'\", \"¡\",\n-             \"¿\", \"[\", \"^\", \"<code>\", \"]\",\n-             \"+\", \"}\", \"{\", \"¨\", \"´\",\n-             \">\", \"< \", \";\", \",\", \":\",\n-             \".\"),\n-        '',\n-        $string\n-    );\n-\n-    return $string;\n-}\n-\n-function calcular_cuit($dni, $codigo_sexo) {\n-\n-    $dni = completar_numero($dni, 8);\n-    $multiplicadores = Array('3', '2', '7','6', '5', '4', '3', '2');\n-    $calculo = (substr($codigo_sexo, 0, 1) * 5) + (substr($codigo_sexo, 1, 1) * 4);\n-\n-    for ( $i=0 ; $i<8 ; $i++ ) {\n-        $calculo += substr($dni,$i,1) * $multiplicadores[$i];\n-    }\n-\n-    $resto = ($calculo) % 11;\n-\n-    if (($codigo_sexo != '30') && ($resto <= 1)) {\n-        if ($resto == 0) {\n-            $ultimo_digito_cuit = '0';\n-        } else {\n-            if ($codigo_sexo == 20) {\n-                $ultimo_digito_cuit = '9';\n-            } else {\n-                $ultimo_digito_cuit = '4';\n-            }\n-        }\n-        $codigo_sexo = '23';\n-    } else {\n-        $ultimo_digito_cuit = 11 - $resto;\n-    }\n-\n-    return $codigo_sexo . $dni . $ultimo_digito_cuit;\n-}\n-\n-function escape_no_sql($unescaped) {\n-    $replacements = array(\n-        \"\\x00\"=>'\\x00',\n-        \"\\n\"=>'\\n',\n-        \"\\r\"=>'\\r',\n-        \"\\\\\"=>'\\\\\\\\',\n-        \"'\"=>\"\\'\",\n-        '\"'=>'\\\"',\n-        \"\\x1a\"=>'\\x1a',\n-    );\n-    return strtr($unescaped, $replacements);\n-}\n-\n-function queue_ml($url, $body)\n-{\n-    file_put_contents(PATH_LOGS.'notificaciones/'.date(\"Y-m-d_H-i\").'.log',\n-        date(\"Y-m-d_H-i-s\")\n-        . '|||'\n-        . $url\n-        . '|||'\n-        . $body\n-        . \"\\r\\n\"\n-        , FILE_APPEND);\n-}\n-\n-function en_rfce($cuit)\n-{\n-    $cuit = (string)$cuit;\n-    if (strlen($cuit) != 11)\n-        return false;\n-\n-    $rfce_listado = file_get_contents(__DIR__.'/rfce_listado.txt');\n-    return strpos($rfce_listado, $cuit) === false\n-        ? false\n-        : true;\n-}\n-\n-function mes($idmes)\n-{\n-    $mes = array(\n-        1 => 'Enero',\n-        2 => 'Febrero',\n-        3 => 'Marzo',\n-        4 => 'Abril',\n-        5 => 'Mayo',\n-        6 => 'Junio',\n-        7 => 'Julio',\n-        8 => 'Agosto',\n-        9 => 'Septiembre',\n-        10 => 'Octubre',\n-        11 => 'Noviembre',\n-        12 => 'Diciembre'\n-        );\n-    return $mes[$idmes];\n-}\n-\n-function es_franquiciado($idempresa, $franquicia = false)\n-{\n-    switch ($franquicia) {\n-        default: // Todos los franquiciados\n-            if (in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12348]))\n-                return 874;\n-            else\n-                return false;\n-            break;\n-\n-        case 'gaming': // Franquicia de Gaming City\n-        case 874:\n-            return in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664, 12348]);\n-            break;\n-    }\n-}\n-\n-function sin_control_numeracion($idempresa)\n-{\n-    // Empresas anteriores que ya arreglaron la numeración\n-    // 8142, 7398, 2443, 301, 798, 5914, 8828, 10008\n-    // Empresas excluidas del control de numeración porque usaron otro sistema\n-    return in_array($idempresa, [8905, 11597]);\n-}\n-\n-function sin_fullsearch($idempresa)\n-{\n-    $empresas_sin_fullsearch = [];\n-    return in_array($idempresa, $empresas_sin_fullsearch);\n-}\n-\n-function tipoDocs($id = false, $sin_cuit = false) {\n-    $tipoDocs = [\n-        ['id' => 99, 'valor' => 'Ninguno'],\n-        ['id' => 80, 'valor' => 'CUIT'],\n-        ['id' => 96, 'valor' => 'DNI'],\n-        ['id' => 87, 'valor' => 'CDI'],\n-        ['id' => 91, 'valor' => 'CI Extranjera'],\n-        ['id' => 94, 'valor' => 'Pasaporte'],\n-    ];\n-\n-    if ($id) {\n-        foreach ($tipoDocs as $tipoDoc) {\n-            if ($tipoDoc['id'] == $id) {\n-                return $tipoDoc['valor'];\n-            }\n-        }\n-    }\n-\n-    if ($sin_cuit) {\n-        unset($tipoDocs[1]);\n-    }\n-\n-    return $tipoDocs;\n-}\n"}, {"date": 1748886464506, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -584,4 +584,9 @@\n     }\n \n     return $tipoDocs;\n }\n+\n+function como_fe($idempresa) {\n+    // pyafipws, pyafipws_2025, afipsdk_lambda\n+    return 'pyafipws';\n+}\n\\ No newline at end of file\n"}, {"date": 1748886470871, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -588,5 +588,5 @@\n \n function como_fe($idempresa) {\n     // pyafipws, pyafipws_2025, afipsdk_lambda\n     return 'pyafipws';\n-}\n\\ No newline at end of file\n+}\n"}, {"date": 1748887152550, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -586,7 +586,10 @@\n     return $tipoDocs;\n }\n \n function como_fe($idempresa) {\n-    // pyafipws, pyafipws_2025, afipsdk_lambda\n+    // Por ahora las opciones son: pyafipws, pyafipws_2025, afipsdk_lambda\n+    if (in_array($idempresa, [161]))\n+        return 'afipsdk_lambda';\n+\n     return 'pyafipws';\n }\n"}, {"date": 1750169534118, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -551,9 +551,9 @@\n {\n     // Empresas anteriores que ya arreglaron la numeración\n     // 8142, 7398, 2443, 301, 798, 5914, 8828, 10008\n     // Empresas excluidas del control de numeración porque usaron otro sistema\n-    return in_array($idempresa, [8905, 11597, 11854]);\n+    return in_array($idempresa, [8905, 8980, 11597, 11854]);\n }\n \n function sin_fullsearch($idempresa)\n {\n"}], "date": 1726155751808, "name": "Commit-0", "content": "<?php\nfunction recuperar_sesion()\n{\n    session_name(\"saasargentina\");\n    session_set_cookie_params(0, '/', URL_COOKIE);\n    session_start();\n}\n\nfunction hash_session_token()\n{\n    return hash('sha256', $_SESSION['empresa_idempresa'].$_SESSION['usuario_idusuario'].session_id());\n}\n\nfunction mostrar_error($valor, $continuar = false)\n{\n    global $modulo;\n    global $a;\n    global $id;\n    global $boton;\n    global $ventana;\n    global $id_boton;\n\n    // Si estamos ejecutando por consola\n    if (!isset($_SESSION['empresa_idempresa']) && in_array($modulo, ['script']))\n        exit('ERROR: '.$valor);\n\n    // Si estamos ejecutando por crontab\n    if (!isset($_SESSION['empresa_idempresa']) && in_array($modulo, ['crontab'])) {\n        global $idempresa;\n        global $script;\n        $modulo = $script['idscript'];\n\n    } else {\n        $idempresa = $_SESSION['empresa_idempresa'];\n    }\n\n    $texto = 'ERROR: '.$valor.'<br /><br />\n        empresa_idempresa: <a href=\"'.URL_SAAS.'/saas.php?a=verempresa&id='.$idempresa.'\">'.$idempresa.'</a><br />\n        empresa_nombre: '.$_SESSION['empresa_nombre'].'<br />\n        empresa_mail: '.$_SESSION['empresa_mail'].'<br />\n        usuario_idusuario: <a href=\"'.URL_SAAS.'/saas.php?a=altaticket&idusuario='.$_SESSION['usuario_idusuario'].'\">'.$_SESSION['usuario_idusuario'].'</a><br />\n        usuario_mail: '.$_SESSION['usuario_mail'].'<br />\n        usuario_nombre: '.$_SESSION['usuario_nombre'].'<br />\n        mail: '.$_SESSION['mail'].' ('.$_POST['mail'].')<br />\n        <br />\n        modulo: '.$modulo.'<br />\n        a: '.$a.'<br />\n        id: '.$id.'<br />\n        boton: '.$boton.'<br />\n        ventana: '.$ventana.'<br />\n        id_boton: '.$id_boton.'<br />\n        continuar: '.$continuar.'<br /><br />\n        $_SESSION[a]: '.$_SESSION['a'].'<br /><br />\n        $_REQUEST: '.json_encode($_REQUEST).'<br />';\n\n    if (ESTADO != 'desarrollo') {\n        enviar_mail(MAIL_SERVIDOR, MAIL_DESARROLLO, 'ERROR', $texto);\n        if (!$continuar) {\n            exit('<script>document.location = \"'.URL_ERROR.'\";</script>');\n        }\n\n    } else {\n        exit(\"<br /><br />\\r\\n\\r\\n\".$texto);\n    }\n}\n\nfunction enviar_mail($remitente, $destinatario, $asunto, $texto, $copia_remitente = false, $cuenta = 'default', $adjuntos = array())\n{\n    global $modulo;\n\n    if (ESTADO == 'desarrollo')\n        return true;\n\n    if (!$asunto)\n        return false;\n\n    // Revisar si $asunto tiene las palabras con las que se mandó SPAM\n    if (preg_match('/bloqueo|urgente|sospecha/i', $asunto)) {\n        mostrar_error('No se envió un mail porque el asunto contiene palabras prohibidas: '.$asunto, true);\n        return false;\n    }\n\n    if ($modulo != 'crontab' && $modulo != 'script'\n        && isset($_SESSION['empresa_idempresa']) && $_SESSION['empresa_idempresa']) {\n        // Empresas que bloqueamos el envío de mails porque hicieron SPAM\n        if ($_SESSION['empresa_idempresa'] && in_array($_SESSION['empresa_idempresa'], [8902, 2630]))\n            return false;\n\n        // Revisar si en la variable de sesion 'hora_ultimo_mail' se envió un mail hace menos de x segundos\n        if ($_SESSION['hora_ultimo_mail'] && (time() - $_SESSION['hora_ultimo_mail']) < SEGUNDOS_ENTRE_MAILS) {\n            mostrar_error(\"No se envió un mail porque fue hace menos de \".SEGUNDOS_ENTRE_MAILS.\" segundos. El asunto es: \".$asunto, true);\n            return false;\n        }\n        $_SESSION['hora_ultimo_mail'] = time();\n    }\n\n\n    // Si es sqs lo mando a la cola\n    if (($cuenta === 'sqs' || $cuenta === 'default')\n        && function_exists('email_queue'))\n        return email_queue($remitente, $destinatario, $asunto, $texto, $copia_remitente, $adjuntos);\n\n    if ($cuenta === 'default') // No existe funcion email_queue\n        $cuenta = 'no-reply';\n\n\n    // Voy con algún SMTP\n    require_once PATH_TOOLS.'swiftmailer/lib/swift_required.php';\n\n    try {\n\n        switch ($cuenta) {\n            case 'info':\n                $cuenta = array(\n                    'servidor' => 'smtp.gmail.com',\n                    'puerto' => 465,\n                    'seguridad' => 'ssl',\n                    'autenticacion' => 0,\n                    'user' => SMTP_INFO_USER,\n                    'pass' => SMTP_INFO_PASS,\n                    );\n                $texto.= '<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>';\n                break;\n\n            case 'no-reply':\n                $cuenta = array(\n                    'servidor' => 'smtp.gmail.com',\n                    'puerto' => 465,\n                    'seguridad' => 'ssl',\n                    'autenticacion' => 0,\n                    'user' => SMTP_NOREPLY_USER,\n                    'pass' => SMTP_NOREPLY_PASS,\n                    );\n                $texto.= '<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>';\n                break;\n\n            case 'ses':\n                $cuenta = array(\n                    'servidor' => 'email-smtp.sa-east-1.amazonaws.com',\n                    'puerto' => 587,\n                    'seguridad' => 'tls',\n                    'autenticacion' => 0,\n                    'user' => SMTP_SES_USER,\n                    'pass' => SMTP_SES_PASS,\n                    );\n                $copia_remitente = false;\n                if (is_array($remitente)\n                    && count($remitente) > 1\n                    && $remitente[0] == MAIL_SERVIDOR) {\n                    $responder = $remitente[1];\n                    $remitente = MAIL_SERVIDOR;\n                } else {\n                    $responder = $remitente;\n                }\n                $texto.= '<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>';\n                break;\n\n            default:\n                if (!(is_array($cuenta)\n                    && isset($cuenta['servidor'])\n                    && isset($cuenta['puerto'])\n                    && isset($cuenta['seguridad'])\n                    && isset($cuenta['autenticacion'])\n                    && isset($cuenta['user'])\n                    && isset($cuenta['pass'])\n                    ))\n                    mostrar_error('Función enviar_mail con parámetro cuenta incorrecto: '.json_encode($cuenta), true);\n                break;\n        }\n\n        // Si tengo más de un destinatario, el segundo va como copia y el resto lo borro\n        // porque no queremos que se use como sistema de newsletter (tampoco mantengo el nombre)\n        if (is_array($destinatario)\n            && count($destinatario) > 1\n            && $destinatario[0] != MAIL_INFO) {\n            $copia = $destinatario[1];\n            $destinatario = array($destinatario[0]);\n        } else {\n            $copia = false;\n        }\n\n        if (!isset($responder))\n            $responder = $remitente;\n\n        $transporte = Swift_SmtpTransport::newInstance($cuenta['servidor'], $cuenta['puerto'], $cuenta['seguridad'])\n            -> setUsername($cuenta['user'])\n            -> setPassword($cuenta['pass']);\n\n        $envio = Swift_Message::newInstance()\n            -> setFrom($remitente)\n            -> setSender($remitente)\n            -> setReplyTo($responder)\n            -> setTo($destinatario)\n            -> setSubject($asunto)\n            -> setContentType('text/html')\n            -> setBody($texto, 'text/html');\n        if ($copia)\n            $envio -> setCc($copia);\n        if ($copia_remitente)\n            $envio -> setBcc($responder);\n\n        foreach ($adjuntos as $adjunto) {\n            switch ($adjunto['tipo']) {\n                default:\n                case 'archivo':\n                    $envio -> attach(Swift_Attachment::fromPath($adjunto['nombre']));\n                    break;\n\n                case 'html':\n                    $envio -> attach(Swift_Attachment::newInstance()\n                        -> setFilename($adjunto['nombre'])\n                        -> setContentType('text/html')\n                        -> setBody($adjunto['valor'])\n                        );\n                    break;\n\n                case 'pdf':\n                    $envio -> attach(Swift_Attachment::newInstance()\n                        -> setFilename($adjunto['nombre'])\n                        -> setContentType('application/pdf')\n                        -> setBody($adjunto['valor'])\n                        );\n                    break;\n\n                case 'planilla':\n                    $envio -> attach(Swift_Attachment::newInstance()\n                        -> setFilename($adjunto['nombre'])\n                        -> setContentType('application/vnd.ms-excel')\n                        -> setBody($adjunto['valor'])\n                        );\n                    break;\n            }\n        }\n\n        return Swift_Mailer::newInstance($transporte) -> send($envio);\n\n    } catch (Exception $e) {\n        $separador = ';';\n        file_put_contents(PATH_LOGS . 'mail_caido.csv',\n            date(\"Y-m-d H:i:s\") . $separador\n            . 'ERROR' . $separador\n            . $_SESSION['empresa_idempresa'] . $separador\n            . $cuenta['user'] . $separador\n            . str_replace($separador, ',', $e->getMessage()) . \"\\r\\n\"\n            , FILE_APPEND);\n    }\n}\n\nfunction generar_random($tamaño = 50) {\n    $chars = \"abcdefghijkmnopqrstuvwxyz023456789ABCDEFGHIJKMNOPQRSTUVWXYZ\";\n    $random = '';\n    for($i = 0; $i < $tamaño; $i++)\n        $random.= $chars[rand(0, 58)];\n\n    return $random;\n}\n\nfunction redondeo($numero)\n{\n    return (isset($_SESSION['control_formato_separador_miles']) && $_SESSION['control_formato_separador_miles'])\n        ? number_format(round($numero, 2), 2, ',', '.')\n        : number_format(round($numero, 2), 2, '.', '');\n}\n\nfunction responder_json($estado = 'ok', $datos = array()) {\n    $respuesta = array(\n        'estado' => $estado\n        );\n    foreach ($datos as $key => $value) {\n        $respuesta[$key] = $value;\n    }\n\n    header('Content-type: application/json');\n    if ($estado == 'error')\n        http_response_code(400);\n\n    $callback = filter_input(INPUT_GET, 'callback');\n    echo ($callback ? $callback . '(' . json_encode($respuesta) . ')' : json_encode($respuesta));\n    exit();\n}\n\nfunction listar_meses($desde, $hasta = false)\n{\n    if (!$hasta)\n        $hasta = date(\"Y-m-d\");\n    $meses = array();\n    $desde_destripado = explode('-', substr($desde, 0, 7)); // Separo solo la fecha por si acaso trae la hora\n    $hasta_destripado = explode('-', substr($hasta, 0, 7));\n\n    $desde_año = (int)$desde_destripado[0];\n    $desde_mes = (int)$desde_destripado[1];\n    $hasta_año = (int)$hasta_destripado[0];\n    $hasta_mes = (int)$hasta_destripado[1];\n\n    while ($desde_año <= $hasta_año) {\n        while (($desde_año < $hasta_año && $desde_mes < 13)\n            || ($desde_año == $hasta_año && $desde_mes <= $hasta_mes && $desde_mes < 13)) {\n            $meses[] = completar_numero($desde_mes, 2) . '-' . completar_numero($desde_año, 4);\n            $desde_mes++;\n        }\n        $desde_mes = 1;\n        $desde_año++;\n    }\n\n    return $meses;\n}\n\nfunction vacios($cantidad) {\n    $temp = '';\n    for ($i = 0; $i < $cantidad; $i++) {\n        $temp.= ' ';\n    }\n    return $temp;\n}\n\nfunction ceros($cant)\n{\n    $return = '';\n    for ($i = 0; $i < $cant; $i++)\n        $return.= '0';\n    return $return;\n}\n\nfunction completar_texto($valor, $digitos)\n{\n    $return = '';\n    $largo_valor = mb_strlen($valor, 'UTF-8');\n    if ($largo_valor > $digitos) {\n        $return = mb_substr($valor, 0, $digitos);\n    } else {\n        $return = $valor;\n        for ($i = 0; $i < ($digitos - $largo_valor); $i++) {\n            $return.= ' ';\n        }\n    }\n    return $return;\n}\n\nfunction completar_numero($valor, $digitos)\n{\n    if ($valor == 0) {\n        $return = '';\n        for($j = 0; $j < $digitos; $j++)\n            $return.='0';\n\n        return $return;\n    }\n\n    $negativo = $valor < 0;\n\n    if ($negativo) {\n        $valor = $valor * -1;\n        $digitos--;\n    }\n\n    $valor = \"$valor\";\n    $return = '';\n    $largo_valor = strlen($valor);\n    if ($largo_valor > $digitos) {\n        $return = substr($valor, 0, $digitos);\n    } else {\n        $return = $valor;\n        for ($i = 0; $i < ($digitos - $largo_valor); $i++) {\n            $return = '0'.$return;\n        }\n    }\n    return ($negativo ? '-' : '') . $return;\n}\n\nfunction numero_comprobante($letra, $puntodeventa, $numero)\n{\n    return $letra.completar_numero($puntodeventa, 5).'-'.completar_numero($numero, 8);\n}\n\nfunction url_amigable($valor) {\n    $separador = '-';\n    $valor = strtolower($valor);\n    $valor = str_replace(\n        array('á', 'é', 'í', 'ó', 'ú', 'ñ', '&', '+', '-', '_', \"\\r\", \"\\r\\n\", \"\\n\"),\n        array('a', 'e', 'i', 'o', 'u', 'n', 'y', 'y', ' ', ' ', ' ', ' ', ' '),\n        $valor);\n    $valor = trim(preg_replace(\"/[^ a-z0-9]/\", \"\", $valor));\n    $valor = str_replace(' ', $separador, $valor);\n\n    return $valor;\n}\n\n\nfunction sanear_string($string)\n{\n\n    $string = trim($string);\n\n    $string = str_replace(\n        array('á', 'à', 'ä', 'â', 'ª', 'Á', 'À', 'Â', 'Ä'),\n        array('a', 'a', 'a', 'a', 'a', 'A', 'A', 'A', 'A'),\n        $string\n    );\n\n    $string = str_replace(\n        array('é', 'è', 'ë', 'ê', 'É', 'È', 'Ê', 'Ë'),\n        array('e', 'e', 'e', 'e', 'E', 'E', 'E', 'E'),\n        $string\n    );\n\n    $string = str_replace(\n        array('í', 'ì', 'ï', 'î', 'Í', 'Ì', 'Ï', 'Î'),\n        array('i', 'i', 'i', 'i', 'I', 'I', 'I', 'I'),\n        $string\n    );\n\n    $string = str_replace(\n        array('ó', 'ò', 'ö', 'ô', 'Ó', 'Ò', 'Ö', 'Ô'),\n        array('o', 'o', 'o', 'o', 'O', 'O', 'O', 'O'),\n        $string\n    );\n\n    $string = str_replace(\n        array('ú', 'ù', 'ü', 'û', 'Ú', 'Ù', 'Û', 'Ü'),\n        array('u', 'u', 'u', 'u', 'U', 'U', 'U', 'U'),\n        $string\n    );\n\n    $string = str_replace(\n        array('ñ', 'Ñ', 'ç', 'Ç'),\n        array('n', 'N', 'c', 'C',),\n        $string\n    );\n\n    //Esta parte se encarga de eliminar cualquier caracter extraño\n    $string = str_replace(\n        array(\"¨\", \"º\", \"~\",\n             \"#\", \"@\", \"|\", \"!\",\n             \"·\", \"$\", \"%\", \"&\", \"/\",\n             \"?\", \"'\", \"¡\",\n             \"¿\", \"[\", \"^\", \"<code>\", \"]\",\n             \"+\", \"}\", \"{\", \"¨\", \"´\",\n             \">\", \"< \", \";\", \",\", \":\",\n             \".\"),\n        '',\n        $string\n    );\n\n    return $string;\n}\n\nfunction calcular_cuit($dni, $codigo_sexo) {\n\n    $dni = completar_numero($dni, 8);\n    $multiplicadores = Array('3', '2', '7','6', '5', '4', '3', '2');\n    $calculo = (substr($codigo_sexo, 0, 1) * 5) + (substr($codigo_sexo, 1, 1) * 4);\n\n    for ( $i=0 ; $i<8 ; $i++ ) {\n        $calculo += substr($dni,$i,1) * $multiplicadores[$i];\n    }\n\n    $resto = ($calculo) % 11;\n\n    if (($codigo_sexo != '30') && ($resto <= 1)) {\n        if ($resto == 0) {\n            $ultimo_digito_cuit = '0';\n        } else {\n            if ($codigo_sexo == 20) {\n                $ultimo_digito_cuit = '9';\n            } else {\n                $ultimo_digito_cuit = '4';\n            }\n        }\n        $codigo_sexo = '23';\n    } else {\n        $ultimo_digito_cuit = 11 - $resto;\n    }\n\n    return $codigo_sexo . $dni . $ultimo_digito_cuit;\n}\n\nfunction escape_no_sql($unescaped) {\n    $replacements = array(\n        \"\\x00\"=>'\\x00',\n        \"\\n\"=>'\\n',\n        \"\\r\"=>'\\r',\n        \"\\\\\"=>'\\\\\\\\',\n        \"'\"=>\"\\'\",\n        '\"'=>'\\\"',\n        \"\\x1a\"=>'\\x1a',\n    );\n    return strtr($unescaped, $replacements);\n}\n\nfunction queue_ml($url, $body)\n{\n    file_put_contents(PATH_LOGS.'notificaciones/'.date(\"Y-m-d_H-i\").'.log',\n        date(\"Y-m-d_H-i-s\")\n        . '|||'\n        . $url\n        . '|||'\n        . $body\n        . \"\\r\\n\"\n        , FILE_APPEND);\n}\n\nfunction en_rfce($cuit)\n{\n    $cuit = (string)$cuit;\n    if (strlen($cuit) != 11)\n        return false;\n\n    $rfce_listado = file_get_contents(__DIR__.'/rfce_listado.txt');\n    return strpos($rfce_listado, $cuit) === false\n        ? false\n        : true;\n}\n\nfunction mes($idmes)\n{\n    $mes = array(\n        1 => 'Enero',\n        2 => 'Febrero',\n        3 => 'Marzo',\n        4 => 'Abril',\n        5 => 'Mayo',\n        6 => 'Junio',\n        7 => 'Julio',\n        8 => 'Agosto',\n        9 => 'Septiembre',\n        10 => 'Octubre',\n        11 => 'Noviembre',\n        12 => 'Diciembre'\n        );\n    return $mes[$idmes];\n}\n\nfunction es_franquiciado($idempresa, $franquicia = false)\n{\n    switch ($franquicia) {\n        default: // Todos los franquiciados\n            if (in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664]))\n                return 874;\n            else\n                return false;\n            break;\n\n        case 'gaming': // Franquicia de Gaming City\n        case 874:\n            return in_array($idempresa, [761, 3983, 6551, 9432, 9589, 9746, 10047, 10552, 10645, 10762, 11061, 11166, 11219, 11220, 11664]);\n            break;\n    }\n}\n\nfunction sin_control_numeracion($idempresa)\n{\n    // Empresas anteriores que ya arreglaron la numeración\n    // 8142, 7398, 2443, 301, 798, 5914, 8828, 10008\n    // Empresas excluidas del control de numeración porque usaron otro sistema\n    return in_array($idempresa, [8905, 11597]);\n}\n\nfunction sin_fullsearch($idempresa)\n{\n    $empresas_sin_fullsearch = [];\n    return in_array($idempresa, $empresas_sin_fullsearch);\n}\n\nfunction tipoDocs($id = false, $sin_cuit = false) {\n    $tipoDocs = [\n        ['id' => 80, 'valor' => 'CUIT'],\n        ['id' => 96, 'valor' => 'DNI'],\n        ['id' => 87, 'valor' => 'CDI'],\n        ['id' => 91, 'valor' => 'CI Extranjera'],\n        ['id' => 94, 'valor' => 'Pasaporte'],\n        ['id' => 99, 'valor' => 'Otro']\n    ];\n\n    if ($id) {\n        foreach ($tipoDocs as $tipoDoc) {\n            if ($tipoDoc['id'] == $id) {\n                return $tipoDoc['valor'];\n            }\n        }\n    }\n\n    if ($sin_cuit) {\n        unset($tipoDocs[0]);\n        unset($tipoDocs[5]);\n    }\n\n    return $tipoDocs;\n}\n"}]}