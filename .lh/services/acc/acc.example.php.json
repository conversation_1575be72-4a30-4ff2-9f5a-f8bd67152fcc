{"sourceFile": "services/acc/acc.example.php", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1744312361130, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750445359126, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -74,9 +74,15 @@\n define('AWS_BUCKET', 'saasargentina');\n define('AWS_WSFE', 'saasargentina-wsfe');\n define('AWS_BACKUPS', 'saasargentina-backups');\n define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n-define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\n+\n+// URLs de colas SQS para AFIP SDK por ambiente\n+define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue'); // Retrocompatibilidad\n+define('AWS_URL_AFIPSDK_QUEUE_ALFA', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-alfa');\n+define('AWS_URL_AFIPSDK_QUEUE_BETA', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-beta');\n+define('AWS_URL_AFIPSDK_QUEUE_PROD', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-prod');\n+\n define('AWS_URL_WS_SR_PADRON', 'https://7z8k00qyi1.execute-api.sa-east-1.amazonaws.com/');\n define('AWS_SQS_QUEUER_KEY', '');\n define('AWS_SQS_QUEUER_SECRET', '');\n \n"}, {"date": 1750459192164, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -74,15 +74,9 @@\n define('AWS_BUCKET', 'saasargentina');\n define('AWS_WSFE', 'saasargentina-wsfe');\n define('AWS_BACKUPS', 'saasargentina-backups');\n define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n-\n-// URLs de colas SQS para AFIP SDK por ambiente\n-define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue'); // Retrocompatibilidad\n-define('AWS_URL_AFIPSDK_QUEUE_ALFA', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-alfa');\n-define('AWS_URL_AFIPSDK_QUEUE_BETA', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-beta');\n-define('AWS_URL_AFIPSDK_QUEUE_PROD', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-prod');\n-\n+define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\n define('AWS_URL_WS_SR_PADRON', 'https://7z8k00qyi1.execute-api.sa-east-1.amazonaws.com/');\n define('AWS_SQS_QUEUER_KEY', '');\n define('AWS_SQS_QUEUER_SECRET', '');\n \n"}], "date": 1744312361130, "name": "Commit-0", "content": "<?php\n/**\n * En este archivo se configuran todas las variables de entorno, el archivo original es acc.example.php, que una vez editado debe ser renombrado a acc.php.\n */\n\nrequire_once __DIR__.'/defines.php'; // Constantes definidas por código\nrequire_once __DIR__.'/constantes.php'; // Constantes editables por el usuario\nrequire_once __DIR__.'/funciones_basicas.php';\n\n\n// Estados posibles del sistema: desarrollo, control, produccion, offline\ndefine('ESTADO', 'desarrollo');\n// En caso de estar el sistema offline, se muestra el siguiente mensaje en el login\ndefine('MENSAJE', 'Estamos realizando una actualización programada y el sistema estará offline por unos minutos');\n\n// URLs varias sin / al final\ndefine('URL_HOST', 'www.saasargentina.com');\ndefine('URL_COOKIE', '.saasargentina.com');\ndefine('URL_SITE', 'https://www.saasargentina.com');\ndefine('URL_LOGIN', 'https://www.saasargentina.com/login');\ndefine('URL_SAAS', 'https://app.saasargentina.com');\ndefine('URL_BETA', 'https://beta.saasargentina.com');\ndefine('URL_ALFA', 'https://alfa.saasargentina.com');\ndefine('URL_API', 'https://api.saasargentina.com');\ndefine('URL_API_BETA', 'https://api-beta.saasargentina.com');\ndefine('URL_API_ALFA', 'https://api-alfa.saasargentina.com');\ndefine('URL_API_LOGIN', 'https://login.saasargentina.com');\ndefine('URL_INFORMES', 'https://informes.saasargentina.com');\ndefine('URL_INFORMES_BETA', 'https://informes-beta.saasargentina.com');\ndefine('URL_INFORMES_ALFA', 'https://informes-alfa.saasargentina.com');\ndefine('URL_SCRIPTS', 'https://scripts.saasargentina.com');\ndefine('URL_TIENDA', 'https://tienda.saasargentina.com');\ndefine('URL_ERROR', 'https://www.saasargentina.com/error');\ndefine('URL_S3', 'https://s3-sa-east-1.amazonaws.com/saasargentina/');\ndefine('URL_DESCARGAS', 'https://s3-sa-east-1.amazonaws.com/saasargentina-descargas/');\n\n// PATH varios con / al final\ndefine('PATH_SAAS', '/saas/customer/services/saas/');\ndefine('PATH_BETA', '/saas/customer/services/beta/');\ndefine('PATH_ALFA', '/saas/customer/services/alfa/');\ndefine('PATH_ACC', '/saas/customer/services/acc/');\ndefine('PATH_SCRIPTS', '/saas/customer/services/scripts/');\ndefine('PATH_TOOLS', '/saas/customer/services/acc/tools/');\ndefine('PATH_ARCHIVOS', '/saas/customer/services/acc/archivos/');\ndefine('PATH_WSFE', '/saas/customer/services/acc/empresas/wsfe/');\ndefine('PATH_LOGS', '/saas/customer/services/acc/empresas/logs/');\ndefine('PATH_ELIMINADAS', '/home/<USER>/www/saasargentina/services/acc/empresas/eliminadas/');\ndefine('PATH_BACKUPS', '/saas/customer/services/acc/backups/');\n\n// Base de datos principal\ndefine('BD_HOST', '127.0.0.1');\ndefine('BD_USER', 'root');\ndefine('BD_PASS', '8des4rollo');\ndefine('BD_BD', 'saasargentina');\ndefine('BD_TABLAS', 'tablas');\ndefine('BD_PORT', 0); // Es importante que sea 0 y no ''\ndefine('BD_SOCKET', '');\n\n// Datos de cuentas de Google Apps para enviar mails propios\ndefine('SMTP_INFO_NOMBRE', 'SaaS Argentina');\ndefine('SMTP_INFO_USER', '<EMAIL>');\ndefine('SMTP_INFO_PASS', '');\ndefine('SMTP_NOREPLY_USER', '<EMAIL>');\ndefine('SMTP_NOREPLY_NOMBRE', '[NO RESPONDER] SaaS Argentina');\ndefine('SMTP_NOREPLY_PASS', '');\ndefine('SMTP_SES_USER', '');\ndefine('SMTP_SES_PASS', '');\ndefine('SMTP_PW', '0a422d7698940b3225a5b8936425da10');\n\n// Variables de acceso a Amazon AWS\ndefine('AWS_KEY', '');\ndefine('AWS_SECRET', '');\ndefine('AWS_REGION', 'sa-east-1');\ndefine('AWS_BUCKET', 'saasargentina');\ndefine('AWS_WSFE', 'saasargentina-wsfe');\ndefine('AWS_BACKUPS', 'saasargentina-backups');\ndefine('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\ndefine('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\ndefine('AWS_URL_WS_SR_PADRON', 'https://7z8k00qyi1.execute-api.sa-east-1.amazonaws.com/');\ndefine('AWS_SQS_QUEUER_KEY', '');\ndefine('AWS_SQS_QUEUER_SECRET', '');\n\n\n// Variables de las aplicaciones de Mercado Libre\n//Dejo este mío, por si no hay otros\ndefine('MP_ACCESS_TOKEN', 'TEST-7940098014182402-120716-f83a2aaf3a19dd12756e607c71c20912__LC_LA__-76076010');\ndefine('ML_APP_ID', '6366624061377005');\ndefine('ML_BETA_ID', '2905939953172097');\ndefine('ML_ALFA_ID', '3710461931597197');\ndefine('ML_APP_SECRET', '');\ndefine('ML_BETA_SECRET', '');\ndefine('ML_ALFA_SECRET', '');\ndefine('ML_USER_ID_TEST', '236848119');\n"}]}