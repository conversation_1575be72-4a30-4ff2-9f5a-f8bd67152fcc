{"sourceFile": "services/acc/acc.php", "activeCommit": 0, "commits": [{"activePatchIndex": 8, "patches": [{"date": 1744052411773, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1744294329164, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -77,8 +77,9 @@\n define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\n define('AWS_SQS_QUEUER_KEY', '********************');\n define('AWS_SQS_QUEUER_SECRET', 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO');\n+define('AWS_URL_WS_SR_PADRON', 'https://7z8k00qyi1.execute-api.sa-east-1.amazonaws.com/');\n \n \n // Variables de las aplicaciones de Mercado Libre\n //Dejo este mío, por si no hay otros\n"}, {"date": 1744312368128, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -75,11 +75,11 @@\n define('AWS_WSFE', 'saasargentina-wsfe');\n define('AWS_BACKUPS', 'saasargentina-backups');\n define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\n+define('AWS_URL_WS_SR_PADRON', 'https://7z8k00qyi1.execute-api.sa-east-1.amazonaws.com/');\n define('AWS_SQS_QUEUER_KEY', '********************');\n define('AWS_SQS_QUEUER_SECRET', 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO');\n-define('AWS_URL_WS_SR_PADRON', 'https://7z8k00qyi1.execute-api.sa-east-1.amazonaws.com/');\n \n \n // Variables de las aplicaciones de Mercado Libre\n //Dejo este mío, por si no hay otros\n"}, {"date": 1744313056758, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -75,9 +75,9 @@\n define('AWS_WSFE', 'saasargentina-wsfe');\n define('AWS_BACKUPS', 'saasargentina-backups');\n define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\n-define('AWS_URL_WS_SR_PADRON', 'https://7z8k00qyi1.execute-api.sa-east-1.amazonaws.com/');\n+define('AWS_URL_WS_SR_PADRON', 'https://7z8k00qyi1.execute-api.sa-east-1.amazonaws.com/dev/');\n define('AWS_SQS_QUEUER_KEY', '********************');\n define('AWS_SQS_QUEUER_SECRET', 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO');\n \n \n"}, {"date": 1744313209685, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -75,9 +75,9 @@\n define('AWS_WSFE', 'saasargentina-wsfe');\n define('AWS_BACKUPS', 'saasargentina-backups');\n define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\n-define('AWS_URL_WS_SR_PADRON', 'https://7z8k00qyi1.execute-api.sa-east-1.amazonaws.com/dev/');\n+define('AWS_URL_WS_SR_PADRON', 'https://eq6bd27cmj.execute-api.sa-east-1.amazonaws.com/dev/');\n define('AWS_SQS_QUEUER_KEY', '********************');\n define('AWS_SQS_QUEUER_SECRET', 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO');\n \n \n"}, {"date": 1748890584742, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,93 @@\n+<?php\n+/**\n+ * En este archivo se configuran todas las variables de entorno, el archivo original es acc.example.php, que una vez editado debe ser renombrado a acc.php.\n+ */\n+\n+require_once __DIR__.'/defines.php'; // Constantes definidas por código\n+require_once __DIR__.'/constantes.php'; // Constantes editables por el usuario\n+require_once __DIR__.'/funciones_basicas.php';\n+\n+\n+// Estados posibles del sistema: desarrollo, control, produccion, offline\n+define('ESTADO', 'desarrollo');\n+// En caso de estar el sistema offline, se muestra el siguiente mensaje en el login\n+define('MENSAJE', 'Estamos realizando una actualización programada y el sistema estará offline por unos minutos');\n+\n+// URLs varias\n+define('URL_HOST', 'www.saasargentina.des');\n+define('URL_COOKIE', '.saasargentina.des');\n+define('URL_SITE', 'http://saasargentina.des');\n+define('URL_LOGIN', 'http://saasargentina.des/login');\n+define('URL_SAAS', 'http://app.saasargentina.des');\n+define('URL_BETA', 'http://app.saasargentina.des');\n+define('URL_ALFA', 'http://app.saasargentina.des');\n+define('URL_API', 'http://api.saasargentina.des');\n+define('URL_API_BETA', 'http://api.saasargentina.des');\n+define('URL_API_ALFA', 'http://api.saasargentina.des');\n+define('URL_API_LOGIN', 'http://login.saasargentina.des');\n+define('URL_INFORMES', 'http://informes.saasargentina.des');\n+define('URL_INFORMES_BETA', 'http://informes.saasargentina.des');\n+define('URL_INFORMES_ALFA', 'http://informes.saasargentina.des');\n+define('URL_SCRIPTS', 'http://scripts.saasargentina.des');\n+define('URL_TIENDA', 'http://tienda.saasargentina.des');\n+define('URL_ERROR', 'https://www.saasargentina.des/error');\n+define('URL_S3', 'https://s3-sa-east-1.amazonaws.com/saasargentina/');\n+define('URL_DESCARGAS', 'https://s3-sa-east-1.amazonaws.com/saasargentina-descargas/');\n+\n+// PATH varios\n+define('PATH_SAAS', '/home/<USER>/www/saasargentina/services/app/');\n+define('PATH_BETA', '/home/<USER>/www/saasargentina/services/app/');\n+define('PATH_ALFA', '/home/<USER>/www/saasargentina/services/app/');\n+define('PATH_ACC', '/home/<USER>/www/saasargentina/services/acc/');\n+define('PATH_SCRIPTS', '/home/<USER>/www/saasargentina/services/scripts/');\n+define('PATH_TOOLS', '/home/<USER>/www/saasargentina/services/acc/tools/');\n+define('PATH_ARCHIVOS', '/home/<USER>/www/saasargentina/services/acc/archivos/');\n+define('PATH_WSFE', '/home/<USER>/www/saasargentina/services/acc/empresas/wsfe/');\n+define('PATH_LOGS', '/home/<USER>/www/saasargentina/services/acc/empresas/logs/');\n+define('PATH_ELIMINADAS', '/home/<USER>/www/saasargentina/services/acc/empresas/eliminadas/');\n+define('PATH_BACKUPS', '/home/<USER>/www/saasargentina/services/acc/backups/');\n+\n+// Base de datos principal\n+define('BD_HOST', '127.0.0.1');\n+define('BD_USER', 'yosoyroot');\n+define('BD_PASS', '8des4rollo');\n+define('BD_BD', 'saasargentina');\n+define('BD_TABLAS', 'tablas');\n+define('BD_PORT', 0); // Es importante que sea 0 y no ''\n+define('BD_SOCKET', '');\n+\n+// Datos de cuentas de Google Apps para enviar mails propios\n+define('SMTP_INFO_NOMBRE', 'SaaS Argentina');\n+define('SMTP_INFO_USER', '<EMAIL>');\n+define('SMTP_INFO_PASS', '');\n+define('SMTP_NOREPLY_USER', '<EMAIL>');\n+define('SMTP_NOREPLY_NOMBRE', '[NO RESPONDER] SaaS Argentina');\n+define('SMTP_NOREPLY_PASS', 'FUiFxYRtgr6FPsRvkWnFTwUWOxgr9f6p');\n+define('SMTP_SES_USER', '********************');\n+define('SMTP_SES_PASS', 'BI6YyhCX6SiFHGKRdJDX+xRzwNekMBAqNFkA15tyryMn');\n+define('SMTP_PW', '0a422d7698940b3225a5b8936425da10');\n+\n+// Variables de acceso a Amazon AWS\n+define('AWS_KEY', '********************');\n+define('AWS_SECRET', 'ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+');\n+define('AWS_REGION', 'sa-east-1');\n+define('AWS_BUCKET', 'saasargentina');\n+define('AWS_WSFE', 'saasargentina-wsfe');\n+define('AWS_BACKUPS', 'saasargentina-backups');\n+define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n+define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev');\n+define('AWS_URL_WS_SR_PADRON', 'https://eq6bd27cmj.execute-api.sa-east-1.amazonaws.com/dev/');\n+define('AWS_SQS_QUEUER_KEY', '********************');\n+define('AWS_SQS_QUEUER_SECRET', 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO');\n+\n+\n+// Variables de las aplicaciones de Mercado Libre\n+//Dejo este mío, por si no hay otros\n+define('MP_ACCESS_TOKEN', 'TEST-7940098014182402-120716-f83a2aaf3a19dd12756e607c71c20912__LC_LA__-76076010');\n+define('ML_APP_ID', '6366624061377005');\n+define('ML_BETA_ID', '2905939953172097');\n+define('ML_ALFA_ID', '3710461931597197');\n+define('ML_APP_SECRET', '');\n+define('ML_BETA_SECRET', '');\n+define('ML_ALFA_SECRET', '');\n+define('ML_USER_ID_TEST', '236848119');\n"}, {"date": 1750445358780, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -74,102 +74,15 @@\n define('AWS_BUCKET', 'saasargentina');\n define('AWS_WSFE', 'saasargentina-wsfe');\n define('AWS_BACKUPS', 'saasargentina-backups');\n define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n-define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev');\n-define('AWS_URL_WS_SR_PADRON', 'https://eq6bd27cmj.execute-api.sa-east-1.amazonaws.com/dev/');\n-define('AWS_SQS_QUEUER_KEY', '********************');\n-define('AWS_SQS_QUEUER_SECRET', 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO');\n \n+// URLs de colas SQS para AFIP SDK por ambiente\n+define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev'); // Retrocompatibilidad\n+define('AWS_URL_AFIPSDK_QUEUE_ALFA', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-alfa');\n+define('AWS_URL_AFIPSDK_QUEUE_BETA', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-beta');\n+define('AWS_URL_AFIPSDK_QUEUE_PROD', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-prod');\n \n-// Variables de las aplicaciones de Mercado Libre\n-//Dejo este mío, por si no hay otros\n-define('MP_ACCESS_TOKEN', 'TEST-7940098014182402-120716-f83a2aaf3a19dd12756e607c71c20912__LC_LA__-76076010');\n-define('ML_APP_ID', '6366624061377005');\n-define('ML_BETA_ID', '2905939953172097');\n-define('ML_ALFA_ID', '3710461931597197');\n-define('ML_APP_SECRET', '');\n-define('ML_BETA_SECRET', '');\n-define('ML_ALFA_SECRET', '');\n-define('ML_USER_ID_TEST', '236848119');\n-<?php\n-/**\n- * En este archivo se configuran todas las variables de entorno, el archivo original es acc.example.php, que una vez editado debe ser renombrado a acc.php.\n- */\n-\n-require_once __DIR__.'/defines.php'; // Constantes definidas por código\n-require_once __DIR__.'/constantes.php'; // Constantes editables por el usuario\n-require_once __DIR__.'/funciones_basicas.php';\n-\n-\n-// Estados posibles del sistema: desarrollo, control, produccion, offline\n-define('ESTADO', 'desarrollo');\n-// En caso de estar el sistema offline, se muestra el siguiente mensaje en el login\n-define('MENSAJE', 'Estamos realizando una actualización programada y el sistema estará offline por unos minutos');\n-\n-// URLs varias\n-define('URL_HOST', 'www.saasargentina.des');\n-define('URL_COOKIE', '.saasargentina.des');\n-define('URL_SITE', 'http://saasargentina.des');\n-define('URL_LOGIN', 'http://saasargentina.des/login');\n-define('URL_SAAS', 'http://app.saasargentina.des');\n-define('URL_BETA', 'http://app.saasargentina.des');\n-define('URL_ALFA', 'http://app.saasargentina.des');\n-define('URL_API', 'http://api.saasargentina.des');\n-define('URL_API_BETA', 'http://api.saasargentina.des');\n-define('URL_API_ALFA', 'http://api.saasargentina.des');\n-define('URL_API_LOGIN', 'http://login.saasargentina.des');\n-define('URL_INFORMES', 'http://informes.saasargentina.des');\n-define('URL_INFORMES_BETA', 'http://informes.saasargentina.des');\n-define('URL_INFORMES_ALFA', 'http://informes.saasargentina.des');\n-define('URL_SCRIPTS', 'http://scripts.saasargentina.des');\n-define('URL_TIENDA', 'http://tienda.saasargentina.des');\n-define('URL_ERROR', 'https://www.saasargentina.des/error');\n-define('URL_S3', 'https://s3-sa-east-1.amazonaws.com/saasargentina/');\n-define('URL_DESCARGAS', 'https://s3-sa-east-1.amazonaws.com/saasargentina-descargas/');\n-\n-// PATH varios\n-define('PATH_SAAS', '/home/<USER>/www/saasargentina/services/app/');\n-define('PATH_BETA', '/home/<USER>/www/saasargentina/services/app/');\n-define('PATH_ALFA', '/home/<USER>/www/saasargentina/services/app/');\n-define('PATH_ACC', '/home/<USER>/www/saasargentina/services/acc/');\n-define('PATH_SCRIPTS', '/home/<USER>/www/saasargentina/services/scripts/');\n-define('PATH_TOOLS', '/home/<USER>/www/saasargentina/services/acc/tools/');\n-define('PATH_ARCHIVOS', '/home/<USER>/www/saasargentina/services/acc/archivos/');\n-define('PATH_WSFE', '/home/<USER>/www/saasargentina/services/acc/empresas/wsfe/');\n-define('PATH_LOGS', '/home/<USER>/www/saasargentina/services/acc/empresas/logs/');\n-define('PATH_ELIMINADAS', '/home/<USER>/www/saasargentina/services/acc/empresas/eliminadas/');\n-define('PATH_BACKUPS', '/home/<USER>/www/saasargentina/services/acc/backups/');\n-\n-// Base de datos principal\n-define('BD_HOST', '127.0.0.1');\n-define('BD_USER', 'yosoyroot');\n-define('BD_PASS', '8des4rollo');\n-define('BD_BD', 'saasargentina');\n-define('BD_TABLAS', 'tablas');\n-define('BD_PORT', 0); // Es importante que sea 0 y no ''\n-define('BD_SOCKET', '');\n-\n-// Datos de cuentas de Google Apps para enviar mails propios\n-define('SMTP_INFO_NOMBRE', 'SaaS Argentina');\n-define('SMTP_INFO_USER', '<EMAIL>');\n-define('SMTP_INFO_PASS', '');\n-define('SMTP_NOREPLY_USER', '<EMAIL>');\n-define('SMTP_NOREPLY_NOMBRE', '[NO RESPONDER] SaaS Argentina');\n-define('SMTP_NOREPLY_PASS', 'FUiFxYRtgr6FPsRvkWnFTwUWOxgr9f6p');\n-define('SMTP_SES_USER', '********************');\n-define('SMTP_SES_PASS', 'BI6YyhCX6SiFHGKRdJDX+xRzwNekMBAqNFkA15tyryMn');\n-define('SMTP_PW', '0a422d7698940b3225a5b8936425da10');\n-\n-// Variables de acceso a Amazon AWS\n-define('AWS_KEY', '********************');\n-define('AWS_SECRET', 'ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+');\n-define('AWS_REGION', 'sa-east-1');\n-define('AWS_BUCKET', 'saasargentina');\n-define('AWS_WSFE', 'saasargentina-wsfe');\n-define('AWS_BACKUPS', 'saasargentina-backups');\n-define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n-define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\n define('AWS_URL_WS_SR_PADRON', 'https://eq6bd27cmj.execute-api.sa-east-1.amazonaws.com/dev/');\n define('AWS_SQS_QUEUER_KEY', '********************');\n define('AWS_SQS_QUEUER_SECRET', 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO');\n \n"}, {"date": 1750459166737, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -76,12 +76,9 @@\n define('AWS_BACKUPS', 'saasargentina-backups');\n define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n \n // URLs de colas SQS para AFIP SDK por ambiente\n-define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev'); // Retrocompatibilidad\n-define('AWS_URL_AFIPSDK_QUEUE_ALFA', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-alfa');\n-define('AWS_URL_AFIPSDK_QUEUE_BETA', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-beta');\n-define('AWS_URL_AFIPSDK_QUEUE_PROD', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-prod');\n+define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\n \n define('AWS_URL_WS_SR_PADRON', 'https://eq6bd27cmj.execute-api.sa-east-1.amazonaws.com/dev/');\n define('AWS_SQS_QUEUER_KEY', '********************');\n define('AWS_SQS_QUEUER_SECRET', 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO');\n"}, {"date": 1750459198648, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -74,12 +74,9 @@\n define('AWS_BUCKET', 'saasargentina');\n define('AWS_WSFE', 'saasargentina-wsfe');\n define('AWS_BACKUPS', 'saasargentina-backups');\n define('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\n-\n-// URLs de colas SQS para AFIP SDK por ambiente\n define('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\n-\n define('AWS_URL_WS_SR_PADRON', 'https://eq6bd27cmj.execute-api.sa-east-1.amazonaws.com/dev/');\n define('AWS_SQS_QUEUER_KEY', '********************');\n define('AWS_SQS_QUEUER_SECRET', 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO');\n \n"}], "date": 1744052411772, "name": "Commit-0", "content": "<?php\n/**\n * En este archivo se configuran todas las variables de entorno, el archivo original es acc.example.php, que una vez editado debe ser renombrado a acc.php.\n */\n\nrequire_once __DIR__.'/defines.php'; // Constantes definidas por código\nrequire_once __DIR__.'/constantes.php'; // Constantes editables por el usuario\nrequire_once __DIR__.'/funciones_basicas.php';\n\n\n// Estados posibles del sistema: desarrollo, control, produccion, offline\ndefine('ESTADO', 'desarrollo');\n// En caso de estar el sistema offline, se muestra el siguiente mensaje en el login\ndefine('MENSAJE', 'Estamos realizando una actualización programada y el sistema estará offline por unos minutos');\n\n// URLs varias\ndefine('URL_HOST', 'www.saasargentina.des');\ndefine('URL_COOKIE', '.saasargentina.des');\ndefine('URL_SITE', 'http://saasargentina.des');\ndefine('URL_LOGIN', 'http://saasargentina.des/login');\ndefine('URL_SAAS', 'http://app.saasargentina.des');\ndefine('URL_BETA', 'http://app.saasargentina.des');\ndefine('URL_ALFA', 'http://app.saasargentina.des');\ndefine('URL_API', 'http://api.saasargentina.des');\ndefine('URL_API_BETA', 'http://api.saasargentina.des');\ndefine('URL_API_ALFA', 'http://api.saasargentina.des');\ndefine('URL_API_LOGIN', 'http://login.saasargentina.des');\ndefine('URL_INFORMES', 'http://informes.saasargentina.des');\ndefine('URL_INFORMES_BETA', 'http://informes.saasargentina.des');\ndefine('URL_INFORMES_ALFA', 'http://informes.saasargentina.des');\ndefine('URL_SCRIPTS', 'http://scripts.saasargentina.des');\ndefine('URL_TIENDA', 'http://tienda.saasargentina.des');\ndefine('URL_ERROR', 'https://www.saasargentina.des/error');\ndefine('URL_S3', 'https://s3-sa-east-1.amazonaws.com/saasargentina/');\ndefine('URL_DESCARGAS', 'https://s3-sa-east-1.amazonaws.com/saasargentina-descargas/');\n\n// PATH varios\ndefine('PATH_SAAS', '/home/<USER>/www/saasargentina/services/app/');\ndefine('PATH_BETA', '/home/<USER>/www/saasargentina/services/app/');\ndefine('PATH_ALFA', '/home/<USER>/www/saasargentina/services/app/');\ndefine('PATH_ACC', '/home/<USER>/www/saasargentina/services/acc/');\ndefine('PATH_SCRIPTS', '/home/<USER>/www/saasargentina/services/scripts/');\ndefine('PATH_TOOLS', '/home/<USER>/www/saasargentina/services/acc/tools/');\ndefine('PATH_ARCHIVOS', '/home/<USER>/www/saasargentina/services/acc/archivos/');\ndefine('PATH_WSFE', '/home/<USER>/www/saasargentina/services/acc/empresas/wsfe/');\ndefine('PATH_LOGS', '/home/<USER>/www/saasargentina/services/acc/empresas/logs/');\ndefine('PATH_ELIMINADAS', '/home/<USER>/www/saasargentina/services/acc/empresas/eliminadas/');\ndefine('PATH_BACKUPS', '/home/<USER>/www/saasargentina/services/acc/backups/');\n\n// Base de datos principal\ndefine('BD_HOST', '127.0.0.1');\ndefine('BD_USER', 'yosoyroot');\ndefine('BD_PASS', '8des4rollo');\ndefine('BD_BD', 'saasargentina');\ndefine('BD_TABLAS', 'tablas');\ndefine('BD_PORT', 0); // Es importante que sea 0 y no ''\ndefine('BD_SOCKET', '');\n\n// Datos de cuentas de Google Apps para enviar mails propios\ndefine('SMTP_INFO_NOMBRE', 'SaaS Argentina');\ndefine('SMTP_INFO_USER', '<EMAIL>');\ndefine('SMTP_INFO_PASS', '');\ndefine('SMTP_NOREPLY_USER', '<EMAIL>');\ndefine('SMTP_NOREPLY_NOMBRE', '[NO RESPONDER] SaaS Argentina');\ndefine('SMTP_NOREPLY_PASS', 'FUiFxYRtgr6FPsRvkWnFTwUWOxgr9f6p');\ndefine('SMTP_SES_USER', '********************');\ndefine('SMTP_SES_PASS', 'BI6YyhCX6SiFHGKRdJDX+xRzwNekMBAqNFkA15tyryMn');\ndefine('SMTP_PW', '0a422d7698940b3225a5b8936425da10');\n\n// Variables de acceso a Amazon AWS\ndefine('AWS_KEY', '********************');\ndefine('AWS_SECRET', 'ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+');\ndefine('AWS_REGION', 'sa-east-1');\ndefine('AWS_BUCKET', 'saasargentina');\ndefine('AWS_WSFE', 'saasargentina-wsfe');\ndefine('AWS_BACKUPS', 'saasargentina-backups');\ndefine('AWS_URL_EMAIL_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue');\ndefine('AWS_URL_AFIPSDK_QUEUE', 'https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue');\ndefine('AWS_SQS_QUEUER_KEY', '********************');\ndefine('AWS_SQS_QUEUER_SECRET', 'MkbDiQOqUrZrXziFDt1WwVwRAhZVL/e0ayI4ZbZO');\n\n\n// Variables de las aplicaciones de Mercado Libre\n//Dejo este mío, por si no hay otros\ndefine('MP_ACCESS_TOKEN', 'TEST-7940098014182402-120716-f83a2aaf3a19dd12756e607c71c20912__LC_LA__-76076010');\ndefine('ML_APP_ID', '6366624061377005');\ndefine('ML_BETA_ID', '2905939953172097');\ndefine('ML_ALFA_ID', '3710461931597197');\ndefine('ML_APP_SECRET', '');\ndefine('ML_BETA_SECRET', '');\ndefine('ML_ALFA_SECRET', '');\ndefine('ML_USER_ID_TEST', '236848119');\n"}]}