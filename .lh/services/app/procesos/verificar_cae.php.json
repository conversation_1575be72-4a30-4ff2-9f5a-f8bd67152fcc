{"sourceFile": "services/app/procesos/verificar_cae.php", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1750188459940, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1750188459940, "name": "Commit-0", "content": "<?php\n// Archivo para verificar CAE\n// Por ahora devuelve siempre false para probar la funcionalidad de reintentos\n\n// Obtener el ID de la venta/comprobante\n$id = recibir_variable('id');\n$accion = recibir_variable('accion');\n\n// Simular tiempo de procesamiento\nsleep(1);\n\n// Por ahora devolver siempre false para pruebas\n// En producción aquí iría la lógica real para verificar el CAE\n$resultado = array(\n    'success' => false,\n    'message' => 'CAE aún no disponible',\n    'data' => array(\n        'id' => $id,\n        'accion' => $accion,\n        'timestamp' => date('Y-m-d H:i:s')\n    )\n);\n\n// Enviar respuesta como JSON\nheader('Content-Type: application/json');\necho json_encode($resultado);\n?>\n"}]}