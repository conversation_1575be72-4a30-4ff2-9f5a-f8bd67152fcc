{"sourceFile": "services/app/public/estilos/estilo_2/js/saasargentina.js", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1738595053465, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750252309384, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,9 +43,9 @@\n     return true;\n   } else {\n     // Primero devuelvo el return porque sino no se puede desbloquear\n     return false;\n-    window.desbloqueable = false;\n+    // window.desbloqueable = false;\n   }\n }\n \n function alerta(mensaje) {\n"}], "date": 1738595053465, "name": "Commit-0", "content": "var desbloqueable = false;\nvar confirmarsalida = false;\n//var set_control_sesion = setTimeout(\"control_sesion()\", 10000);\n\nfunction bloquear() {\n  var top = $(window).height() / 2 - $(\"#actualizando\").outerHeight();\n  $(\"#actualizando\").css({\n    position: \"fixed\",\n    margin: 0,\n    top: (top > 0 ? top : 0) + \"px\",\n    width: \"100%\"\n  });\n  $(\"#bloquea\").fadeIn(500);\n  $(\"#actualizando\").fadeIn(500);\n}\n\nfunction desbloquear() {\n  $(\"#bloquea\").fadeOut(500);\n  $(\"#actualizando\").fadeOut(500);\n  $(\".informacion_especifica\").remove();\n  $(\"#marco_flotante\").fadeOut(500);\n  $(\"#marco_flotante\").html(\"\");\n  window.desbloqueable = false;\n}\n\nfunction marco_flotante() {\n  $(\"#bloquea\").fadeIn(500);\n  $(\"#marco_flotante\").fadeIn(500);\n  window.desbloqueable = true;\n}\n\nfunction marco_modal() {\n  $(\"#bloquea\").fadeIn(500);\n  $(\"#marco_flotante\").fadeIn(500);\n  window.desbloqueable = false;\n}\n\nfunction confirma(mensaje) {\n  window.desbloqueable = true;\n  if (confirm(mensaje)) {\n    window.confirmarsalida = false;\n    bloquear();\n    return true;\n  } else {\n    // Primero devuelvo el return porque sino no se puede desbloquear\n    return false;\n    window.desbloqueable = false;\n  }\n}\n\nfunction alerta(mensaje) {\n  alert(mensaje);\n}\n\nfunction cerrar_mensaje_flotante(idmensaje, destacado = false) {\n  $(\"#\"+idmensaje).fadeOut(200);\n\n  if (destacado) {\n    $.ajax(\n    {\n        url: \"cargadores/ajax.php\",\n        type: \"post\",\n        data: (\n        {\n            t: window.t,\n            modulo: \"inicio\",\n            ventana: \"controles\",\n            boton: \"ocultar_destacado\",\n            id: idmensaje\n        })\n    });\n  }\n}\n\nfunction control_sesion(boton) {\n  $.ajax({\n    url: \"cargadores/sesion.php\",\n    type: \"get\",\n    jsonp: \"callback\",\n    dataType: \"jsonp\",\n    data: {\n      boton: boton\n    },\n    success: function (data) {\n      $(\"#contenedor\").append(data);\n    },\n    error: function () {\n      alerta(\n        \"¡CUIDADO! Se ha producido un error conectándose con el servidor para controlar su sesión\"\n      );\n    }\n  });\n}\n\nwindow.onbeforeunload = function() {\n  var returnValue = undefined;\n  if (confirmarsalida) {\n      returnValue = \"Los datos que haya ingresado puede que se pierdan\";\n  }\n  // window.desbloqueable = false;\n  // eventObject.returnValue = returnValue;\n  return returnValue;\n\n}\n\nfunction salir() {\n  if (confirma(\"¿Está seguro que desea salir del sistema?\")) {\n    $.ajax({\n      url: URL_API_LOGIN + \"/salir\",\n      type: \"get\",\n      dataType: \"jsonp\",\n      error: function (jqXHR) {\n        var data = jQuery.parseJSON(jqXHR.responseText);\n        var mensaje = data.mensaje\n          ? data.mensaje\n          : \"Ocurrió un error conectándose con el servidor\";\n        alerta(mensaje);\n      },\n      success: function (data) {\n        document.location = data.location;\n      }\n    });\n  }\n  return false;\n}\n\nfunction reiniciar() {\n  $.ajax({\n    url: URL_API_LOGIN + \"/reingresar\",\n    type: \"get\",\n    jsonp: \"callback\",\n    dataType: \"jsonp\",\n    error: function (jqXHR) {\n      var data = jQuery.parseJSON(jqXHR.responseText);\n      var mensaje = data.mensaje\n        ? data.mensaje\n        : \"Ocurrió un error conectándose con el servidor\";\n      alerta(mensaje);\n    },\n    success: function (data) {\n      document.location = data.location;\n    }\n  });\n}\n\nfunction gratis() {\n  marco_flotante();\n  $.ajax({\n    url: \"cargadores/modal.php\",\n    type: \"post\",\n    data: {\n      modulo: \"configuraciones\",\n      ventana: \"configuraciones_gratis\"\n    },\n    success: function (data) {\n      $(\"#marco_flotante\").html(data);\n    }\n  });\n\n  return false;\n}\n\nfunction isFloat(n) {\n  return !isNaN(n) && n.toString().indexOf(\".\") != -1;\n}\n\nfunction redondeo(n) {\n  return (!isNaN(n) && n > 0.01) || n < -0.01\n    ? parseFloat(n).toFixed(2)\n    : \"0.00\";\n}\n\n// FUNCIONES DE VENTANAS PARA MIGRAR A JS EXCLUSIVO\nfunction modal(modulo, ventana, id, callback) {\n  marco_modal();\n  var ser = $(\"form\").serialize();\n  $.ajax({\n    url: \"cargadores/modal.php\",\n    type: \"post\",\n    data: {\n      modulo: modulo,\n      ventana: ventana,\n      id: id,\n      serialize: ser\n    },\n    success: function (data) {\n      $(\"#marco_flotante\").html(data);\n      if (callback) {\n        callback();\n      }\n    }\n  });\n}\n\nfunction alerta_selector(tipo, valor, selector, flotar_abajo, tiempo) {\n  if (typeof flotar_abajo === \"undefined\" || flotar_abajo === null)\n    flotar_abajo = false;\n  if (typeof tiempo === \"undefined\" || tiempo === null) tiempo = 20000;\n\n  var selector_limpio = selector.replace(/\\W/g, \"\");\n\n  // Elimino la alerta anterior\n  $(\"#alerta_selector_\" + selector_limpio).remove();\n\n  // Si hay una nueva alerta\n  if (valor) {\n    var my = flotar_abajo ? \"left top\" : \"left bottom\";\n    var at = flotar_abajo ? \"center bottom\" : \"center top\";\n\n    $(selector)\n      .parents(\".marco, #marco_flotante\")\n      .append(\n        '<div class=\"' +\n          tipo +\n          '_especifica\" id=\"alerta_selector_' +\n          selector_limpio +\n          '\">' +\n          '<span class=\"campo_texto\">' +\n          valor +\n          \"</div>\"\n      );\n    $(\"#alerta_selector_\" + selector_limpio).position({\n      my: my,\n      at: at,\n      of: selector\n    });\n    if (tiempo)\n      setTimeout(\n        '$(\"#alerta_selector_' + selector_limpio + '\").remove()',\n        5000\n      );\n  }\n}\n\nfunction ahora() {\n  var d = new Date();\n  var ahora =\n    (\"0\" + d.getDate()).slice(-2) +\n    \"-\" +\n    (\"0\" + (d.getMonth() + 1)).slice(-2) +\n    \"-\" +\n    d.getFullYear() +\n    \" \" +\n    (\"0\" + d.getHours()).slice(-2) +\n    \":\" +\n    (\"0\" + d.getMinutes()).slice(-2);\n  return ahora;\n}\n\n$(function () {\n  $(\"ul li:has(ul)\").bind(detectMobile() ? 'touchstart' : 'hover', function (e) {\n    if ($(this).is(\".li_menu\")) $(this).addClass(\"li_menu_hover\");\n    $(this).find(\"ul\").show();\n    $(this).find(\"li ul\").hide();\n  });\n  $(\"ul li:has(ul), ul li ul\").mouseleave(function (e) {\n    if ($(this).is(\".li_menu\")) $(this).removeClass(\"li_menu_hover\");\n    $(this).find(\"ul\").hide();\n  });\n\n  $(\"img:not(.no_title), .ordenar, .input_boton_imagen\")\n    .live(\"mouseover\", function () {\n      $(\".informacion_especifica\").remove();\n      window.title = $(this).attr(\"title\");\n      $(this).removeAttr(\"title\");\n      if (title && !detectMobile()) {\n        $(\"#flotante\").append(\n          '<div class=\"informacion_especifica\"><span class=\"campo_texto\">' +\n            title +\n            \"</span></div>\"\n        );\n        $(\".informacion_especifica\").position({\n          my: \"center bottom\",\n          at: \"right top\",\n          of: this\n        });\n      }\n    })\n    .live(\"mouseleave\", function () {\n      $(\".informacion_especifica\").fadeOut(200, function () {\n        $(this).remove();\n      });\n      $(this).attr(\"title\", window.title);\n      window.title = \"\";\n    });\n  $(\"a\").bind(detectMobile() ? 'touchstart' : 'click', function (e) {\n    if (!detectMobile()) {\n      if (\n        e.type == \"touchstart\" &&\n        $(this).attr(\"target\") != \"_blank\" &&\n        $(this).parent().parent().attr(\"class\") != \"solapas\"\n      ) {\n        window.location.href = $(this).attr(\"href\");\n      } else if (e.type == \"touchstart\" && $(this).attr(\"target\") == \"_blank\") {\n        $(\"a:contains('\" + $(this).html() + \"')\")[0].click();\n        return false;\n      } else if (\n        $(this).attr(\"href\").search(\".php\") > 0 &&\n        $(this).attr(\"target\") != \"_blank\" &&\n        !window.desbloqueable &&\n        $(this).attr(\"href\").search(\"exportar\") == -1\n      ) {\n        bloquear();\n      }\n    }\n  });\n  $(\"#bloquea\").live(detectMobile() ? 'touchstart' : 'click', function (e) {\n    if (window.desbloqueable) {\n      desbloquear();\n    }\n  });\n  $(\"#cerrar\").live(detectMobile() ? 'touchstart' : 'click', function (e) {\n    e.preventDefault();\n    desbloquear();\n  });\n\n  $(\".roll\").each(function () {\n    rollsrc = $(this).attr(\"src\");\n    rollON = rollsrc.replace(/.png$/gi, \"on.png\");\n    $(\"<img>\").attr(\"src\", rollON);\n  });\n  $(\".roll\").mouseover(function () {\n    imgsrc = $(this).attr(\"src\");\n    matches = imgsrc.match(/_over/);\n    if (!matches) {\n      imgsrcON = imgsrc.replace(/.png$/gi, \"on.png\");\n      $(this).attr(\"src\", imgsrcON);\n    }\n  });\n  $(\".roll\").mouseout(function () {\n    $(this).attr(\"src\", imgsrc);\n  });\n\n  $(\"#superbuscador\").live(detectMobile() ? 'touchstart' : 'click', function (e) {\n    e.preventDefault();\n    marco_flotante();\n    $(\"#marco_flotante\").load(\"cargadores/flotante.php\", {\n      modulo: \"inicio\",\n      ventana: \"superbuscador\"\n    });\n  });\n\n  $(\".redactor_entrada_textarea\").live(\"keydown\", function (e) {\n    if (e.keyCode == 13 && e.ctrlKey) $(\".boton\").first().click();\n  });\n\n  $(\"#salir\").live(detectMobile() ? 'touchstart' : 'click', function (e) {\n    e.preventDefault();\n    salir();\n  });\n});\n\nwindow.onscroll = function () {\n  var speed = 4.0;\n  document.body.style.backgroundPosition =\n    -window.pageXOffset + \"px \" + -window.pageYOffset / speed + \"px\";\n};\n\n$.mask.options = {\n  attr: \"alt\",\n  mask: null,\n  type: \"fixed\",\n  maxLength: -1,\n  defaultValue: \"\",\n  textAlign: true,\n  selectCharsOnFocus: true,\n  setSize: false,\n  autoTab: false,\n  fixedChars: \"[(),.:/ -]\",\n  onInvalid: function () {},\n  onValid: function () {},\n  onOverflow: function () {}\n};\n\n\n$.mask.masks = {\n  moneda: {\n    mask: \"99.9999999999999\",\n    type: \"reverse\"\n  },\n  monedanegativa: {\n    mask: \"99.9999999999999\",\n    type: \"reverse\",\n    signal: \"\",\n    defaultValue: \"+\"\n  },\n  descuento: { mask: \"99.991\", type: \"reverse\" },\n  porcentaje: { mask: \"99.9999\", type: \"reverse\", signal: \"\" },\n  cantidad: { mask: \"99.999999\", type: \"reverse\" },\n  cantidadnegativa: {\n    mask: \"99.999999\",\n    type: \"reverse\",\n    signal: \"\",\n    defaultValue: \"+\"\n  },\n  tiempo: { mask: \"99:99:997\", type: \"reverse\" },\n  cuit: { mask: \"9-99999999-99\", type: \"reverse\" }\n};\n\n\njQuery.extend(jQuery.validator.messages, {\n  required: \"\",\n  email: \"\"\n});\n\nfunction abrirMenu() {\n  let menu = document.getElementById(\"menu\");\n  if (menu.style.display === \"block\") {\n    $(\".submenu\").hide();\n    $(\".submenu2\").hide();\n    $(\"#menu\").fadeOut(100);\n  } else {\n    $(\"#menu\").fadeIn(100);\n  }\n}\n\nfunction abrirSubMenu(modulo) {\n  let subMenu = document.getElementById(modulo);\n  if (subMenu.style.display === \"block\") {\n    subMenu.style.display = \"none\";\n  } else {\n    subMenu.style.display = \"block\";\n  }\n}\n\nfunction detectMobile() {\n  return (\n    typeof window.orientation !== \"undefined\" ||\n    navigator.userAgent.indexOf(\"IEMobile\") !== -1\n  );\n}\n\n$(document).mouseup(function(e){\n  var menu = $('#menu'); //Cierro menú desplegable en mobile al hacer click en cualquier lado\n  if (!menu.is(e.target)\n  && menu.has(e.target).length === 0\n  && detectMobile())\n  {\n    menu.slideUp();\n  }\n});\n\n$(document).bind(detectMobile() ? 'touchstart' : 'click', function (e) {\n  if ($('.opciones_flotantes:visible').length > 0 && e.target.nodeName != \"IMG\") {\n    $(\".opciones_flotantes\").fadeOut(500);\n  }\n});\n\niniciarRotacion();\nfunction iniciarRotacion() {\n  if (!window.location.search.substring(1)) {\n    setTimeout(function(){\n      let iniciarRotacion = document.getElementById('resaltar'),\n        transformInicio = \"transform: scale(1.2) rotate(360deg); transition: 2s;\";\n      iniciarRotacion.style = transformInicio;\n      iniciarRotacion.webkitTransform = transformInicio;\n      iniciarRotacion.style.msTransform = transformInicio;\n      finalizarRotacion();\n    }, 3000);\n  }\n}\n\nfunction finalizarRotacion() {\n  setTimeout(function(){\n    let finalizarRotacion = document.getElementById('resaltar'),\n      transformFin = \"transform: scale(1) rotate(0deg); transition: 2s;\";\n    finalizarRotacion.style = transformFin;\n    finalizarRotacion.webkitTransform = transformFin;\n    finalizarRotacion.style.msTransform = transformFin;\n  }, 2000);\n}\n\nfunction cerrar_modal() {\n  $(\"#bloquea\").fadeOut(500);\n  window.close();\n}\n\nfunction seleccionarLocalidades() {\n  marco_modal();\n  $.ajax(\n  {\n    url: \"cargadores/modal.php\",\n    type: \"post\",\n    data: (\n    {\n        modulo: \"configuraciones\",\n        ventana: \"cambiar_localidades\"\n    }),\n    success: function(data) {\n        $(\"#marco_flotante\").html(data);\n    }\n  });\n}\n\nfunction copyClipboard(textToCopy) {\n  navigator.clipboard.writeText(textToCopy);\n  alerta('Se copio <b>' + textToCopy + '</b> al portapapeles', 'confirmacion', 2);\n}\n\nfunction alerta(mensaje, tipo = 'alerta', tiempo = 10000) {\n\n  let aleatorio = Math.floor(Math.random() * 10000); // Random entero de 0 a 10000\n  $(\"#flotante\").append('<div class=\"' + tipo + '\" id=\"' + aleatorio +'\"><span class=\"campo_texto\">' + mensaje + '</span></div>');\n\n  if (tiempo)\n      $(\"#\" + aleatorio).delay(tiempo).fadeOut(3000);\n\n  if (detectMobile())\n    document.documentElement.scrollTop = 0;\n\n}\n\nfunction buscadorTodo(modulo) {\n  buscadorAjax(modulo, \"todo\");\n  return false;\n}\n\nfunction buscadorBuscar(modulo) {\n  if ($(\"#\" + modulo + \"_input\").val().length < 1) {\n    $(\"#\" + modulo + \"_resultados\").html(\"\");\n    return false;\n  }\n\n  buscadorAjax(modulo, \"buscar\");\n  return false;\n}\n\nfunction buscadorInput(modulo, keyCode) {\n  if ($(\"#\" + modulo + \"_input\").val().length < 3) {\n    $(\"#\" + modulo + \"_resultados\").html(\"\");\n    return false;\n  }\n\n  if (keyCode == 38) { // Flecha arriba\n    if ($(\"#\" + modulo + \"_\"+(window.flag-1)).length) {\n      window.flag=window.flag-1;\n      $(\"#\" + modulo + \"_\"+window.flag).addClass(\"linea_hover\");\n      $(\"#\" + modulo + \"_\"+(window.flag+1)).removeClass(\"linea_hover\");\n    }\n\n  } else if (keyCode == 40) { // Flecha abajo\n    if ($(\"#\" + modulo + \"_\"+(window.flag+1)).length) {\n      window.flag=window.flag+1;\n      $(\"#\" + modulo + \"_\"+window.flag).addClass(\"linea_hover\");\n      $(\"#\" + modulo + \"_\"+(window.flag-1)).removeClass(\"linea_hover\");\n    }\n\n  } else if (keyCode == 13) { // Enter\n      if (window.flag == 0) {\n        buscadorAjax(modulo, \"ir\");\n\n      } else {\n          bloquear();\n          document.location = modulo + \".php?a=ver&id=\"+$(\"#\" + modulo + \"_\"+window.flag).find(\"input[name=id]\").val();\n      }\n\n  } else if (jQuery.inArray(keyCode, [9, 16, 18, 19, 20, 27, 33, 34, 35, 36, 37, 39, 45, 91, 92, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 144, 145]) == -1) {\n\n    buscadorAjaxDebounced(modulo, \"buscar\");\n    window.flag=0;\n  }\n\n  return false;\n}\n\nfunction buscadorAjax(modulo, boton) {\n\n  if (window.runningRequest && typeof undefined != typeof window.request) {\n    window.request.abort();\n  }\n  window.runningRequest=true;\n\n  window.request = $.ajax(\n  {\n    url: \"cargadores/ajax.php\",\n    type: \"post\",\n    data: (\n    {\n      t: window.t,\n      modulo: modulo,\n      ventana: modulo + \"_buscar\",\n      boton: boton,\n      busqueda: $(\"#\" + modulo + \"_input\").val()\n    }),\n    before: function() {\n      $(\"#\" + modulo + \"_resultados\").hide();\n      $(\"#\" + modulo + \"_conectando\").show();\n    },\n    success: function(data) {\n      $(\"#\" + modulo + \"_resultados\").html(data);\n      $(\"#\" + modulo + \"_conectando\").hide();\n      $(\"#\" + modulo + \"_resultados\").show();\n    }\n  });\n}\n\nfunction buscadorAjaxDebounced(modulo, boton) {\n\n  $(\"#\" + modulo + \"_resultados\").hide();\n  $(\"#\" + modulo + \"_conectando\").show();\n\n  if (window.runningRequest && typeof undefined != typeof window.request) {\n    window.request.abort();\n  }\n  window.runningRequest=true;\n\n  clearTimeout(window.debounce);\n  window.debounce = setTimeout(function () {\n\n    window.request = $.ajax(\n    {\n      url: \"cargadores/ajax.php\",\n      type: \"post\",\n      data: (\n      {\n        t: window.t,\n        modulo: modulo,\n        ventana: modulo + \"_buscar\",\n        boton: boton,\n        busqueda: $(\"#\" + modulo + \"_input\").val()\n      }),\n      success: function(data) {\n        $(\"#\" + modulo + \"_resultados\").html(data);\n        $(\"#\" + modulo + \"_conectando\").hide();\n        $(\"#\" + modulo + \"_resultados\").show();\n      }\n    });\n\n  }, 800);\n\n}\n\nwindow.addEventListener('pageshow', function(event) {\n  if (event.persisted) {\n    // La página fue restaurada desde el bfcache\n    window.location.reload();\n  }\n});\n\nwindow.addEventListener('pagehide', function(event) {\n  if (event.persisted) {\n    // Prevenir que la página sea almacenada en el bfcache\n    event.preventDefault();\n  }\n});\n"}]}