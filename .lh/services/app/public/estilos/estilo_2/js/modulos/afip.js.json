{"sourceFile": "services/app/public/estilos/estilo_2/js/modulos/afip.js", "activeCommit": 0, "commits": [{"activePatchIndex": 8, "patches": [{"date": 1726525280696, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726525319924, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -349,8 +349,9 @@\n     dni.prop('disabled', false);\n     if (tipoDoc == '96') {\n         $.mask.masks.dni = {mask: \"99999999\", type : \"reverse\"}\n     } else if (tipoDoc == '99') {\n+        dni.val('');\n         dni.prop('disabled', true);\n     } else {\n         $.mask.masks.dni = {mask: \"999999999999999\", type : \"reverse\"}\n     }\n"}, {"date": 1726525357461, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -343,10 +343,8 @@\n \n function validar_dni() {\n     const tipoDoc = $(\"select[name=tipodoc]\").val();\n     const dni = $(\"input[name=dni]\");\n-    console.log(tipoDoc);\n-    console.log(dni);\n     dni.prop('disabled', false);\n     if (tipoDoc == '96') {\n         $.mask.masks.dni = {mask: \"99999999\", type : \"reverse\"}\n     } else if (tipoDoc == '99') {\n"}, {"date": 1732733101789, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,9 +2,9 @@\n var domicilio_afip;\n var idlocalidad_afip;\n var localidad_afip;\n var confirma_domicilio = true; // Flag para no consultarle al usuario varias veces si quiere pisar el domicilio\n-var afip_responde = true; // Flag para cuando no respondió AFIP una vez, no volvemos a consultar\n+var afip_responde = true; // Flag para cuando no respondió ARCA una vez, no volvemos a consultar\n // var url_afip_cuit = \"https://soa.afip.gob.ar/sr-padron/v2/persona/\";\n // var url_afip_dni = \"https://soa.afip.gob.ar/sr-padron/v2/personas/\";\n var url_afip_cuit = \"procesos/afip_proxy.php?cuit=\";\n var url_afip_dni = \"procesos/afip_proxy.php?dni=\";\n@@ -92,21 +92,21 @@\n         success: function(data) {\n             if (data != false && data.success) {\n                 // $(\"input[type=submit]\").first().removeAttr(\"disabled\");\n                 completar_campos(data.data);\n-                afip_mostrar('afip_responde', 'confirmacion', 'CUIT habilitada en la base pública de AFIP');\n+                afip_mostrar('afip_responde', 'confirmacion', 'CUIT habilitada en la base pública de ARCA');\n \n             } else if (data != false && !data.success) {\n                 // $(\"input[type=submit]\").first().attr(\"disabled\", \"disabled\");\n                 vaciar_campos('cuit');\n-                afip_mostrar('afip_responde', 'alerta', 'No se encontró este CUIT habilitado en la base pública de AFIP');\n+                afip_mostrar('afip_responde', 'alerta', 'No se encontró este CUIT habilitado en la base pública de ARCA');\n             }\n         },\n         error: function(data) {\n             // $(\"input[type=submit]\").first().removeAttr(\"disabled\");\n             vaciar_campos('cuit');\n             afip_responde = false;\n-            afip_mostrar('afip_no_responde', 'alerta', 'La base de datos pública de AFIP no se encuentra disponible');\n+            afip_mostrar('afip_no_responde', 'alerta', 'La base de datos pública de ARCA no se encuentra disponible');\n         }\n     });\n }\n \n@@ -138,25 +138,25 @@\n \n function consultar_afip_callback(data)\n {\n     if (data === 'error') {\n-        afip_mostrar('afip_no_responde', 'alerta', 'La base de datos pública de AFIP no se encuentra disponible');\n+        afip_mostrar('afip_no_responde', 'alerta', 'La base de datos pública de ARCA no se encuentra disponible');\n \n     } else if (data != false && data.success) {\n         completar_campos(data.data);\n-        afip_mostrar('afip_responde', 'confirmacion', 'CUIT habilitada en la base pública de AFIP');\n+        afip_mostrar('afip_responde', 'confirmacion', 'CUIT habilitada en la base pública de ARCA');\n \n     } else if (data == false || !data.success) {\n         vaciar_campos();\n-        afip_mostrar('afip_responde', 'alerta', 'No se encontraron datos en la base pública de AFIP para este Nº de CUIT o DNI');\n+        afip_mostrar('afip_responde', 'alerta', 'No se encontraron datos en la base pública de ARCA para este Nº de CUIT o DNI');\n \n     }\n }\n \n function chequear_cuit()\n {\n     if (!$(\"input[name=cuit]\").val()) {\n-        alerta('Complete el CUIT para cargar los datos de la base pública de AFIP');\n+        alerta('Complete el CUIT para cargar los datos de la base pública de ARCA');\n \n     } else {\n         bloquear();\n         obtener_datos_cuit($(\"input[name=cuit]\").val(), consultar_afip_callback);\n@@ -168,12 +168,12 @@\n function chequear_dni()\n {\n     //const tipoDoc = $(\"select[name=tipodoc]\").val();\n     if ($(\"select[name=tipodoc]\").val() != '96') {\n-        alerta('El tipo de documento debe ser DNI para cargar los datos de la base pública de AFIP');\n+        alerta('El tipo de documento debe ser DNI para cargar los datos de la base pública de ARCA');\n \n     } else if (!$(\"input[name=dni]\").val()) {\n-        alerta('Complete el DNI para cargar los datos de la base pública de AFIP');\n+        alerta('Complete el DNI para cargar los datos de la base pública de ARCA');\n \n     } else {\n         bloquear();\n         obtener_datos_dni($(\"input[name=dni]\").val(), consultar_afip_callback);\n@@ -184,12 +184,12 @@\n \n function obtener_nombre_dni()\n {\n     if ($(\"select[name=tipodoc]\").val() != '96') {\n-        alerta('El tipo de documento debe ser DNI para cargar los datos de la base pública de AFIP');\n+        alerta('El tipo de documento debe ser DNI para cargar los datos de la base pública de ARCA');\n \n     } else if (!$(\"input[name=dni]\").val()) {\n-        alerta('Complete el DNI para cargar los datos de la base pública de AFIP');\n+        alerta('Complete el DNI para cargar los datos de la base pública de ARCA');\n \n     } else {\n         confirma_domicilio = false;\n         obtener_datos_dni($(\"input[name=dni]\").val(), consultar_afip_callback);\n@@ -199,9 +199,9 @@\n \n function obtener_proveedores_cuit()\n {\n     if (!$(\"input[name=cuit]\").val()) {\n-        alerta('Complete el CUIT para cargar los datos de la base pública de AFIP');\n+        alerta('Complete el CUIT para cargar los datos de la base pública de ARCA');\n \n     } else {\n         obtener_datos_cuit($(\"input[name=cuit]\").val(), consultar_afip_callback);\n     }\n@@ -223,12 +223,12 @@\n         $(\"input[type=submit]\").first().removeAttr(\"disabled\");\n         vaciar_campos('cuit');\n \n     } else if (afip_responde) {\n-    // Después me fijo si existe en AFIP\n+    // Después me fijo si existe en ARCA\n         consultar_afip_cuit(cuit);\n     } else {\n-        // alerta_selector('alerta', 'La base de datos pública de AFIP no se encuentra disponible', '#cuit');\n+        // alerta_selector('alerta', 'La base de datos pública de ARCA no se encuentra disponible', '#cuit');\n         alerta_selector('', '', '#cuit');\n     }\n }\n \n@@ -308,9 +308,9 @@\n {\n     if (!confirma_domicilio)\n         return false;\n \n-    if (!confirm('Se encontró un domicilio diferente en la base pública de AFIP, ¿Desea reemplazar el domicilio ingresado?'))\n+    if (!confirm('Se encontró un domicilio diferente en la base pública de ARCA, ¿Desea reemplazar el domicilio ingresado?'))\n         confirma_domicilio = false;\n \n     return confirma_domicilio;\n }\n@@ -318,9 +318,9 @@\n function afip_mostrar(estado, tipo_mensaje, mensaje)\n {\n     $(\"#afip_responde, #afip_validando,  #afip_no_responde\").hide();\n     $(\"#\" + estado).show();\n-    if (estado != 'afip_no_responde') // Pongo parche temporal porque AFIP falla mucho para no mostrar la alerta siempre\n+    if (estado != 'afip_no_responde') // Pongo parche temporal porque ARCA falla mucho para no mostrar la alerta siempre\n         alerta_selector(tipo_mensaje, mensaje, '#cuit');\n }\n \n $(\"input[name=cuit]\").keyup(function () {\n"}, {"date": 1750187714568, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -354,4 +354,31 @@\n         $.mask.masks.dni = {mask: \"999999999999999\", type : \"reverse\"}\n     }\n     dni.setMask();\n }\n+\n+// Esta función es para verificar si ya se procesó el CAE de una venta, llama a ajax.php con el idventa cada 5 segundos\n+function verificando_cae(idventa) {\n+    if (typeof idventa == 'undefined' || idventa == 0) {\n+        console.error('No se ha definido el id de la venta para verificar el CAE');\n+        return false;\n+    }\n+\n+    $.ajax({\n+        url: 'ajax.php',\n+        type: 'POST',\n+        data: {accion: 'verificar_cae', idventa: idventa},\n+        success: function(data) {\n+            if (data.success) {\n+                // Aquí puedes manejar la respuesta exitosa\n+                console.log('CAE verificado:', data);\n+            } else {\n+                // Aquí puedes manejar el caso en que no se encontró el CAE\n+                console.log('CAE no encontrado o error:', data);\n+            }\n+        },\n+        error: function(xhr, status, error) {\n+            console.error('Error al verificar CAE:', error);\n+        }\n+    });\n+\n+}\n\\ No newline at end of file\n"}, {"date": 1750187895452, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -365,9 +365,9 @@\n \n     $.ajax({\n         url: 'ajax.php',\n         type: 'POST',\n-        data: {accion: 'verificar_cae', idventa: idventa},\n+        data: {accion: 'verificar_cae', id: idventa},\n         success: function(data) {\n             if (data.success) {\n                 // Aquí puedes manejar la respuesta exitosa\n                 console.log('CAE verificado:', data);\n"}, {"date": 1750187979885, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -355,10 +355,9 @@\n     }\n     dni.setMask();\n }\n \n-// Esta función es para verificar si ya se procesó el CAE de una venta, llama a ajax.php con el idventa cada 5 segundos\n-function verificando_cae(idventa) {\n+function verificar_cae(idventa) {\n     if (typeof idventa == 'undefined' || idventa == 0) {\n         console.error('No se ha definido el id de la venta para verificar el CAE');\n         return false;\n     }\n"}, {"date": 1750188460103, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -361,23 +361,115 @@\n         console.error('No se ha definido el id de la venta para verificar el CAE');\n         return false;\n     }\n \n-    $.ajax({\n-        url: 'ajax.php',\n-        type: 'POST',\n-        data: {accion: 'verificar_cae', id: idventa},\n-        success: function(data) {\n-            if (data.success) {\n-                // Aquí puedes manejar la respuesta exitosa\n-                console.log('CAE verificado:', data);\n-            } else {\n-                // Aquí puedes manejar el caso en que no se encontró el CAE\n-                console.log('CAE no encontrado o error:', data);\n+    // Intervalos de tiempo para los reintentos (en segundos)\n+    var intervalos = [3, 5, 7, 10, 15, 20, 30, 60, 180];\n+    var intentoActual = 0;\n+    var maxIntentos = intervalos.length;\n+    var timeoutId = null;\n+    \n+    function ejecutar_verificacion() {\n+        var tiempoEspera = intentoActual === 0 ? 3 : intervalos[intentoActual - 1];\n+        console.log('Verificando CAE - Intento ' + (intentoActual + 1) + ' de ' + maxIntentos + ' (después de ' + tiempoEspera + 's)');\n+        \n+        $.ajax({\n+            url: '../../../cargadores/ajax.php',\n+            type: 'POST',\n+            data: {modulo: 'afip', ventana: 'verificar_cae', accion: 'verificar_cae', id: idventa},\n+            dataType: 'json',\n+            timeout: 10000, // 10 segundos de timeout\n+            success: function(data) {\n+                console.log('Respuesta del servidor:', data);\n+                \n+                if (data && data.success === true) {\n+                    // CAE verificado exitosamente - recargar página\n+                    console.log('✓ CAE verificado exitosamente, recargando página...');\n+                    mostrar_mensaje_cae_exito();\n+                    setTimeout(function() {\n+                        location.reload();\n+                    }, 1000);\n+                } else {\n+                    // CAE aún no disponible\n+                    if (intentoActual < maxIntentos - 1) {\n+                        // Aún quedan intentos\n+                        intentoActual++;\n+                        var proximoIntervalo = intervalos[intentoActual];\n+                        console.log('CAE no disponible, reintentando en ' + proximoIntervalo + ' segundos...');\n+                        mostrar_mensaje_cae_reintentando(proximoIntervalo, intentoActual + 1, maxIntentos);\n+                        \n+                        // Programar próximo intento\n+                        timeoutId = setTimeout(ejecutar_verificacion, proximoIntervalo * 1000);\n+                    } else {\n+                        // Se agotaron todos los intentos\n+                        console.log('Se agotaron todos los intentos de verificación de CAE');\n+                        mostrar_mensaje_cae_pendiente();\n+                    }\n+                }\n+            },\n+            error: function(xhr, status, error) {\n+                console.error('Error al verificar CAE (intento ' + (intentoActual + 1) + '):', error);\n+                console.error('Status:', status);\n+                console.error('Response:', xhr.responseText);\n+                \n+                if (intentoActual < maxIntentos - 1) {\n+                    // Aún quedan intentos, reintentar\n+                    intentoActual++;\n+                    var proximoIntervalo = intervalos[intentoActual];\n+                    console.log('Error en la verificación, reintentando en ' + proximoIntervalo + ' segundos...');\n+                    mostrar_mensaje_cae_error_reintentando(proximoIntervalo, intentoActual + 1, maxIntentos);\n+                    timeoutId = setTimeout(ejecutar_verificacion, proximoIntervalo * 1000);\n+                } else {\n+                    // Se agotaron todos los intentos\n+                    console.log('Se agotaron todos los intentos debido a errores');\n+                    mostrar_mensaje_cae_pendiente();\n+                }\n             }\n-        },\n-        error: function(xhr, status, error) {\n-            console.error('Error al verificar CAE:', error);\n+        });\n+    }\n+    \n+    // Mostrar mensaje inicial\n+    mostrar_mensaje_cae_iniciando();\n+    \n+    // Iniciar el primer intento después de 3 segundos\n+    console.log('Iniciando verificación de CAE en 3 segundos...');\n+    timeoutId = setTimeout(ejecutar_verificacion, 3000);\n+    \n+    // Retornar función para cancelar si es necesario\n+    return {\n+        cancelar: function() {\n+            if (timeoutId) {\n+                clearTimeout(timeoutId);\n+                console.log('Verificación de CAE cancelada');\n+            }\n         }\n-    });\n+    };\n+}\n \n+function mostrar_mensaje_cae_iniciando() {\n+    console.log('🔄 Iniciando verificación de CAE...');\n+    // Aquí puedes agregar la lógica para mostrar un mensaje en la UI\n+}\n+\n+function mostrar_mensaje_cae_reintentando(segundos, intento, total) {\n+    console.log('⏳ CAE no disponible aún. Reintento ' + intento + '/' + total + ' en ' + segundos + ' segundos...');\n+    // Aquí puedes agregar la lógica para mostrar un mensaje en la UI\n+}\n+\n+function mostrar_mensaje_cae_error_reintentando(segundos, intento, total) {\n+    console.log('⚠️ Error en verificación. Reintento ' + intento + '/' + total + ' en ' + segundos + ' segundos...');\n+    // Aquí puedes agregar la lógica para mostrar un mensaje en la UI\n+}\n+\n+function mostrar_mensaje_cae_exito() {\n+    console.log('✅ CAE verificado exitosamente');\n+    // Aquí puedes agregar la lógica para mostrar un mensaje en la UI\n+}\n+\n+function mostrar_mensaje_cae_pendiente() {\n+    console.log('❌ CAE Pendiente, no se seguirá verificando');\n+    // Aquí puedes agregar la lógica para mostrar un mensaje en la UI\n+    // Por ejemplo:\n+    // alert('CAE Pendiente, no se seguirá verificando');\n+    // o si tienes una función para mostrar mensajes:\n+    // mostrar_mensaje('CAE Pendiente, no se seguirá verificando', 'warning');\n }\n\\ No newline at end of file\n"}, {"date": 1750188711122, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -354,122 +354,4 @@\n         $.mask.masks.dni = {mask: \"999999999999999\", type : \"reverse\"}\n     }\n     dni.setMask();\n }\n-\n-function verificar_cae(idventa) {\n-    if (typeof idventa == 'undefined' || idventa == 0) {\n-        console.error('No se ha definido el id de la venta para verificar el CAE');\n-        return false;\n-    }\n-\n-    // Intervalos de tiempo para los reintentos (en segundos)\n-    var intervalos = [3, 5, 7, 10, 15, 20, 30, 60, 180];\n-    var intentoActual = 0;\n-    var maxIntentos = intervalos.length;\n-    var timeoutId = null;\n-    \n-    function ejecutar_verificacion() {\n-        var tiempoEspera = intentoActual === 0 ? 3 : intervalos[intentoActual - 1];\n-        console.log('Verificando CAE - Intento ' + (intentoActual + 1) + ' de ' + maxIntentos + ' (después de ' + tiempoEspera + 's)');\n-        \n-        $.ajax({\n-            url: '../../../cargadores/ajax.php',\n-            type: 'POST',\n-            data: {modulo: 'afip', ventana: 'verificar_cae', accion: 'verificar_cae', id: idventa},\n-            dataType: 'json',\n-            timeout: 10000, // 10 segundos de timeout\n-            success: function(data) {\n-                console.log('Respuesta del servidor:', data);\n-                \n-                if (data && data.success === true) {\n-                    // CAE verificado exitosamente - recargar página\n-                    console.log('✓ CAE verificado exitosamente, recargando página...');\n-                    mostrar_mensaje_cae_exito();\n-                    setTimeout(function() {\n-                        location.reload();\n-                    }, 1000);\n-                } else {\n-                    // CAE aún no disponible\n-                    if (intentoActual < maxIntentos - 1) {\n-                        // Aún quedan intentos\n-                        intentoActual++;\n-                        var proximoIntervalo = intervalos[intentoActual];\n-                        console.log('CAE no disponible, reintentando en ' + proximoIntervalo + ' segundos...');\n-                        mostrar_mensaje_cae_reintentando(proximoIntervalo, intentoActual + 1, maxIntentos);\n-                        \n-                        // Programar próximo intento\n-                        timeoutId = setTimeout(ejecutar_verificacion, proximoIntervalo * 1000);\n-                    } else {\n-                        // Se agotaron todos los intentos\n-                        console.log('Se agotaron todos los intentos de verificación de CAE');\n-                        mostrar_mensaje_cae_pendiente();\n-                    }\n-                }\n-            },\n-            error: function(xhr, status, error) {\n-                console.error('Error al verificar CAE (intento ' + (intentoActual + 1) + '):', error);\n-                console.error('Status:', status);\n-                console.error('Response:', xhr.responseText);\n-                \n-                if (intentoActual < maxIntentos - 1) {\n-                    // Aún quedan intentos, reintentar\n-                    intentoActual++;\n-                    var proximoIntervalo = intervalos[intentoActual];\n-                    console.log('Error en la verificación, reintentando en ' + proximoIntervalo + ' segundos...');\n-                    mostrar_mensaje_cae_error_reintentando(proximoIntervalo, intentoActual + 1, maxIntentos);\n-                    timeoutId = setTimeout(ejecutar_verificacion, proximoIntervalo * 1000);\n-                } else {\n-                    // Se agotaron todos los intentos\n-                    console.log('Se agotaron todos los intentos debido a errores');\n-                    mostrar_mensaje_cae_pendiente();\n-                }\n-            }\n-        });\n-    }\n-    \n-    // Mostrar mensaje inicial\n-    mostrar_mensaje_cae_iniciando();\n-    \n-    // Iniciar el primer intento después de 3 segundos\n-    console.log('Iniciando verificación de CAE en 3 segundos...');\n-    timeoutId = setTimeout(ejecutar_verificacion, 3000);\n-    \n-    // Retornar función para cancelar si es necesario\n-    return {\n-        cancelar: function() {\n-            if (timeoutId) {\n-                clearTimeout(timeoutId);\n-                console.log('Verificación de CAE cancelada');\n-            }\n-        }\n-    };\n-}\n-\n-function mostrar_mensaje_cae_iniciando() {\n-    console.log('🔄 Iniciando verificación de CAE...');\n-    // Aquí puedes agregar la lógica para mostrar un mensaje en la UI\n-}\n-\n-function mostrar_mensaje_cae_reintentando(segundos, intento, total) {\n-    console.log('⏳ CAE no disponible aún. Reintento ' + intento + '/' + total + ' en ' + segundos + ' segundos...');\n-    // Aquí puedes agregar la lógica para mostrar un mensaje en la UI\n-}\n-\n-function mostrar_mensaje_cae_error_reintentando(segundos, intento, total) {\n-    console.log('⚠️ Error en verificación. Reintento ' + intento + '/' + total + ' en ' + segundos + ' segundos...');\n-    // Aquí puedes agregar la lógica para mostrar un mensaje en la UI\n-}\n-\n-function mostrar_mensaje_cae_exito() {\n-    console.log('✅ CAE verificado exitosamente');\n-    // Aquí puedes agregar la lógica para mostrar un mensaje en la UI\n-}\n-\n-function mostrar_mensaje_cae_pendiente() {\n-    console.log('❌ CAE Pendiente, no se seguirá verificando');\n-    // Aquí puedes agregar la lógica para mostrar un mensaje en la UI\n-    // Por ejemplo:\n-    // alert('CAE Pendiente, no se seguirá verificando');\n-    // o si tienes una función para mostrar mensajes:\n-    // mostrar_mensaje('CAE Pendiente, no se seguirá verificando', 'warning');\n-}\n\\ No newline at end of file\n"}], "date": 1726525280696, "name": "Commit-0", "content": "var razonsocial_afip;\nvar domicilio_afip;\nvar idlocalidad_afip;\nvar localidad_afip;\nvar confirma_domicilio = true; // Flag para no consultarle al usuario varias veces si quiere pisar el domicilio\nvar afip_responde = true; // Flag para cuando no respondió AFIP una vez, no volvemos a consultar\n// var url_afip_cuit = \"https://soa.afip.gob.ar/sr-padron/v2/persona/\";\n// var url_afip_dni = \"https://soa.afip.gob.ar/sr-padron/v2/personas/\";\nvar url_afip_cuit = \"procesos/afip_proxy.php?cuit=\";\nvar url_afip_dni = \"procesos/afip_proxy.php?dni=\";\n\nfunction mostrar_cuit(cuit)\n{\n    // Si está vació, es cero o ya tiene guiones bien devolver el mismo\n    if (!cuit)\n        return cuit;\n    cuit = cuit.toString();\n    if (cuit.length == 13 && cuit.charAt(2) == '-' && cuit.charAt(11) == '-')\n        return cuit;\n\n    var digits = cuit.split(\"\");\n    var cuit_con_guiones = digits[0]+digits[1]+'-'+digits[2]+digits[3]+digits[4]+digits[5]+digits[6]+digits[7]+digits[8]+digits[9]+'-'+digits[10];\n    return cuit_con_guiones;\n}\n\nfunction limpiar_cuit(cuit)\n{\n    return cuit.toString().replace('-' , \"\").replace('-' , \"\");\n}\n\nfunction obtener_datos_cuit(cuit, callback)\n{\n    cuit = limpiar_cuit(cuit);\n    $.ajax(\n        {\n            url: url_afip_cuit + cuit,\n            type: \"get\",\n            data: \"\",\n            beforeSend: function(data) {\n                afip_mostrar('validando', '', '');\n            },\n            success: function(data) {\n\n                callback(data);\n            },\n            error:function (e){\n                callback(false);\n            }\n        });\n}\n\nfunction obtener_datos_dni(dni, callback)\n{\n    if (!dni) {\n        callback(false);\n        return false;\n    }\n\n    $.ajax({\n        url: url_afip_dni + dni,\n        type: \"get\",\n        data: \"\",\n        beforeSend: function(data) {\n            afip_mostrar('validando', '', '');\n        },\n        success: function(data) {\n            if (data.success) {\n                console.log(mostrar_cuit(data.cuit))\n                $(\"input[name=cuit]\").val(mostrar_cuit(data.cuit));\n                completar_campos(data.data);\n                callback(data);\n            } else {\n                callback(false);\n            }\n        },\n        error:function (e){\n            callback(false);\n        }\n    });\n}\n\nfunction consultar_afip_cuit(cuit)\n{\n    $.ajax({\n        url: url_afip_cuit + cuit,\n        timeout: 5000,\n        type: \"get\",\n        data: \"\",\n        beforeSend: function() {\n            afip_mostrar('afip_validando', '', '');\n        },\n        success: function(data) {\n            if (data != false && data.success) {\n                // $(\"input[type=submit]\").first().removeAttr(\"disabled\");\n                completar_campos(data.data);\n                afip_mostrar('afip_responde', 'confirmacion', 'CUIT habilitada en la base pública de AFIP');\n\n            } else if (data != false && !data.success) {\n                // $(\"input[type=submit]\").first().attr(\"disabled\", \"disabled\");\n                vaciar_campos('cuit');\n                afip_mostrar('afip_responde', 'alerta', 'No se encontró este CUIT habilitado en la base pública de AFIP');\n            }\n        },\n        error: function(data) {\n            // $(\"input[type=submit]\").first().removeAttr(\"disabled\");\n            vaciar_campos('cuit');\n            afip_responde = false;\n            afip_mostrar('afip_no_responde', 'alerta', 'La base de datos pública de AFIP no se encuentra disponible');\n        }\n    });\n}\n\n// Para saber la condición de IVA tengo que evaluar lo que vuelve en impuestos\nfunction evaluar_condicion(iva, mt)\n{\n    if (iva == 'AC' || iva == 'S')\n        var idtipoiva = 1; // Responsable Inscripto\n    else if (iva == 'NA' && mt == 'N')\n        var idtipoiva = 15; // No Alcanzado\n    else if (iva == 'EX')\n        var idtipoiva = 3; // Exento\n    else if (mt == 'S')\n        var idtipoiva = 2; // Monotributo\n    else\n        var idtipoiva = 0; // Consumidor Final\n\n    return idtipoiva;\n}\n\nfunction nombre_condicion(idtipoiva)\n{\n    if (typeof tablas_condiciones == 'object')\n        var nombre = tablas_condiciones[idtipoiva];\n    else\n        var nombre = 'Consumidor final';\n    return nombre;\n}\n\nfunction consultar_afip_callback(data)\n{\n    if (data === 'error') {\n        afip_mostrar('afip_no_responde', 'alerta', 'La base de datos pública de AFIP no se encuentra disponible');\n\n    } else if (data != false && data.success) {\n        completar_campos(data.data);\n        afip_mostrar('afip_responde', 'confirmacion', 'CUIT habilitada en la base pública de AFIP');\n\n    } else if (data == false || !data.success) {\n        vaciar_campos();\n        afip_mostrar('afip_responde', 'alerta', 'No se encontraron datos en la base pública de AFIP para este Nº de CUIT o DNI');\n\n    }\n}\n\nfunction chequear_cuit()\n{\n    if (!$(\"input[name=cuit]\").val()) {\n        alerta('Complete el CUIT para cargar los datos de la base pública de AFIP');\n\n    } else {\n        bloquear();\n        obtener_datos_cuit($(\"input[name=cuit]\").val(), consultar_afip_callback);\n        if($(\".modal\").length == 0) desbloquear();//creo que acá tmb debería ir, ya que desbloquear a su vez, cierra modal\n    }\n    return false;\n}\n\nfunction chequear_dni()\n{\n    //const tipoDoc = $(\"select[name=tipodoc]\").val();\n    if ($(\"select[name=tipodoc]\").val() != '96') {\n        alerta('El tipo de documento debe ser DNI para cargar los datos de la base pública de AFIP');\n\n    } else if (!$(\"input[name=dni]\").val()) {\n        alerta('Complete el DNI para cargar los datos de la base pública de AFIP');\n\n    } else {\n        bloquear();\n        obtener_datos_dni($(\"input[name=dni]\").val(), consultar_afip_callback);\n        if($(\".modal\").length == 0) desbloquear();\n    }\n    return false;\n}\n\nfunction obtener_nombre_dni()\n{\n    if ($(\"select[name=tipodoc]\").val() != '96') {\n        alerta('El tipo de documento debe ser DNI para cargar los datos de la base pública de AFIP');\n\n    } else if (!$(\"input[name=dni]\").val()) {\n        alerta('Complete el DNI para cargar los datos de la base pública de AFIP');\n\n    } else {\n        confirma_domicilio = false;\n        obtener_datos_dni($(\"input[name=dni]\").val(), consultar_afip_callback);\n    }\n    return false;\n}\n\nfunction obtener_proveedores_cuit()\n{\n    if (!$(\"input[name=cuit]\").val()) {\n        alerta('Complete el CUIT para cargar los datos de la base pública de AFIP');\n\n    } else {\n        obtener_datos_cuit($(\"input[name=cuit]\").val(), consultar_afip_callback);\n    }\n    return false;\n}\n\n// TODO: Hay que migrar esta funciona a la librería de validaciones\nfunction validar_cuit(cuit_input)\n{\n    var cuit = limpiar_cuit(cuit_input.val());\n    if (cuit == \"\") {\n    // Si el campo es vacío solo me aseguro de que se pueda Aceptar y de borrar si hay alguna alerta\n        $(\"input[type=submit]\").first().removeAttr(\"disabled\");\n        alerta_selector(false, false, '#cuit');\n\n    } else if (!$.isNumeric(cuit) || cuit.length != 11) {\n    // Primero reviso si tiene 11 caracteres numéricos\n        alerta_selector('alerta', 'El CUIT no es correcto y no se guardará', '#cuit', false, false);\n        $(\"input[type=submit]\").first().removeAttr(\"disabled\");\n        vaciar_campos('cuit');\n\n    } else if (afip_responde) {\n    // Después me fijo si existe en AFIP\n        consultar_afip_cuit(cuit);\n    } else {\n        // alerta_selector('alerta', 'La base de datos pública de AFIP no se encuentra disponible', '#cuit');\n        alerta_selector('', '', '#cuit');\n    }\n}\n\nfunction completar_campos(data)\n{\n    razonsocial_afip = data.nombre;\n    if (typeof data.domicilioFiscal != 'undefined'){\n        idlocalidad_afip = data.domicilioFiscal.idlocalidad;\n        localidad_afip = data.domicilioFiscal.localidad;\n        /*var existeLocalidad = ($('#select_idlocalidad option[value='+idlocalidad_afip+']').length > 0);\n        if(!existeLocalidad)\n        {\n            $('#select_idlocalidad').append(\"<option value='\"+idlocalidad_afip+\"'>\"+localidad_afip+\"</option>\");\n            $(\"#select_idlocalidad\").val(idlocalidad_afip);\n        } else {\n            $(\"#select_idlocalidad\").val(idlocalidad_afip);\n        }*/\n        $(\"#idlocalidad\").val(idlocalidad_afip);\n        $(\"#localidad\").val(localidad_afip);\n    }\n\n    $(\"input[name=razonsocial]\").val(razonsocial_afip);\n    $('#razonsocial_texto').html(razonsocial_afip);\n\n    if (!$(\"input[name=nombre]\").val())\n        $(\"input[name=nombre]\").val(razonsocial_afip);\n\n    var idtipoiva = (typeof data.idtipoiva == undefined || isNaN(data.idtipoiva))\n        ? evaluar_condicion(data.iva, data.mt) // Agrego selección automática de Tipo de IVA\n        : data.idtipoiva;\n    $(\"input[name=idtipoiva]\").val(idtipoiva);\n    $(\"#tipoiva_texto\").html(nombre_condicion(idtipoiva));\n\n    domicilio_afip = data.hasOwnProperty('domicilioFiscal')\n        ? data.domicilioFiscal.direccion\n        : false;\n    if (domicilio_afip\n        && $(\"input[name=domicilio]\").val() != domicilio_afip\n        && ($(\"input[name=domicilio]\").val() == ''\n            || domicilio_confirma())) {\n\n            $(\"input[name=domicilio]\").val(domicilio_afip);\n    }\n\n    // Parche forzando la ventana para seleccionar la condición de iva\n    if ($(\"input[name=domicilio]\").length && idtipoiva == 0)\n        modal('clientes', 'afip_no_responde', '');\n}\n\nfunction vaciar_campos(modificando = false)\n{\n    if (modificando == 'cuit')\n        $(\"input[name=dni]\").val(\"\");\n    else if (modificando == 'dni')\n        $(\"input[name=cuit]\").val(\"\");\n    else\n        $(\"input[name=cuit], input[name=dni]\").val(\"\");\n\n    if ($(\"input[name=nombre]\").val() == razonsocial_afip)\n        $(\"input[name=nombre]\").val(\"\");\n\n    if ($(\"input[name=domicilio]\").val() == domicilio_afip)\n        $(\"input[name=domicilio]\").val(\"\");\n\n    razonsocial_afip = '';\n    $(\"input[name=razonsocial]\").val(razonsocial_afip);\n    $('#razonsocial_texto').html(razonsocial_afip);\n    $(\"input[name=idtipoiva]\").val(0);\n    $(\"#tipoiva_texto\").html(nombre_condicion(0));\n    //$(\"#select_idlocalidad\").val(0);\n    $(\"#idlocalidad\").val(\"\");\n    $(\"#localidad\").val(\"\");\n}\n\n// Función utilizada para recordar la respuesta y no preguntarle muchas veces\nfunction domicilio_confirma()\n{\n    if (!confirma_domicilio)\n        return false;\n\n    if (!confirm('Se encontró un domicilio diferente en la base pública de AFIP, ¿Desea reemplazar el domicilio ingresado?'))\n        confirma_domicilio = false;\n\n    return confirma_domicilio;\n}\n\nfunction afip_mostrar(estado, tipo_mensaje, mensaje)\n{\n    $(\"#afip_responde, #afip_validando,  #afip_no_responde\").hide();\n    $(\"#\" + estado).show();\n    if (estado != 'afip_no_responde') // Pongo parche temporal porque AFIP falla mucho para no mostrar la alerta siempre\n        alerta_selector(tipo_mensaje, mensaje, '#cuit');\n}\n\n$(\"input[name=cuit]\").keyup(function () {\n    if (($(\"input[name=dni]\").length && !$(\"input[name=dni]\").val().toString().length)\n        && !$(\"input[name=cuit]\").val().toString().length) {\n        //vaciar_campos();\n    } else {\n        validar_cuit($(\"input[name=cuit]\"));\n    }\n});\n/*\n$(\"input[name=dni]\").keyup(function () {\n    if (!$(\"input[name=dni]\").val().toString().length\n        && !$(\"input[name=cuit]\").val().toString().length)\n        vaciar_campos();\n    else if ($(\"input[name=dni]\").val().toString().length > 0)\n        vaciar_campos('dni');\n});\n*/\n\nfunction validar_dni() {\n    const tipoDoc = $(\"select[name=tipodoc]\").val();\n    const dni = $(\"input[name=dni]\");\n    console.log(tipoDoc);\n    console.log(dni);\n    dni.prop('disabled', false);\n    if (tipoDoc == '96') {\n        $.mask.masks.dni = {mask: \"99999999\", type : \"reverse\"}\n    } else if (tipoDoc == '99') {\n        dni.prop('disabled', true);\n    } else {\n        $.mask.masks.dni = {mask: \"999999999999999\", type : \"reverse\"}\n    }\n    dni.setMask();\n}\n"}]}