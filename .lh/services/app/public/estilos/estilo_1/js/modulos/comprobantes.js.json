{"sourceFile": "services/app/public/estilos/estilo_1/js/modulos/comprobantes.js", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1726579731511, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750254694696, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -342,4 +342,165 @@\n     $lineaContenido.find('.celda').eq(1).text(nuevaCantidad.toFixed(2));\n     $lineaContenido.find('.celda').eq(9).text(datos.simbolo + ' '+ nuevototalDescuento.toFixed(2))\n     $lineaContenido.find('.celda_auto ').text(datos.simbolo + ' '+ nuevototalDescuento.toFixed(2));\n }\n+\n+function verificar_cae(idventa) {\n+\n+    if (typeof idventa == 'undefined' || idventa == 0) {\n+        $(\"#verificando_cae\").html('<div class=\"error\">No se ha definido el id de la venta para verificar el CAE</div>');\n+        return false;\n+    }\n+\n+    // Intervalos de tiempo para los reintentos (en segundos)\n+    var intervalos = [3, 5, 7, 10, 15, 20, 30, 60]; // Puedes ajustar estos valores según tus necesidades\n+    var intentoActual = 0;\n+    var maxIntentos = intervalos.length;\n+    var timeoutId = null;\n+    var intervalId = null;\n+\n+    // Función para actualizar la cuenta regresiva\n+    function actualizarCuentaRegresiva(segundosRestantes, totalSegundos, intentoNum) {\n+        if (segundosRestantes >= 0) {\n+            var porcentaje = Math.round((segundosRestantes / totalSegundos) * 100);\n+            $(\"#verificando_cae\").html(\n+                '<div class=\"warning\">' +\n+                '⏳ CAE no disponible aún. Reintento ' + intentoNum + '/' + maxIntentos +\n+                ' en ' + segundosRestantes + ' segundos...' +\n+                '<div class=\"progress-bar\" style=\"width:100%; background:#eee; height:5px; margin-top:5px;\">' +\n+                '<div style=\"width:' + porcentaje + '%; background:#f90; height:100%;\"></div>' +\n+                '</div>' +\n+                '</div>'\n+            );\n+        }\n+    }\n+\n+    function ejecutar_verificacion() {\n+        var tiempoEspera = intentoActual === 0 ? 3 : intervalos[intentoActual - 1];\n+        $(\"#verificando_cae\").html('<div class=\"info\">Verificando CAE - Intento ' + (intentoActual + 1) + ' de ' + maxIntentos + '</div>');\n+\n+        // Limpiar cualquier intervalo previo\n+        if (intervalId) {\n+            clearInterval(intervalId);\n+            intervalId = null;\n+        }\n+\n+        $.ajax({\n+            url: \"cargadores/ajax.php\",\n+            type: \"post\",\n+            data: (\n+            {\n+                t: window.t,\n+                modulo : 'ventas',\n+                id: idventa,\n+                ventana : \"verificar_cae\"\n+            }),\n+            dataType: 'json',\n+            success: function(data) {\n+                if (data && data.success === true) {\n+                    // CAE verificado exitosamente - recargar página\n+                    $(\"#verificando_cae\").html('<div class=\"success\">✅ CAE verificado exitosamente, recargando página...</div>');\n+                    setTimeout(function() {\n+                        location.reload();\n+                    }, 1000);\n+                    window.location.replace(window.location.href);\n+                } else {\n+                    // CAE aún no disponible\n+                    if (intentoActual < maxIntentos - 1) {\n+                        // Aún quedan intentos\n+                        intentoActual++;\n+                        var proximoIntervalo = intervalos[intentoActual];\n+\n+                        // Iniciar cuenta regresiva\n+                        var segundosRestantes = proximoIntervalo;\n+                        actualizarCuentaRegresiva(segundosRestantes, proximoIntervalo, intentoActual + 1);\n+\n+                        intervalId = setInterval(function() {\n+                            segundosRestantes--;\n+                            actualizarCuentaRegresiva(segundosRestantes, proximoIntervalo, intentoActual + 1);\n+\n+                            if (segundosRestantes <= 0) {\n+                                clearInterval(intervalId);\n+                                intervalId = null;\n+                            }\n+                        }, 1000);\n+\n+                        // Programar próximo intento\n+                        timeoutId = setTimeout(ejecutar_verificacion, proximoIntervalo * 1000);\n+                    } else {\n+                        // Se agotaron todos los intentos\n+                        $(\"#verificando_cae\").html('<div class=\"italica\">Factura cerrada pendiente de CAE. Más información <a href=\"ayudas.php?a=ver&id=22\" target=\"_blank\">aquí</a></div>');\n+                    }\n+                }\n+            },\n+            error: function(xhr, status, error) {\n+                console.error('Error al verificar CAE (intento ' + (intentoActual + 1) + '):', error);\n+                console.error('Status:', status);\n+                console.error('Response:', xhr.responseText);\n+\n+                if (intentoActual < maxIntentos - 1) {\n+                    // Aún quedan intentos, reintentar\n+                    intentoActual++;\n+                    var proximoIntervalo = intervalos[intentoActual];\n+                    console.log('Error en la verificación, reintentando en ' + proximoIntervalo + ' segundos...');\n+\n+                    // Iniciar cuenta regresiva para errores\n+                    var segundosRestantes = proximoIntervalo;\n+                    $(\"#verificando_cae\").html('<div class=\"error\">⚠️ Error en verificación. Reintento ' + (intentoActual + 1) + '/' + maxIntentos + ' en ' + segundosRestantes + ' segundos...</div>');\n+\n+                    intervalId = setInterval(function() {\n+                        segundosRestantes--;\n+                        if (segundosRestantes >= 0) {\n+                            $(\"#verificando_cae\").html('<div class=\"error\">⚠️ Error en verificación. Reintento ' + (intentoActual + 1) + '/' + maxIntentos + ' en ' + segundosRestantes + ' segundos...</div>');\n+                        }\n+\n+                        if (segundosRestantes <= 0) {\n+                            clearInterval(intervalId);\n+                            intervalId = null;\n+                        }\n+                    }, 1000);\n+\n+                    timeoutId = setTimeout(ejecutar_verificacion, proximoIntervalo * 1000);\n+                } else {\n+                    // Se agotaron todos los intentos\n+                    $(\"#verificando_cae\").html('<div class=\"italica\">Factura cerrada pendiente de CAE. Más información <a href=\"ayudas.php?a=ver&id=22\" target=\"_blank\">aquí</a></div>');\n+                }\n+            }\n+        });\n+    }\n+\n+    // Mostrar mensaje inicial\n+    $(\"#verificando_cae\").html('<div class=\"info\">🔄 Iniciando verificación de CAE...</div>');\n+\n+    // Cuenta regresiva inicial\n+    var segundosIniciales = 3;\n+    var segundosRestantesInicial = segundosIniciales;\n+\n+    actualizarCuentaRegresiva(segundosRestantesInicial, segundosIniciales, 1);\n+\n+    intervalId = setInterval(function() {\n+        segundosRestantesInicial--;\n+        actualizarCuentaRegresiva(segundosRestantesInicial, segundosIniciales, 1);\n+\n+        if (segundosRestantesInicial <= 0) {\n+            clearInterval(intervalId);\n+            intervalId = null;\n+        }\n+    }, 1000);\n+\n+    timeoutId = setTimeout(ejecutar_verificacion, segundosIniciales * 1000);\n+\n+    // Retornar función para cancelar si es necesario\n+    return {\n+        cancelar: function() {\n+            if (timeoutId) {\n+                clearTimeout(timeoutId);\n+                timeoutId = null;\n+            }\n+            if (intervalId) {\n+                clearInterval(intervalId);\n+                intervalId = null;\n+            }\n+            $(\"#verificando_cae\").html('<div class=\"warning\">Verificación de CAE cancelada.</div>');\n+        }\n+    };\n+}\n"}], "date": 1726579731511, "name": "Commit-0", "content": "function recalculando(datos)\n{\n    if(!datos.totaltributos) datos.totaltributos = '0,00';\n    if(!datos.descuentoenpesos) datos.descuentoenpesos = '0,00';\n    $(\"#subtotal\").html(datos.simbolo + ' ' + datos.subtotal);\n    $(\"#descuentoenpesos\").html(datos.simbolo + ' ' + datos.descuentoenpesos);\n    $(\"#descuentoenpesos\").parent().find('span.campo_nombre').html('Descuento (' + datos.descuento + ' %): ');\n    $(\"#totalnetos\").html(datos.simbolo + ' ' + datos.totalnetos);\n    $(\"#totalivas\").html(datos.simbolo + ' ' + datos.totalivas);\n    $(\"#totaltributos\").html(datos.simbolo + ' ' + datos.totaltributos);\n    $(\"#totales #total\").html(datos.simbolo + ' ' + datos.total);\n}\n\nfunction trigger_ok(e) {\n    if (e.keyCode == 13) {\n        e.preventDefault();\n        var entrada = $(\":focus\");\n        var name = entrada.attr(\"name\");\n        if (name == \"cantidad\" || name == \"precio\" || name == \"preciofinal\" || name == \"descuento\") {\n            entrada.parent().parent().parent().find('.ok').trigger(\"click\");\n        }\n    }\n}\n\nfunction revisarAgregarPago()\n{\n    let condicionventa = $(\"select[name='condicionventa'] option:selected\").val();\n    if ((condicionventa != 'cuentacorriente' && condicionventa != 'cheque')\n        && ($(\"input[name='muevesaldo']\").is(\":checked\")\n            || $(\"input[type='hidden'][name='muevesaldo']\").val() == '1')) {\n\n        $(\"#altapago\").fadeIn();\n\n    } else {\n        $(\"#altapago\").fadeOut();\n        $(\"#detallepago\").fadeOut();\n    }\n\n    if ($(\"#agregarpago\").is(\":checked\")) {\n        $(\"#detallepago\").fadeIn();\n    } else {\n        $(\"#detallepago\").fadeOut();\n    }\n\n}\n\nfunction ventas_dni(tipodoc, dni, razonsocial, idlocalidad, localidad)\n{\n    $(\"#tipodoc .campo_texto\").html(tipodoc);\n    $(\"#ventas_dni .campo_texto\").html(dni);\n    $(\"#ventas_razonsocial .campo_texto\").html(razonsocial);\n    $(\"#ventas_localidad .campo_texto\").html(localidad);\n    $(\"#idlocalidad\").val(idlocalidad);\n}\n\nfunction compras_cuit(tipoiva, modulo, id, cuit, razonsocial, idtipoiva, idlocalidad, localidad)\n{\n    $(\"#compras_tipoiva .campo_texto\").html(tipoiva);\n    $(\"#compras_cuit .campo_texto\").html(cuit);\n    $(\"#compras_razonsocial .campo_texto\").html(razonsocial);\n    $(\"#compras_localidad .campo_texto\").html(localidad);\n    $(\"#idlocalidad\").val(idlocalidad);\n    if(idtipoiva == 1)\n        var letra = 'A';\n    else\n        var letra = 'C';\n\n    recargar_formulario(false, modulo, 'Factura', id, letra);\n}\n\nfunction recargar_formulario(mensaje = false, modulo, tipocompra = false, id, letra = false) {\n    if (!mensaje || confirma(mensaje)) {\n        $.ajax(\n        {\n            url: \"cargadores/ajax.php\",\n            type: \"post\",\n            data: (\n            {\n                t: window.t,\n                modulo: modulo,\n                id: id,\n                ventana : \"recargar_formulario\",\n                letra: letra,\n                tipocompra: tipocompra\n            }),\n            success: function(data) {\n                desbloquear();\n                window.location.href = modulo+'.php?a=mod&id='+id;\n            }\n        });\n    }\n}\n\nfunction guardar_sucursal(tiposucursal, modulo, id, idtiposucursal) {\n    if (confirma('Para aplicar esta modificación debemos recargar la página. Algunos cambios como fechas y datos adicionales recién modificados pueden perderse y deben volver a cargarse. ¿Desea continuar?')) {\n        $.ajax(\n        {\n            url: \"cargadores/ajax.php\",\n            type: \"post\",\n            data: (\n            {\n                t: window.t,\n                modulo : modulo,\n                id: id,\n                ventana : \"guardar_sucursal\",\n                idtiposucursal: idtiposucursal,\n                tiposucursal: tiposucursal\n            }),\n            success: function(data) {\n                $(\"#\"+tiposucursal+\"_origen\").val(idtiposucursal);\n                desbloquear();\n                window.confirmarsalida = false;\n                window.location.replace(window.location.href); // Hay que recargar sí o sí, sino no contempla los cambios iddeposito y idlista de fullsearch\n            }\n        });\n    } else {\n        let idorigen = $(\"#\"+tiposucursal+\"_origen\").val();\n        $(\"select[name='\"+tiposucursal+\"']\").val(idorigen);\n        desbloquear();\n    }\n}\n\nfunction guardar_moneda(modulo, idoperacion, idmoneda) {\n    if (confirma('Para aplicar esta modificación debemos recargar la página. Algunos cambios como fechas y datos adicionales recién modificados pueden perderse y deben volver a cargarse. ¿Desea continuar?')) {\n        $.ajax(\n        {\n            url: \"cargadores/ajax.php\",\n            type: \"post\",\n            data: (\n            {\n                t: window.t,\n                modulo: modulo,\n                id: idoperacion,\n                ventana : \"guardar_moneda\",\n                idmoneda: idmoneda,\n            }),\n            success: function(data) {\n                desbloquear();\n                window.confirmarsalida = false;\n                window.location.replace(window.location.href); // Hay que recargar sí o sí, sino no contempla los cambios de moneda\n            }\n        });\n    } else {\n        let idorigen = $(\"#idmoneda_origen\").val();\n        $(\"select[name='idmoneda']\").val(idorigen);\n        desbloquear();\n    }\n}\n\nfunction seleccionar_tipo_caja()\n{\n    var tipoformapago = $(\"select[name='idformapago'] option:selected\").attr('data-tipoformapago');\n    $(\"select[name='idcaja']\").prop('disabled', false);\n    var tipoformapago = $(\"select[name='idformapago'] option:selected\").attr('data-tipoformapago');\n    var idformapago = $(\"select[name='idformapago'] option:selected\").val();\n\n    if (tipoformapago == 'cheque') {\n        $(\"select[name='idrelacion'] option:selected\").removeAttr(\"selected\");\n    }\n    //idformapago_5 = cheque\n    //idformapago_10 = mercadopago\n    //idformapago_13 = retenciones\n    $(\"#contenido_idformapago_5, #contenido_idformapago_10, #contenido_idformapago_13\").hide();\n    $(\"#contenido_idformapago_\" + idformapago).show();\n\n    $(\"select[name='idcaja'] option:selected\").removeAttr(\"selected\");\n    $(\"select[name='idcaja'] option\").hide();\n    $(\"select[name='idcaja'] option[data-tipoformapago='\"+tipoformapago+\"']\").show();\n    $(\"select[name='idcaja'] option[data-tipoformapago='\"+tipoformapago+\"']\").attr(\"selected\", \"selected\");\n    $(\"input[name='total']\").prop('disabled', false);\n};\n\n// TODO: Pasar a las validaciones del framework\nfunction validacion_pago()\n{\n    if ($(\"#total\").val() <= 0) {\n        alerta_selector('alerta', 'El total es obligatorio', \"input[name='total']\");\n        return false;\n    } else {\n        var tipoformapago = $(\"select[name='idformapago'] option:selected\").attr('data-tipoformapago');\n        if (tipoformapago == 'cheque') {\n            if ($(\"#en_cartera\").css('display') == \"block\" && !idcheque) {\n                    alerta_selector('alerta', 'Seleccionar Cheque', \"select[name='idcheque']\");\n                    return false;\n            } else if (typeof idcheque === 'undefined') {\n\n                if (!$(\"#fechacobro\").val()) {\n                    alerta_selector('alerta', 'La fecha es obligatoria', \"input[name='fechacobro']\");\n                    return false;\n\n                }\n                if (!$(\"#titular\").val()) {\n                    alerta_selector('alerta', 'El titular es obligatorio', \"input[name='titular']\");\n                    return false;\n                }\n                if (!$(\"#numero\").val()) {\n                    alerta_selector('alerta', 'El número de cheque es obligatorio', \"input[name='numero']\");\n                    return false;\n                }\n            }\n        }\n        /*if (tipoformapago == 'retencion') {\n            if (!$(\"#tributo\").val()) {\n                alerta_selector('alerta', 'El nombre de retención es obligatorio', \"input[name='tributo']\");\n                return false;\n            }\n        }\n        */\n        $(\"select[name='idcaja']\").prop('disabled', false);\n        $('input[name=\"total\"]').prop('disabled', false);\n        return true;\n    }\n};\n\nfunction validacion_ventaspagos_altamod(boton)\n{\n    var formapago = $(\"select[name='idformapago'] option:selected\");\n    return boton == 'Cancelar'\n        ? true\n        : validacion_pago();\n}\n\nfunction validacion_compraspagos_altamod(boton)\n{\n    var formapago = $(\"select[name='idformapago'] option:selected\");\n    return boton == 'Cancelar'\n        ? true\n        : validacion_pago();\n}\n\nfunction seleccionar_cheque_encartera()\n{\n    var totalcheque = $(\"select[name='idrelacion'] option:selected\").attr('data-totalcheque');\n    var idcaja = $(\"select[name='idrelacion'] option:selected\").attr('data-idcaja');\n    $(\"input[value='Agregar']\").prop(\"disabled\", false);\n\n    $('input[name=\"total\"]').val(totalcheque);\n    if(totalcheque) {\n        $('input[name=\"total\"]').prop('disabled', true);\n    } else {\n        $('input[name=\"total\"]').prop('disabled', false);\n    }\n\n    if(typeof idcaja === 'undefined') {\n        $('select[name=\"idcaja\"]').prop('disabled', false);\n        $(\"select[name='idcaja']\").prop('disabled', true);\n    } else if(!idcaja) { //si el cheque viene sin idcaja, mando alert\n        $(\"select[name='idcaja']\").prop('disabled', true);\n        $(\"input[value='Agregar']\").prop(\"disabled\", true);\n        alerta('No hay cajas abiertas en la cual agregar el movimiento. Re-abra o abra una nueva instancia de esa caja para poder agregar esta orden de pago');\n    } else {\n        $(\"select[name='idcaja']\").val(idcaja);\n        $(\"select[name='idcaja']\").prop('disabled', true);\n    }\n}\n\nfunction recargar_ventana_mercado(tipo_ventana)\n{\n    var idtienda = $(\"select[name='idtienda'] option:selected\").val();\n    window.location.href = 'ventas.php?a='+tipo_ventana+'&idtienda='+idtienda;\n}\n\n$(function() {\n\n    $(\"a[href='vermas']\").live(\"click\", function(e) {\n        e.preventDefault();\n        $(this).parent().parent().find(\".oculto\").slideDown();\n        $(this).attr(\"href\",\"vermenos\");\n        $(this).html('<img title=\"\" src=\"estilos/estilo_1/images/vermenos.png\">');\n        $(this).parent().parent().find(\".oculto textarea\").redactor();\n    });\n    $(\"a[href='vermenos']\").live(\"click\", function(e) {\n        e.preventDefault();\n        $(this).parent().parent().find(\".oculto\").slideUp();\n        $(this).attr(\"href\",\"vermas\");\n        $(this).html('<img title=\"\" src=\"estilos/estilo_1/images/vermas.png\">');\n    });\n\n    $(\"input[name='vencimiento1']\").change(function() {\n        if ($(\"input[name='vencimiento1']\").val()) {\n            $(\"#div_vencimiento2\").fadeIn(400);\n        } else {\n            $(\"#div_vencimiento2\").fadeOut(400);\n        }\n    });\n\n    $(\"select[name='concepto']\").change(function() {\n        if ($(\"select[name='concepto'] option:selected\").val() > 1) {\n            $(\"#periodo_facturado\").fadeIn(400);\n        } else {\n            $(\"#periodo_facturado\").fadeOut(400);\n        }\n    });\n\n    $(\"#linea_agregar #descuento\").live(\"change\", function() {\n        if (parseFloat($(\"#linea_agregar #descuento\").val()) > 100)\n            $(\"#linea_agregar #descuento\").val(100);\n    });\n\n    $(\"a\").click(function(e) {\n        $('input[name=\"total\"]').prop('disabled', false);\n        $('select[name=\"idcaja\"]').prop('disabled', false);\n        var tipocheque = $(e.target).text();\n        if(tipocheque != 'Propio') {\n            //$('input[name=\"total\"]').val('');\n            $(\"select[name='idrelacion']\").val(0);\n        }\n    });\n\n    if ($('#tipocheque').val() == 'tercero') {\n        $('li:has(a[href=\"#en_cartera\"])').click();\n//        idcheque = <?php echo json_encode($cheques['idcheque']);?>;\n        $(\"select[name='idcheque']\").val(idcheque).change;\n        $('input[name=\"total\"]').prop('disabled', true);\n        $('#fechacobro').val('');\n        $('#titular').val('');\n        $('#numero').val('');\n    }\n\n    $(\"#agregarpago\").click(function() {\n        revisarAgregarPago();\n    });\n    $(\"select[name='condicionventa']\").change(function() {\n        revisarAgregarPago();\n    });\n    $(\"input[name='muevesaldo']\").click(function() {\n        revisarAgregarPago();\n    });\n    revisarAgregarPago();\n});\n"}]}