{"sourceFile": "services/app/public/procesos/verificar_cae.php", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1750187775911, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750187930028, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,6 @@\n+<?php\n+\n+if (!$id)\n+    script_flotante('alerta', $i18n_funciones['url_incorrecto']);\n+\n+    script_flotante('alerta', 'vamo bien');\n\\ No newline at end of file\n"}, {"date": 1750252973645, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,9 +6,9 @@\n $id = recibir_variable('id');\n $accion = recibir_variable('accion');\n \n // Simular tiempo de procesamiento\n-sleep(1);\n+sleep(8);\n \n // Por ahora devolver siempre false para pruebas\n // En producción aquí iría la lógica real para verificar el CAE\n $resultado = array(\n"}, {"date": 1750253347049, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,15 +1,10 @@\n <?php\n-// Archivo para verificar CAE\n-// Por ahora devuelve siempre false para probar la funcionalidad de reintentos\n \n-// Obtener el ID de la venta/comprobante\n-$id = recibir_variable('id');\n-$accion = recibir_variable('accion');\n-\n // Simular tiempo de procesamiento\n sleep(8);\n \n+\n // Por ahora devolver siempre false para pruebas\n // En producción aquí iría la lógica real para verificar el CAE\n $resultado = array(\n     'success' => false,\n"}], "date": 1750187775911, "name": "Commit-0", "content": ""}]}