{"sourceFile": "services/app/public/procesos/tributos_baja.php", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1749307449502, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1749307449502, "name": "Commit-0", "content": "<?php\n\n$comprobante = array_sql(consulta_sql(\"SELECT * FROM \".$GLOBALS['modulo'].\" WHERE \".$GLOBALS['idmodulo'].\" =\n    (SELECT \".$GLOBALS['idmodulo'].\" FROM tributosx\".$GLOBALS['modulo'].\" WHERE idtributox\".$GLOBALS['modulo_singular'].\" = '$id')\"));\n\nif (!$comprobante) {\n    script_flotante('alerta', $i18n[966]);\n    exit();\n}\n\n$buscar_monedas = [\n    $GLOBALS['modulo'] => $comprobante[$GLOBALS['idmodulo']],\n    $GLOBALS['tercero'] => $comprobante[$GLOBALS['idtercero']]\n];\n// if ($comprobante['tiporelacion'] == 'servicio')\n//     $buscar_monedas['servicios'] = $comprobante['idrelacion'];\n$idmonedas = idmonedas($buscar_monedas);\n\nconsulta_sql(\n    \"DELETE FROM tributosx{$GLOBALS['modulo']}\n    WHERE idtributox\".substr($GLOBALS['modulo'], 0, -1).\" = '$id'\n    LIMIT 1\");\n\n// Recalculo el comprobante y actualizo saldo\n$recalculada = comprobantes_recalculando($comprobante[$GLOBALS['idmodulo']]);\nif ($comprobante['estado'] == 'cerrado'\n    && $comprobante['muevesaldo']\n    && $comprobante['total'] != $recalculada['total']) {\n    $diferencia = ($comprobante['operacioninversa'] ? -1 : 1) * ($recalculada['total'] - $comprobante['total']);\n    actualizar_saldo($GLOBALS['modulo'], $comprobante[$GLOBALS['idmodulo']], cotizacion($idmonedas[$GLOBALS['modulo']], $idmonedas[$GLOBALS['tercero']], $diferencia));\n    actualizar_saldo($GLOBALS['tercero'], $comprobante[$GLOBALS['idtercero']], cotizacion($idmonedas[$GLOBALS['tercero']], $idmonedas[$GLOBALS['modulo']], $diferencia));\n    if ($comprobante['tiporelacion'] == 'servicio')\n        actualizar_saldo($GLOBALS['modulo'].'xservicios', $comprobante['idrelacion'], $diferencia); //No aplica aún\n}\n"}]}