{"sourceFile": "services/app/public/ventanas/saas_faqs_mod.php", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1749161869005, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1749161869005, "name": "Commit-0", "content": "<?php\n\nswitch ($boton) {\n    case $i18n[1]: //Aceptar\n    \t$datos = recibir_matriz(array('titulo', 'tags', 'texto', 'url_amigable', 'tipo', 'curso', 'publico', 'orden', 'schema'));\n        consulta_sql(\"UPDATE faqs SET\n\t\t\t\t\ttitulo = '\".$datos['titulo'].\"',\n\t\t\t\t\ttags = '\".$datos['tags'].\"',\n\t\t\t\t\ttexto = '\".filtro_link($datos['texto']).\"',\n\t\t\t\t\turl_amigable = '\".$datos['url_amigable'].\"',\n\t\t\t\t\ttipo = '\".$datos['tipo'].\"',\n\t\t\t\t\tcurso = '\".$datos['curso'].\"',\n\t\t\t\t\tpublico = '\".$datos['publico'].\"',\n\t\t\t\t\torden = '\".$datos['orden'].\"',\n\t\t\t\t\t`schema` = '\".strip_tags($datos['schema']).\"'\n\t\t\t\tWHERE idfaq = '\".$id.\"'\", 'saasargentina');\n        ir_atras();\n        break;\n\n    case $i18n[3]: //Cancelar\n        ir_atras();\n        break;\n\n    default:\n    \t$datos = array_sql(consulta_sql(\"SELECT * FROM faqs WHERE idfaq = '\".$id.\"'\", 'saasargentina'));\n    \tbreak;\n}\n\n$tipos = [\n\t['id' => 'faq', 'valor' => $i18n[249]],\n\t['id' => 'consejos', 'valor' => $i18n[250]],\n\t['id' => 'lecciones', 'valor' => $i18n[251]],\n\t['id' => 'flotantes', 'valor' => $i18n[252]]\n];\n\n$cursos = [\n\t['id' => '', 'valor' => ''],\n\t['id' => 'Los primeros pasos', 'valor' => $i18n[253]],\n\t['id' => 'Funcionalidades básicas', 'valor' => $i18n[254]]\n];\n\n$publicos = [\n\t['id' => 'web', 'valor' => $i18n[255]],\n\t['id' => 'sistema', 'valor' => $i18n[256]],\n\t['id' => 'privado', 'valor' => $i18n[257]]\n];\n\nventana_inicio($i18n[147]);\n{\n\tcontenido_inicio();\n\t{\n\t\tentrada('texto', 'titulo', $i18n[142], $datos['titulo'], '50', '200');\n\t\tentrada('texto', 'tags', $i18n[143], $datos['tags'], '50', '100');\n\t\tentrada('texto', 'url_amigable', $i18n[247], $datos['url_amigable'], '50', '100');\n\t\tselector_array('tipo', $i18n[244], $datos['tipo'], '50', $tipos);\n\t\tselector_array('curso', $i18n[245], $datos['curso'], '50', $cursos);\n\t\tselector_array('publico', $i18n[258], $datos['publico'], '50', $publicos);\n\t\tentrada('numeros', 'orden', $i18n[248], $datos['orden'], '10');\n\t\tarea('texto', $i18n[144], $datos['texto']);\n\t\tarea('schema', $i18n[256], $datos['schema']);\n\t}\n\tcontenido_fin();\n\tbotones(array(array('valor' => $i18n[1]), array('valor' => $i18n[3])));\n}\nventana_fin();\n\n?>\n"}]}