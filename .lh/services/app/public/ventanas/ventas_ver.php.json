{"sourceFile": "services/app/public/ventanas/ventas_ver.php", "activeCommit": 0, "commits": [{"activePatchIndex": 25, "patches": [{"date": 1732633685976, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1732634060747, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -237,12 +237,16 @@\n     // Moneda\n     contenido_inicio($i18n[390], '50', true);\n     {\n         texto('titulo', $i18n[390], $venta['moneda'].' ('.$venta['simbolo'].')', 'auto');\n-        if ($venta['idmoneda'] != 1 && $venta['estado'] == 'abierto')\n+        if ($venta['idmoneda'] != 1 && $venta['estado'] == 'abierto') {\n             texto('moneda', $i18n_funciones[331], $venta['cotizacion'], 'auto');\n-        else if ($venta['idmoneda'] != 1)\n+            texto('moneda', $i18n_funciones[332], cotizacion_con_valores(1, $venta['cotizacion'], $venta['total']), 'auto');\n+\n+        } else if ($venta['idmoneda'] != 1) {\n             texto('moneda', $i18n_funciones[330], $venta['cotizacion_anterior'], 'auto');\n+\n+        }\n     }\n     contenido_fin();\n \n     salto_linea();\n@@ -315,9 +319,9 @@\n         contenido_fin();\n \n         contenido_inicio(false, '33', false, false, false, ['id' => \"totales\", 'class' => \"desglose\"]);\n         {\n-            if ($tipoventa['discrimina'] == 'C' || $tipoventa['discrimina'] == 'B') {\n+            if ($tipoventa['discrimina'] == 'A' || $tipoventa['discrimina'] == 'B') {\n                 enlaces(false, array(\n                     array('tipo' => 'flotante', 'modulo' => $modulo, 'url' => 'comprobantes_ver_totales', 'id' => $id, 'valor' => $i18n[195]),\n                     ));\n             }\n"}, {"date": 1732634081932, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -243,8 +243,9 @@\n             texto('moneda', $i18n_funciones[332], cotizacion_con_valores(1, $venta['cotizacion'], $venta['total']), 'auto');\n \n         } else if ($venta['idmoneda'] != 1) {\n             texto('moneda', $i18n_funciones[330], $venta['cotizacion_anterior'], 'auto');\n+            texto('moneda', $i18n_funciones[332], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');\n \n         }\n     }\n     contenido_fin();\n"}, {"date": 1732634127190, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -243,9 +243,9 @@\n             texto('moneda', $i18n_funciones[332], cotizacion_con_valores(1, $venta['cotizacion'], $venta['total']), 'auto');\n \n         } else if ($venta['idmoneda'] != 1) {\n             texto('moneda', $i18n_funciones[330], $venta['cotizacion_anterior'], 'auto');\n-            texto('moneda', $i18n_funciones[332], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');\n+            texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');\n \n         }\n     }\n     contenido_fin();\n"}, {"date": 1732634158982, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -239,9 +239,9 @@\n     {\n         texto('titulo', $i18n[390], $venta['moneda'].' ('.$venta['simbolo'].')', 'auto');\n         if ($venta['idmoneda'] != 1 && $venta['estado'] == 'abierto') {\n             texto('moneda', $i18n_funciones[331], $venta['cotizacion'], 'auto');\n-            texto('moneda', $i18n_funciones[332], cotizacion_con_valores(1, $venta['cotizacion'], $venta['total']), 'auto');\n+            texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion'], $venta['total']), 'auto');\n \n         } else if ($venta['idmoneda'] != 1) {\n             texto('moneda', $i18n_funciones[330], $venta['cotizacion_anterior'], 'auto');\n             texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');\n"}, {"date": 1738591366112, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -243,9 +243,9 @@\n             texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion'], $venta['total']), 'auto');\n \n         } else if ($venta['idmoneda'] != 1) {\n             texto('moneda', $i18n_funciones[330], $venta['cotizacion_anterior'], 'auto');\n-            texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');\n+            // texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');\n \n         }\n     }\n     contenido_fin();\n"}, {"date": 1738591568376, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -243,9 +243,9 @@\n             texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion'], $venta['total']), 'auto');\n \n         } else if ($venta['idmoneda'] != 1) {\n             texto('moneda', $i18n_funciones[330], $venta['cotizacion_anterior'], 'auto');\n-            // texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');\n+            texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');\n \n         }\n     }\n     contenido_fin();\n"}, {"date": 1738592135548, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n         usuarios.nombre AS usuario, tablas_condiciones.nombre AS tipoiva, categorias_ventas.discrimina,\n         categorias_localidades.nombre AS localidad,\n         listas.nombre AS lista, depositos.nombre AS deposito,\n         monedas.idmoneda, monedas.nombre AS moneda, monedas.simbolo, monedas.cotizacion,\n-        (SELECT cotizaciones.cotizacion FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND cotizaciones.fechayhora = (SELECT MAX(fechayhora) FROM cotizaciones WHERE fechayhora < ventas.fecha)) AS cotizacion_anterior\n+        (SELECT cotizaciones.cotizacion FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND cotizaciones.fechayhora = (SELECT MAX(fechayhora) FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND fechayhora < ventas.fecha)) AS cotizacion_anterior\n     FROM ventas\n         LEFT JOIN usuarios ON ventas.idusuario = usuarios.idusuario\n         LEFT JOIN tablas_condiciones ON ventas.idtipoiva = tablas_condiciones.idtipoiva\n         LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n"}, {"date": 1747920551433, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -242,8 +242,10 @@\n             texto('moneda', $i18n_funciones[331], $venta['cotizacion'], 'auto');\n             texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion'], $venta['total']), 'auto');\n \n         } else if ($venta['idmoneda'] != 1) {\n+            if (!$venta['cotizacion_anterior'])\n+                $venta['cotizacion_anterior'] = campo_sql(consulta_sql(\"SELECT cotizacion FROM cotizaciones WHERE idmoneda = \".$venta['idmoneda'].\"' ORDER BY fechayhora LIMIT 1\"));\n             texto('moneda', $i18n_funciones[330], $venta['cotizacion_anterior'], 'auto');\n             texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');\n \n         }\n"}, {"date": 1747920605314, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n         usuarios.nombre AS usuario, tablas_condiciones.nombre AS tipoiva, categorias_ventas.discrimina,\n         categorias_localidades.nombre AS localidad,\n         listas.nombre AS lista, depositos.nombre AS deposito,\n         monedas.idmoneda, monedas.nombre AS moneda, monedas.simbolo, monedas.cotizacion,\n-        (SELECT cotizaciones.cotizacion FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND cotizaciones.fechayhora = (SELECT MAX(fechayhora) FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND fechayhora < ventas.fecha)) AS cotizacion_anterior\n+        (SELECT cotizaciones.cotizacion FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND cotizaciones.fechayhora = (SELECT MAX(fechayhora) FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND fechayhora < ventas.closed_at)) AS cotizacion_anterior\n     FROM ventas\n         LEFT JOIN usuarios ON ventas.idusuario = usuarios.idusuario\n         LEFT JOIN tablas_condiciones ON ventas.idtipoiva = tablas_condiciones.idtipoiva\n         LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n@@ -242,10 +242,8 @@\n             texto('moneda', $i18n_funciones[331], $venta['cotizacion'], 'auto');\n             texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion'], $venta['total']), 'auto');\n \n         } else if ($venta['idmoneda'] != 1) {\n-            if (!$venta['cotizacion_anterior'])\n-                $venta['cotizacion_anterior'] = campo_sql(consulta_sql(\"SELECT cotizacion FROM cotizaciones WHERE idmoneda = \".$venta['idmoneda'].\"' ORDER BY fechayhora LIMIT 1\"));\n             texto('moneda', $i18n_funciones[330], $venta['cotizacion_anterior'], 'auto');\n             texto('moneda', $i18n[400], cotizacion_con_valores(1, $venta['cotizacion_anterior'], $venta['total']), 'auto');\n \n         }\n"}, {"date": 1750187308453, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n         usuarios.nombre AS usuario, tablas_condiciones.nombre AS tipoiva, categorias_ventas.discrimina,\n         categorias_localidades.nombre AS localidad,\n         listas.nombre AS lista, depositos.nombre AS deposito,\n         monedas.idmoneda, monedas.nombre AS moneda, monedas.simbolo, monedas.cotizacion,\n-        (SELECT cotizaciones.cotizacion FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND cotizaciones.fechayhora = (SELECT MAX(fechayhora) FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND fechayhora < ventas.closed_at)) AS cotizacion_anterior\n+        (SELECT cotizaciones.cotizacion FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND cotizaciones.fechayhora = (SELECT MAX(fechayhora) FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND fechayhora < ventas.fecha)) AS cotizacion_anterior\n     FROM ventas\n         LEFT JOIN usuarios ON ventas.idusuario = usuarios.idusuario\n         LEFT JOIN tablas_condiciones ON ventas.idtipoiva = tablas_condiciones.idtipoiva\n         LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n@@ -110,10 +110,13 @@\n     contenido_inicio($i18n[34], '50');\n     {\n         texto('texto', $i18n[106], $i18n[$venta['estado']], 'auto', false, $venta['estado']);\n         texto('texto', $i18n[49], $tipoventa['nombre']);\n-        if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] != 'aprobado')\n+        if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] != 'aprobado') {\n             texto('texto', $i18n[50], $numeroventa . $i18n[380], 'auto', false, 'alerta');\n+            if ($venta['estadocae'] == 'pendiente')\n+                texto('texto', $i18n[51], $i18n[381], 'auto', false, 'alerta');\n+        }\n         else\n             texto('texto', $i18n[50], $numeroventa);\n         $title_fechas =\n             ($venta['closed_at'] != '0000-00-00 00:00:00'\n"}, {"date": 1750187349050, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -110,13 +110,10 @@\n     contenido_inicio($i18n[34], '50');\n     {\n         texto('texto', $i18n[106], $i18n[$venta['estado']], 'auto', false, $venta['estado']);\n         texto('texto', $i18n[49], $tipoventa['nombre']);\n-        if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] != 'aprobado') {\n+        if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] != 'aprobado')\n             texto('texto', $i18n[50], $numeroventa . $i18n[380], 'auto', false, 'alerta');\n-            if ($venta['estadocae'] == 'pendiente')\n-                texto('texto', $i18n[51], $i18n[381], 'auto', false, 'alerta');\n-        }\n         else\n             texto('texto', $i18n[50], $numeroventa);\n         $title_fechas =\n             ($venta['closed_at'] != '0000-00-00 00:00:00'\n@@ -135,8 +132,11 @@\n         } else if ($tipoventa['tipofacturacion'] == 'electronico') {\n             texto('texto', $i18n[140], ucfirst($venta['estadocae']), 'auto', false, 'alerta');\n             if ($venta['obscae'])\n                 texto('texto', $i18n[72], $venta['obscae']);\n+            if ($venta['estadocae'] == 'pendiente')\n+                texto('texto', $i18n[51], $i18n[381], 'auto', false, 'alerta');\n+\n         }\n         if ($tipoventa['tienesituacion'])\n             texto('texto', $i18n[155], ($venta['situacion'] ? $i18n[$venta['situacion']] : $i18n['sin_especificar']), 'auto', false, 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_situacion', 'title' => $i18n[382]));\n     }\n"}, {"date": 1750187493863, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -132,11 +132,15 @@\n         } else if ($tipoventa['tipofacturacion'] == 'electronico') {\n             texto('texto', $i18n[140], ucfirst($venta['estadocae']), 'auto', false, 'alerta');\n             if ($venta['obscae'])\n                 texto('texto', $i18n[72], $venta['obscae']);\n-            if ($venta['estadocae'] == 'pendiente')\n-                texto('texto', $i18n[51], $i18n[381], 'auto', false, 'alerta');\n+            if ($venta['estadocae'] == 'pendiente') {\n+                bloque_inicio('verificando_cae');\n+                texto('texto', '', $i18n[401], 'auto');\n+                bloque_fin();\n+            }\n \n+\n         }\n         if ($tipoventa['tienesituacion'])\n             texto('texto', $i18n[155], ($venta['situacion'] ? $i18n[$venta['situacion']] : $i18n['sin_especificar']), 'auto', false, 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_situacion', 'title' => $i18n[382]));\n     }\n"}, {"date": 1750187525408, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -134,9 +134,9 @@\n             if ($venta['obscae'])\n                 texto('texto', $i18n[72], $venta['obscae']);\n             if ($venta['estadocae'] == 'pendiente') {\n                 bloque_inicio('verificando_cae');\n-                texto('texto', '', $i18n[401], 'auto');\n+                texto('italica', '', $i18n[401], 'auto');\n                 bloque_fin();\n             }\n \n \n"}, {"date": 1750187544914, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -134,9 +134,8 @@\n             if ($venta['obscae'])\n                 texto('texto', $i18n[72], $venta['obscae']);\n             if ($venta['estadocae'] == 'pendiente') {\n                 bloque_inicio('verificando_cae');\n-                texto('italica', '', $i18n[401], 'auto');\n                 bloque_fin();\n             }\n \n \n"}, {"date": 1750187615565, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -347,4 +347,12 @@\n         gratis();\n     </script>\n <?php\n }\n+\n+if ($venta['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente') {\n+?>\n+    <script charset=\"utf-8\">\n+        verificando_cae();\n+    </script>\n+<?php\n+}\n\\ No newline at end of file\n"}, {"date": 1750187653229, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -351,8 +351,8 @@\n \n if ($venta['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente') {\n ?>\n     <script charset=\"utf-8\">\n-        verificando_cae();\n+        verificando_cae(<?php echo $venta['idventa']; ?>?>);\n     </script>\n <?php\n }\n\\ No newline at end of file\n"}, {"date": 1750187994734, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -351,8 +351,8 @@\n \n if ($venta['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente') {\n ?>\n     <script charset=\"utf-8\">\n-        verificando_cae(<?php echo $venta['idventa']; ?>?>);\n+        verificar_cae(<?php echo $venta['idventa']; ?>?>);\n     </script>\n <?php\n }\n\\ No newline at end of file\n"}, {"date": 1750188626456, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -351,8 +351,8 @@\n \n if ($venta['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente') {\n ?>\n     <script charset=\"utf-8\">\n-        verificar_cae(<?php echo $venta['idventa']; ?>?>);\n+        verificar_cae(<?php echo $venta['idventa']; ?>);\n     </script>\n <?php\n }\n\\ No newline at end of file\n"}, {"date": 1750188652296, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -351,8 +351,9 @@\n \n if ($venta['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente') {\n ?>\n     <script charset=\"utf-8\">\n+        console_log('llamo a verificar_cae')\n         verificar_cae(<?php echo $venta['idventa']; ?>);\n     </script>\n <?php\n }\n\\ No newline at end of file\n"}, {"date": 1750188667913, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -348,9 +348,9 @@\n     </script>\n <?php\n }\n \n-if ($venta['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente') {\n+if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente') {\n ?>\n     <script charset=\"utf-8\">\n         console_log('llamo a verificar_cae')\n         verificar_cae(<?php echo $venta['idventa']; ?>);\n"}, {"date": 1750188677330, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -351,9 +351,8 @@\n \n if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente') {\n ?>\n     <script charset=\"utf-8\">\n-        console_log('llamo a verificar_cae')\n         verificar_cae(<?php echo $venta['idventa']; ?>);\n     </script>\n <?php\n }\n\\ No newline at end of file\n"}, {"date": 1750189126093, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -351,8 +351,20 @@\n \n if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente') {\n ?>\n     <script charset=\"utf-8\">\n-        verificar_cae(<?php echo $venta['idventa']; ?>);\n+        // Usamos jQuery para ejecutar la función después de que se haya cargado la página y todos los scripts\n+        $(document).ready(function() {\n+            // Verificamos que la función exista antes de llamarla\n+            if (typeof verificar_cae === 'function') {\n+                verificar_cae(<?php echo $venta['idventa']; ?>);\n+            } else {\n+                console.error('La función verificar_cae no está disponible');\n+                // Alternativa: Cargar el script dinámicamente y luego llamar a la función\n+                $.getScript('<?php echo $_SESSION['servidor_url']; ?>estilos/estilo_<?php echo $_SESSION['usuario_idestilo']; ?>/js/modulos/comprobantes.js?hash=<?php echo $_SESSION['hash']; ?>', function() {\n+                    verificar_cae(<?php echo $venta['idventa']; ?>);\n+                });\n+            }\n+        });\n     </script>\n <?php\n }\n\\ No newline at end of file\n"}, {"date": 1750189168931, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -359,11 +359,9 @@\n                 verificar_cae(<?php echo $venta['idventa']; ?>);\n             } else {\n                 console.error('La función verificar_cae no está disponible');\n                 // Alternativa: Cargar el script dinámicamente y luego llamar a la función\n-                $.getScript('<?php echo $_SESSION['servidor_url']; ?>estilos/estilo_<?php echo $_SESSION['usuario_idestilo']; ?>/js/modulos/comprobantes.js?hash=<?php echo $_SESSION['hash']; ?>', function() {\n-                    verificar_cae(<?php echo $venta['idventa']; ?>);\n-                });\n+\n             }\n         });\n     </script>\n <?php\n"}, {"date": 1750189190726, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -353,16 +353,9 @@\n ?>\n     <script charset=\"utf-8\">\n         // Usamos jQuery para ejecutar la función después de que se haya cargado la página y todos los scripts\n         $(document).ready(function() {\n-            // Verificamos que la función exista antes de llamarla\n-            if (typeof verificar_cae === 'function') {\n-                verificar_cae(<?php echo $venta['idventa']; ?>);\n-            } else {\n-                console.error('La función verificar_cae no está disponible');\n-                // Alternativa: Cargar el script dinámicamente y luego llamar a la función\n-\n-            }\n+            verificar_cae(<?php echo $venta['idventa']; ?>);\n         });\n     </script>\n <?php\n }\n\\ No newline at end of file\n"}, {"date": 1750254899381, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -137,9 +137,8 @@\n                 bloque_inicio('verificando_cae');\n                 bloque_fin();\n             }\n \n-\n         }\n         if ($tipoventa['tienesituacion'])\n             texto('texto', $i18n[155], ($venta['situacion'] ? $i18n[$venta['situacion']] : $i18n['sin_especificar']), 'auto', false, 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_situacion', 'title' => $i18n[382]));\n     }\n"}], "date": 1732633685976, "name": "Commit-0", "content": "<?php\n$venta = array_sql(consulta_sql(\n    \"SELECT ventas.*,\n        usuarios.nombre AS usuario, tablas_condiciones.nombre AS tipoiva, categorias_ventas.discrimina,\n        categorias_localidades.nombre AS localidad,\n        listas.nombre AS lista, depositos.nombre AS deposito,\n        monedas.idmoneda, monedas.nombre AS moneda, monedas.simbolo, monedas.cotizacion,\n        (SELECT cotizaciones.cotizacion FROM cotizaciones WHERE cotizaciones.idmoneda = monedas.idmoneda AND cotizaciones.fechayhora = (SELECT MAX(fechayhora) FROM cotizaciones WHERE fechayhora < ventas.fecha)) AS cotizacion_anterior\n    FROM ventas\n        LEFT JOIN usuarios ON ventas.idusuario = usuarios.idusuario\n        LEFT JOIN tablas_condiciones ON ventas.idtipoiva = tablas_condiciones.idtipoiva\n        LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n        LEFT JOIN categorias_localidades ON ventas.idlocalidad = categorias_localidades.idlocalidad\n        LEFT JOIN listas ON ventas.idlista = listas.idlista\n        LEFT JOIN depositos ON ventas.iddeposito = depositos.iddeposito\n        LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda\n    WHERE idventa = '$id'\"));\n$cliente = array_sql(consulta_sql(\n    \"SELECT clientes.*,\n        tablas_condiciones.nombre AS tipoiva,\n        categorias_localidades.nombre AS localidad\n    FROM clientes\n        LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad\n        LEFT JOIN tablas_condiciones ON clientes.idtipoiva = tablas_condiciones.idtipoiva\n    WHERE idcliente = '\".$venta['idcliente'].\"'\"));\n$servicio = $venta['tiporelacion'] == 'servicio' && $venta['idrelacion']\n    ? array_sql(consulta_sql(\n        \"SELECT idservicio, titulo\n        FROM servicios\n        where idservicio = \".$venta['idrelacion']))\n    : null;\n\n$temp_desplegable = comprobantes_habilitados_clientes($venta['idcliente']);\n\n$tipoventa = array_sql(consulta_sql(\"SELECT * FROM categorias_ventas WHERE idtipoventa='\".$venta['idtipoventa'].\"' LIMIT 1\"));\n$numeroventa = $tipoventa['letra'].completar_numero($tipoventa['puntodeventa'], 5).'-'.completar_numero($venta['numero'], 8);\n$discrimina = $tipoventa['discrimina'];\n\nif ($venta['estado'] == 'abierto' && !$_SESSION['perfil_ventas_alta']) {\n    $temp_boton_mod = array('tipo' => 'imagen', 'a' => 'mod_no', 'title' => $i18n[57]);\n} elseif (($venta['estado'] == 'cerrado' || $venta['estado'] == 'anulado') && !$_SESSION['perfil_ventas_mod']) {\n    $temp_boton_mod = array('tipo' => 'imagen', 'a' => 'mod_no', 'title' => $i18n[58]);\n} elseif (($venta['estado'] == 'cerrado' || $venta['estado'] == 'anulado') && $venta['idusuario'] != $_SESSION['usuario_idusuario'] && !$_SESSION['perfil_ventas_mod_todos']) {\n    $temp_boton_mod = array('tipo' => 'imagen', 'a' => 'mod_no', 'title' => $i18n[59]);\n} else {\n    $temp_boton_mod = array('tipo' => 'imagen', 'url' => 'ventas.php?a=mod&id='.$venta['idventa'], 'a' => 'mod', 'title' => $i18n[60]);\n}\n\n$temp_confirma = $i18n[19].'\\n';\nif ($venta['estado'] == 'abierto') {\n    if ($tipoventa['muevesaldo'])\n        $temp_confirma.= '\\n'.$i18n[132].'\\n'.$i18n[137];\n    if ($tipoventa['muevestock'])\n        $temp_confirma.= '\\n'.$i18n[133];\n} elseif ($venta['estado'] == 'cerrado') {\n    if ($tipoventa['muevesaldo'])\n        $temp_confirma.= '\\n'.$i18n[134].'\\n'.$i18n[137];\n    if ($tipoventa['muevestock'])\n        $temp_confirma.= '\\n'.$i18n[135];\n}\n\nif ($venta['estado'] == 'abierto')\n    $temp_alerta_exportar = $i18n[210];\nelseif ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] != 'aprobado')\n    $temp_alerta_exportar = $i18n[211];\nelse\n    $temp_alerta_exportar = false;\n\n// Proceso para verificar si un cae está correcto\n/* NO Lo uso por ahora\nif (recibir_variable('verificar', true)) {\n    if ($tipoventa['tipofacturacion'] != 'electronico' || $venta['estado'] != 'cerrado' || mb_strlen($venta['cae']) != 14) {\n        mensajes_alta($i18n[317], 'Alerta');\n\n    } else if (bloquear_wsfe()) {\n        if (rece1_verificar($id))\n            mensajes_alta($i18n[318], 'Confirmacion');\n        else\n            mensajes_alta($i18n[319], 'Alerta');\n        desbloquear_wsfe();\n    }\n\n}\n*/\n\nif ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'pendiente')\n    antiafip();\n\nif ($venta['estado'] != 'abierto' && $venta['muevesaldo'])\n    controlar_saldo('ventas', $id);\n$venta = controlar_moneda('ventas', $venta);\n\nmensajes_efimeros();\n\nventana_inicio($i18n[31].$nombre, '100', array(\n    array('tipo' => 'desplegable', 'a' => 'alta_relacionada', 'title' => $i18n[247],\n        'url' => 'ventas_alta_auto.php?a=alta&idventa='.$id.($venta['discrimina'] == 'R' ? '&act_precios=1' : ''),\n        'desplegable' => $temp_desplegable,\n        'desplegable_vacio' => (!count($temp_desplegable) ? $i18n[246].$cliente['tipoiva'] : false)\n        ),\n    $temp_boton_mod,\n    array('tipo' => 'imagen', 'url' => 'ventas.php?a=baja&id='.$venta['idventa'], 'a' => 'baja', 'title' => $i18n[61], 'permiso' => 'ventas_baja', 'opciones' => 'onclick=\"return confirma('.\"'\".$temp_confirma.\"'\".')\"'),\n    array('tipo' => 'exportar', 'a' => 'exportar', 'title' => $i18n[62],\n        'opciones' => ($temp_alerta_exportar\n            ? 'onclick=\"return alert('.\"'\".$temp_alerta_exportar.\"'\".')\"'\n            : ''\n            ))));\n{\n    // Datos básicos\n    contenido_inicio($i18n[34], '50');\n    {\n        texto('texto', $i18n[106], $i18n[$venta['estado']], 'auto', false, $venta['estado']);\n        texto('texto', $i18n[49], $tipoventa['nombre']);\n        if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] != 'aprobado')\n            texto('texto', $i18n[50], $numeroventa . $i18n[380], 'auto', false, 'alerta');\n        else\n            texto('texto', $i18n[50], $numeroventa);\n        $title_fechas =\n            ($venta['closed_at'] != '0000-00-00 00:00:00'\n                ? $i18n[395] . ':<br>' . mostrar_fecha('fechayhora', $venta['closed_at']) . '<br>' : '')\n            . (($venta['updated_at'] != '0000-00-00 00:00:00' && $venta['updated_at'] != $venta['closed_at'])\n                ? $i18n[396] . ':<br>' . mostrar_fecha('fechayhora', $venta['updated_at']) : '');\n        texto('fechayhora', $i18n[52], $venta['fecha'], 'auto', false, 'info', false, ['title' => $title_fechas]);\n        texto('texto', $i18n[65], $i18n[$venta['condicionventa']]);\n        if ($tipoventa['tipofacturacion'] == 'electronico' && $venta['estadocae'] == 'aprobado') {\n            $enlace_verificar = qr_afip($venta, $tipoventa, true);\n            texto('url', $i18n[140], $venta['cae'], 'auto', $enlace_verificar);\n            texto('fecha', $i18n[144], $venta['vencimientocae']);\n            if ($venta['obscae'])\n                texto('texto', $i18n[71], $venta['obscae']);\n\n        } else if ($tipoventa['tipofacturacion'] == 'electronico') {\n            texto('texto', $i18n[140], ucfirst($venta['estadocae']), 'auto', false, 'alerta');\n            if ($venta['obscae'])\n                texto('texto', $i18n[72], $venta['obscae']);\n        }\n        if ($tipoventa['tienesituacion'])\n            texto('texto', $i18n[155], ($venta['situacion'] ? $i18n[$venta['situacion']] : $i18n['sin_especificar']), 'auto', false, 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_situacion', 'title' => $i18n[382]));\n    }\n    contenido_fin();\n\n    // Cliente\n    contenido_inicio($i18n[35], '50');\n    {\n        texto('texto', $i18n[39], $cliente['nombre'], 'auto', 'clientes.php?a=ver&id='.$cliente['idcliente'], 'cambiar', false, $opciones[] = array('tipo' => 'flotante', 'url' => 'cambiar_clientes', 'title' => $i18n[315]));\n        texto('texto', $i18n[36], $cliente['idcliente']);\n        texto('texto', $i18n[43], $venta['domicilio']);\n        texto('texto', $i18n[145], $venta['localidad']);\n        texto('texto', $i18n[63], $venta['tipoiva']);\n        if ($venta['cuit']) {\n            texto('texto', $i18n[37], $venta['razonsocial']);\n            texto('texto', $i18n[38], $venta['cuit']);\n        } else if ($venta['dni']) {\n            texto('texto', $i18n[37], $venta['razonsocial']);\n            texto('texto', $i18n[967], tipoDocs($venta['tipodoc']));\n            texto('texto', $i18n[242], $venta['dni']);\n        }\n    }\n    contenido_fin();\n\n    salto_linea();\n\n    // Datos adicionales\n    contenido_inicio($i18n[73], '50', true);\n    {\n        texto('texto', $i18n[392], $venta['usuario']);\n        if ($venta['vencimiento1'] != '0000-00-00')\n            texto('fecha', $i18n[85], $venta['vencimiento1']);\n        if ($venta['vencimiento2'] != '0000-00-00')\n            texto('fecha', $i18n[86], $venta['vencimiento2']);\n\n        if ($tipoventa['tipofacturacion'] == 'electronico') {\n            $conceptos = array(\n                '1' => $i18n[216],\n                '2' => $i18n[217],\n                '3' => $i18n[218],\n                );\n            texto('texto', $i18n[219], $conceptos[$venta['concepto']]);\n            if ($venta['concepto'] > 1) {\n                texto('fecha', $i18n[239], $venta['fechainicio']);\n                texto('fecha', $i18n[240], $venta['fechafin']);\n            }\n        }\n\n        if ($venta['idrelacion']) {\n            switch ($venta['tiporelacion']) {\n                case 'servicio':\n                    texto('texto', $i18n[47], $servicio['titulo'].' (N° '.$venta['idrelacion'].')', '100',\n                        'servicios.php?a=ver&id='.$venta['idrelacion']);\n                    break;\n\n                case 'abono':\n                    texto('texto', $i18n[48], $venta['idrelacion'], '100', 'abonos.php?a=ver&idservicio='.$venta['idrelacion']);\n                    break;\n            }\n        }\n        $generada_query = consulta_sql(\"SELECT ventas.idventa, ventas.numero, categorias_ventas.letra, categorias_ventas.puntodeventa, ventas.razonsocial, 'generada' AS relacion\n                                        FROM ventas\n                                        LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n                                        WHERE ventas.idventa IN (SELECT idventa FROM ventasxventas WHERE idrelacion = '$id')\");\n        $relacionada_query = consulta_sql(\"SELECT ventas.idventa, ventas.numero, categorias_ventas.letra, categorias_ventas.puntodeventa, ventas.razonsocial, 'relacionada' AS relacion\n                                        FROM ventas\n                                        LEFT JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n                                        WHERE ventas.idventa IN (SELECT idrelacion FROM ventasxventas WHERE idventa = '$id')\");\n        if (contar_sql($generada_query) || contar_sql($relacionada_query)) {\n            $temp_generada = array();\n            $temp_relacionadas = array();\n            while ($temp_array = array_sql($generada_query)) {\n                $temp_generada[] = array('valor' => numero_comprobante($temp_array['letra'], $temp_array['puntodeventa'], $temp_array['numero']), 'url' => 'ventas.php?a=ver&id='.$temp_array['idventa']);\n            }\n            while ($temp_array = array_sql($relacionada_query)) {\n                $temp_relacionadas[] = array('valor' => numero_comprobante($temp_array['letra'], $temp_array['puntodeventa'], $temp_array['numero']), 'url' => 'ventas.php?a=ver&id='.$temp_array['idventa']);\n            }\n            if (count($temp_generada))\n                enlaces($i18n[151], $temp_generada);\n            if (count($temp_relacionadas))\n                enlaces($i18n[139], $temp_relacionadas);\n        }\n\n        if ($_SESSION['modulo_ML'] && $venta['ML_order_id'] && contar_sql(consulta_sql(\"SELECT ML_estado FROM tienda WHERE ML_estado = '1' LIMIT 1\"))) {\n\n            $resultado_sql = consulta_sql(\"SELECT ML_order_id, ML_pack_id, ML_shipping_id FROM ventas_ml WHERE idventa = '\".$id.\"'\");\n\n            $title_ml = '';\n            $temp_enlaces = array();\n            while ($temp_array = array_sql($resultado_sql)) {\n                $temp_enlaces[] = array('valor' => $temp_array['ML_order_id'], 'url' => 'https://myaccount.mercadolibre.com.ar/sales/vop?orderId='.$temp_array['ML_order_id']);\n                $title_ml .=  $i18n[968].': '.$temp_array['ML_pack_id'];\n                $title_ml .=  '<br>'.$i18n[969].': '.$temp_array['ML_shipping_id'];\n            }\n\n            enlaces($i18n[166], $temp_enlaces, false, false, 'info', ['title' => $title_ml]);\n        }\n    }\n    contenido_fin();\n\n    // Moneda\n    contenido_inicio($i18n[390], '50', true);\n    {\n        texto('titulo', $i18n[390], $venta['moneda'].' ('.$venta['simbolo'].')', 'auto');\n        if ($venta['idmoneda'] != 1 && $venta['estado'] == 'abierto')\n            texto('moneda', $i18n_funciones[331], $venta['cotizacion'], 'auto');\n        else if ($venta['idmoneda'] != 1)\n            texto('moneda', $i18n_funciones[330], $venta['cotizacion_anterior'], 'auto');\n    }\n    contenido_fin();\n\n    salto_linea();\n\n    // Movimientos generados\n    contenido_inicio($venta['estado'] == 'abierto' ? $i18n[244] : $i18n[220], '50', true, false, false, 'id=\"movimientos_generados\"');\n    {\n        $venta['esfiscal'] = $tipoventa['tipofacturacion'] == 'interno' ? false : true;\n        comprobantes_movimientos($venta);\n    }\n    contenido_fin();\n\n    // Listas de precios y depósitos\n    contenido_inicio($i18n[302], '50', true);\n    {\n        texto('texto', $i18n[303], $venta['lista']);\n        texto('texto', $i18n[305], $venta['deposito']);\n    }\n    contenido_fin();\n\n    extras_ver();\n\n    // Productos\n    contenido_inicio($i18n[53], '100', false, false, false, ($_SESSION['mobile'] ? 'style=\"overflow-x: scroll;\"' : ''));\n    {\n        comprobantes_mostrar_titulo_productosxcomprobantes($discrimina);\n\n        $resultado_sql = consulta_sql(\n            \"SELECT productosxventas.*,\n                tablas_unidades.nombre AS unidad,\n                tablas_ivas.nombre AS iva,\n                monedas.simbolo\n            FROM productosxventas\n                LEFT JOIN tablas_unidades ON productosxventas.idunidad = tablas_unidades.idunidad\n                LEFT JOIN tablas_ivas ON productosxventas.idiva = tablas_ivas.idiva\n                LEFT JOIN productos ON productosxventas.idproducto = productos.idproducto\n                LEFT JOIN ventas ON productosxventas.idventa = ventas.idventa\n                LEFT JOIN monedas ON ventas.idmoneda = monedas.idmoneda\n            WHERE productosxventas.idventa = '\".$id.\"'\n            ORDER BY idproductoxventa\");\n\n        if (contar_sql($resultado_sql)) {\n            while ($productoxventa = array_sql($resultado_sql)) {\n                comprobantes_ver_productoxcomprobante($productoxventa, $discrimina);\n            }\n\n        } else {\n            linea_inicio();\n            {\n                texto('texto', false, $i18n[192]);\n            }\n            linea_fin();\n        }\n\n    }\n    contenido_fin();\n\n    if ($tipoventa['discrimina'] == 'R') {\n        contenido_inicio($i18n[46]);\n        {\n            observacion(false, $venta['observacion']);\n        }\n        contenido_fin();\n\n    } else {\n        contenido_inicio($i18n[46], '66');\n        {\n            observacion(false, $venta['observacion']);\n        }\n        contenido_fin();\n\n        contenido_inicio(false, '33', false, false, false, ['id' => \"totales\", 'class' => \"desglose\"]);\n        {\n            if ($tipoventa['discrimina'] == 'C' || $tipoventa['discrimina'] == 'B') {\n                enlaces(false, array(\n                    array('tipo' => 'flotante', 'modulo' => $modulo, 'url' => 'comprobantes_ver_totales', 'id' => $id, 'valor' => $i18n[195]),\n                    ));\n            }\n\n            comprobantes_mostrar_totales($venta, $tipoventa['discrimina']);\n        }\n        contenido_fin();\n    }\n\n}\nventana_fin();\n\nif ($_SESSION['sistema_gratis'] && in_array($_SESSION['ad-feg'], [1, 4, 8])) {\n?>\n    <script charset=\"utf-8\">\n        gratis();\n    </script>\n<?php\n}\n"}]}