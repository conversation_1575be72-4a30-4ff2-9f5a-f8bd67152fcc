{"sourceFile": "services/app/public/flotantes/cambiar_proveedores.php", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1749304787622, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1749304787622, "name": "Commit-0", "content": "<?php\nswitch ($boton) {\n    case $i18n_funciones[22]: // Aceptar\n        $idproveedor = recibir_variable('proveedores_id');\n        $idcompra = recibir_variable('idcompra');\n\n        if (!$idproveedor || !$idcompra)\n            break;\n\n        $proveedor = obtener_datos_proveedor($idproveedor);\n        $compra = obtener_datos_compra($idcompra);\n\n        if ($compra['estado'] == 'cerrado') {\n            consulta_sql(\"UPDATE compras SET\n                idproveedor = '\".$proveedor['idproveedor'].\"'\n                WHERE idcompra = '\".$id.\"'\");\n\n        } else {\n            if(compras_obtener_habilitados($proveedor['idtipoiva'], $compra['discrimina'] == 'A')){\n                consulta_sql(\"UPDATE compras SET\n                    idproveedor = '\".$proveedor['idproveedor'].\"',\n                    idtipoiva = '\".$proveedor['idtipoiva'].\"',\n                    cuit = '\".$proveedor['cuit'].\"',\n                    razonsocial = '\".escape_sql($proveedor['razonsocial']).\"',\n                    domicilio = '\".escape_sql($proveedor['domicilio']).\"',\n                    idlocalidad = '\".(!$proveedor['idlocalidad'] ? campo_sql(consulta_sql(\"SELECT idlocalidad FROM configuraciones LIMIT 1\")) : $proveedor['idlocalidad']).\"'\n                    WHERE idcompra = '\".$id.\"'\n                \");\n            } else {\n                script_flotante('alerta', 'El proveedor tiene configurada como condición de iva '\n                   .$proveedor['tipoiva']\n                   .' y no se le puede emitir un comprobante '\n                   .$compra['discrimina'], 5000);\n                modal_cerrar();\n            }\n        }\n\n        $idmoneda_proveedor_origen = idmoneda('proveedores', $compra['idproveedor']);\n        $idmoneda_proveedor_destino = idmoneda('proveedores', $idproveedor);\n        $total = cotizacion($idmoneda_proveedor_destino, $idmoneda_proveedor_origen,\n            $compra['operacioninversa'] ? -$compra['total'] : $compra['total']);\n\n        consulta_sql(\"UPDATE comprasxproveedores SET\n                idproveedor = '{$proveedor['idproveedor']}',\n                total = '{$total}'\n            WHERE id = '$idcompra'\n                AND idtipocompra > 0\");\n\n        /*consulta_sql(\"UPDATE comprasxproveedores SET\n                idproveedor = '{$proveedor['idproveedor']}'\n            WHERE id IN (SELECT idcomprapago FROM compraspagos WHERE idcompra = '$idcompra')\n                AND idtipocompra <= 0\");*/\n\n        $resultado_sql = consulta_sql(\"SELECT idcompraxproveedor, total FROM comprasxproveedores WHERE id IN (SELECT idcomprapago FROM compraspagos WHERE idcompra = '$idcompra') AND idtipocompra <= 0\");\n        while ($comprasxproveedores = array_sql($resultado_sql)) {\n            consulta_sql(\"UPDATE comprasxproveedores SET\n                            idproveedor = '\".$proveedor['idproveedor'].\"',\n                            total = '\".cotizacion($idmoneda_proveedor_destino, $idmoneda_proveedor_origen, $comprasxproveedores['total']).\"'\n                        WHERE idcompraxproveedor = '\".$comprasxproveedores['idcompraxproveedor'].\"'\");\n        }\n\n        consulta_sql(\"UPDATE compraspagos SET\n                idproveedor = '{$proveedor['idproveedor']}'\n            WHERE idcompra = '$idcompra'\");\n\n        if ($compra['estado'] == 'cerrado' && $compra['muevesaldo']) {\n            $diferencia = obtener_saldo('compras', $idcompra);\n            actualizar_saldo('proveedores', $compra['idproveedor'], - $diferencia);\n            actualizar_saldo('proveedores', $idproveedor, cotizacion($idmoneda_proveedor_destino, $idmoneda_proveedor_origen, $diferencia));\n        }\n\n        $script = 'location.reload();';\n        modal_cerrar($script);\n        break;\n\n    case $i18n_funciones[23]: // Cancelar\n        modal_cerrar();\n        break;\n\n}\n$compra = obtener_datos_compra($id);\n\nventana_inicio($i18n[310]);\n{\n    contenido_inicio($i18n[306]);\n    {\n        entrada('hidden', 'idcompra', '', $id);\n        if ($compra['estado'] == 'cerrado') {\n            texto('italica', false, $i18n[307], 'auto', false, 'info');\n            bajo_linea();bajo_linea();\n        }\n        seleccionador('proveedores');\n    }\n    contenido_fin();\n\n    botones(array(\n        array('valor' => $i18n_funciones[22]),\n        array('valor' => $i18n_funciones[23])\n    ));\n}\nventana_fin();\n"}]}