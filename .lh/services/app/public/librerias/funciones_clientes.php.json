{"sourceFile": "services/app/public/librerias/funciones_clientes.php", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1745939184883, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747850712523, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -146,8 +146,9 @@\n     $idmoneda_cliente_destino = idmoneda('clientes', $cliente['idcliente']);\n \n     consulta_sql(\"UPDATE ventasxclientes SET\n             idcliente = '\".$cliente['idcliente'].\"'\n+            total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $venta['total']).\"'\n         WHERE id = '\".$idventa.\"'\n             AND idtipoventa > 0\");\n \n     $resultado_sql = consulta_sql(\"SELECT idventaxcliente, total FROM ventasxclientes WHERE id IN (SELECT idventapago FROM ventaspagos WHERE idventa = '$idventa') AND idtipoventa <= 0\");\n"}, {"date": 1747850803981, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,178 @@\n+<?php\n+\n+/* Esta funcion es propia de clientes por eso debe estar acá */\n+function verficar_email($mail)\n+{\n+    global $i18n;\n+    global $id;\n+    return verificar_mail_cliente($mail, $i18n[181], $i18n[182], $id);\n+}\n+\n+function comprobantes_habilitados_clientes($idcliente, $pago_a_cuenta = \"\", $devolucion_a_cuenta = \"\")\n+{\n+    $opciones = array();\n+    if (($pago_a_cuenta != \"\") && !$_SESSION['sistema_gratis'])\n+        $opciones[] = array('a' => $pago_a_cuenta, 'permiso' => 'ventaspagos_alta');\n+    if (($devolucion_a_cuenta != \"\") && !$_SESSION['sistema_gratis'])\n+        $opciones[] = array('a' => $devolucion_a_cuenta, 'permiso' => 'ventaspagos_alta');\n+    $resultado_sql = comprobantes_obtener_habilitados($idcliente);\n+    while ($tipoventa = array_sql($resultado_sql)) {\n+        $opciones[] = array('a' => $tipoventa['nombre'], 'permiso' => 'ventas_alta');\n+    }\n+\n+    return $opciones;\n+}\n+\n+function comprobantes_habilitados_validar($idcliente, $idtipoventa = 0)\n+{\n+    $resultado_sql = comprobantes_obtener_habilitados($idcliente,$idtipoventa);\n+    return contar_sql($resultado_sql);\n+}\n+\n+function comprobantes_habilitados_condicion($condicion_cliente, $excluir = false) // $condicion_cliente puede ser 0 para CF, 1 para RI o 2 para Monotributos\n+{\n+    $opciones = array();\n+    $resultado_sql = comprobantes_obtener_habilitados(false, false, $condicion_cliente, $excluir);\n+    while ($tipoventa = array_sql($resultado_sql)) {\n+        $opciones[] = array('a' => $tipoventa['nombre'], 'permiso' => 'ventas_alta');\n+    }\n+\n+    return $opciones;\n+}\n+\n+function comprobantes_obtener_habilitados($idcliente, $idtipoventa = false, $condicion_cliente = false, $excluir = false)\n+{\n+    $condicion_empresa = ($_SESSION['configuracion_idtipoiva'] == 1 ? true : false);\n+\n+    if ($idcliente) {\n+        $condicion_cliente = campo_sql(consulta_sql(\n+            \"SELECT idtipoiva FROM clientes WHERE idcliente = '$idcliente'\"), 0);\n+\n+    } else if ($condicion_cliente != 1)\n+        $condicion_cliente = 0; // Cualquiera que no sea Responsable Inscripto\n+\n+    if ($condicion_empresa && $condicion_cliente == 1)\n+        // Ambos RI\n+        $discrimina =(\"('A','R')\");\n+    elseif ($condicion_empresa && in_array($condicion_cliente, [2, 4, 5]))\n+        // Empresa RI y cliente Monotributo\n+        $discrimina =(\"('A','R')\");\n+    elseif ($condicion_empresa)\n+        // Empresa RI y cliente Consumidor Final o Exento\n+        $discrimina =(\"('B','R')\");\n+    else\n+        // Ninguno RI\n+        $discrimina =(\"('C','R')\");\n+\n+    if ($excluir == 'interno') {\n+        $sql_discrimina = \" tipofacturacion != 'interno' AND discrimina IN $discrimina \";\n+    } else {\n+        $sql_discrimina = \" (tipofacturacion = 'interno' OR discrimina IN $discrimina) \";\n+    }\n+\n+    return consulta_sql(\n+        \"SELECT nombre\n+        FROM categorias_ventas\n+        WHERE estado = '1'\n+            AND $sql_discrimina\"\n+            .($idtipoventa\n+                ? \"AND idtipoventa = '$idtipoventa'\"\n+                : \"\")\n+        .\" ORDER BY nombre\");\n+}\n+\n+function obtener_datos_cliente($idcliente)\n+{\n+    return array_sql(consulta_sql(\n+        \"SELECT clientes.idcliente, clientes.nombre AS cliente, clientes.idtipocliente,\n+            clientes.idlocalidad, clientes.idtipoiva, clientes.razonsocial, clientes.cuit, clientes.dni,\n+            clientes.domicilio, categorias_localidades.idlocalidad, categorias_localidades.nombre AS localidad, tablas_condiciones.nombre AS iva\n+        FROM clientes\n+            LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad\n+            LEFT JOIN tablas_condiciones ON clientes.idtipoiva = tablas_condiciones.idtipoiva\n+        WHERE idcliente = '$idcliente'\"));\n+}\n+\n+function obtener_datos_venta($idventa)\n+{\n+    return array_sql(consulta_sql(\n+        \"SELECT ventas.idventa, ventas.idcliente, ventas.idrelacion, ventas.tiporelacion, ventas.operacioninversa, ventas.total, ventas.muevesaldo,\n+            ventas.estado, ventas.idtipoventa, categorias_ventas.discrimina, ventaspagos.idventapago\n+        FROM ventas\n+            INNER JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n+            LEFT JOIN ventaspagos ON ventas.idventa = ventaspagos.idventa\n+        WHERE ventas.idventa = '$idventa'\"));\n+}\n+\n+function obtener_datos_ventas_desde_servicios($idservicio)\n+{\n+    return array_all_sql(consulta_sql(\n+        \"SELECT ventas.idventa, ventas.idcliente, ventas.idrelacion, ventas.tiporelacion, ventas.operacioninversa, ventas.total,\n+            ventas.estado, ventas.idtipoventa, categorias_ventas.discrimina, ventaspagos.idventapago\n+        FROM ventas\n+            INNER JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n+            LEFT JOIN ventaspagos ON ventas.idventa = ventaspagos.idventa\n+        WHERE ventas.idrelacion = '$idservicio'\n+        AND ventas.tiporelacion = 'servicio'\"));\n+}\n+\n+function cambiar_cliente_en_ventas($idventa, $cliente, $venta) {\n+\n+    if (!$idventa || !$venta['idventa']) {\n+        mostrar_error(\"No se ha especificado la venta a la que se le quiere cambiar el cliente.\");\n+        return false;\n+    }\n+\n+    if ($venta['estado'] == 'cerrado') {\n+        consulta_sql(\"UPDATE ventas SET\n+            idcliente = '\".$cliente['idcliente'].\"'\n+            WHERE idventa = '\".$idventa.\"'\");\n+    } else {\n+        consulta_sql(\"UPDATE ventas SET\n+            idcliente = '\".$cliente['idcliente'].\"',\n+            idtipoiva = '\".$cliente['idtipoiva'].\"',\n+            dni = '\".$cliente['dni'].\"',\n+            cuit = '\".$cliente['cuit'].\"',\n+            tipodoc = '\".$cliente['tipodoc'].\"',\n+            razonsocial = '\".escape_sql($cliente['razonsocial']).\"',\n+            domicilio = '\".escape_sql($cliente['domicilio']).\"',\n+            idlocalidad = '\".(!$cliente['idlocalidad'] ? campo_sql(consulta_sql(\"SELECT idlocalidad FROM configuraciones LIMIT 1\")) : $cliente['idlocalidad']).\"'\n+            WHERE idventa = '\".$idventa.\"'\n+        \");\n+    }\n+\n+    $idmoneda_venta = idmoneda('ventas', $idventa);\n+    $idmoneda_cliente_origen = idmoneda('clientes', $venta['idcliente']);\n+    $idmoneda_cliente_destino = idmoneda('clientes', $cliente['idcliente']);\n+\n+    consulta_sql(\"UPDATE ventasxclientes SET\n+            idcliente = '\".$cliente['idcliente'].\"',\n+            total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $venta['total']).\"'\n+        WHERE id = '\".$idventa.\"'\n+            AND idtipoventa > 0\");\n+\n+    $resultado_sql = consulta_sql(\"SELECT idventaxcliente, total FROM ventasxclientes WHERE id IN (SELECT idventapago FROM ventaspagos WHERE idventa = '$idventa') AND idtipoventa <= 0\");\n+    while ($ventasxclientes = array_sql($resultado_sql)) {\n+        consulta_sql(\"UPDATE ventasxclientes SET\n+                        idcliente = '\".$cliente['idcliente'].\"',\n+                        total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $ventasxclientes['total']).\"'\n+                    WHERE idventaxcliente = '\".$ventasxclientes['idventaxcliente'].\"'\");\n+    }\n+\n+    consulta_sql(\"UPDATE ventaspagos SET\n+            idcliente = '\".$cliente['idcliente'].\"'\n+        WHERE idventa = '\".$idventa.\"'\");\n+\n+    if ($venta['estado'] == 'cerrado' && $venta['muevesaldo']) {\n+        $diferencia = obtener_saldo('ventas', $idventa);\n+        actualizar_saldo('clientes', $venta['idcliente'], cotizacion($idmoneda_cliente_origen, $idmoneda_venta, - $diferencia));\n+        actualizar_saldo('clientes', $cliente['idcliente'], cotizacion($idmoneda_cliente_destino, $idmoneda_venta, $diferencia));\n+    }\n+}\n+\n+function cambiar_cliente_en_servicios($idservicio, $idcliente)\n+{\n+    consulta_sql(\"UPDATE servicios SET\n+            idcliente = '\".$idcliente.\"'\n+            WHERE idservicio = '\".$idservicio.\"'\");\n+}\n\\ No newline at end of file\n"}, {"date": 1749304325852, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -144,8 +144,10 @@\n     $idmoneda_venta = idmoneda('ventas', $idventa);\n     $idmoneda_cliente_origen = idmoneda('clientes', $venta['idcliente']);\n     $idmoneda_cliente_destino = idmoneda('clientes', $cliente['idcliente']);\n \n+    echo cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $venta['total']);\n+\n     consulta_sql(\"UPDATE ventasxclientes SET\n             idcliente = '\".$cliente['idcliente'].\"',\n             total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $venta['total']).\"'\n         WHERE id = '\".$idventa.\"'\n"}, {"date": 1749304349358, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -144,9 +144,9 @@\n     $idmoneda_venta = idmoneda('ventas', $idventa);\n     $idmoneda_cliente_origen = idmoneda('clientes', $venta['idcliente']);\n     $idmoneda_cliente_destino = idmoneda('clientes', $cliente['idcliente']);\n \n-    echo cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $venta['total']);\n+    echo \"antes: \". $venta['total'] . \" después: \". cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $venta['total']);\n \n     consulta_sql(\"UPDATE ventasxclientes SET\n             idcliente = '\".$cliente['idcliente'].\"',\n             total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $venta['total']).\"'\n"}, {"date": 1749304588327, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -143,19 +143,20 @@\n \n     $idmoneda_venta = idmoneda('ventas', $idventa);\n     $idmoneda_cliente_origen = idmoneda('clientes', $venta['idcliente']);\n     $idmoneda_cliente_destino = idmoneda('clientes', $cliente['idcliente']);\n+    $total = cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen,\n+        $venta['operacioninversa'] ? -$venta['total'] : $venta['total']);\n \n-    echo \"antes: \". $venta['total'] . \" después: \". cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $venta['total']);\n-\n     consulta_sql(\"UPDATE ventasxclientes SET\n             idcliente = '\".$cliente['idcliente'].\"',\n-            total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $venta['total']).\"'\n+            total = '\".$total.\"'\n         WHERE id = '\".$idventa.\"'\n             AND idtipoventa > 0\");\n \n     $resultado_sql = consulta_sql(\"SELECT idventaxcliente, total FROM ventasxclientes WHERE id IN (SELECT idventapago FROM ventaspagos WHERE idventa = '$idventa') AND idtipoventa <= 0\");\n     while ($ventasxclientes = array_sql($resultado_sql)) {\n+\n         consulta_sql(\"UPDATE ventasxclientes SET\n                         idcliente = '\".$cliente['idcliente'].\"',\n                         total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $ventasxclientes['total']).\"'\n                     WHERE idventaxcliente = '\".$ventasxclientes['idventaxcliente'].\"'\");\n"}, {"date": 1749304649189, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -154,9 +154,8 @@\n             AND idtipoventa > 0\");\n \n     $resultado_sql = consulta_sql(\"SELECT idventaxcliente, total FROM ventasxclientes WHERE id IN (SELECT idventapago FROM ventaspagos WHERE idventa = '$idventa') AND idtipoventa <= 0\");\n     while ($ventasxclientes = array_sql($resultado_sql)) {\n-\n         consulta_sql(\"UPDATE ventasxclientes SET\n                         idcliente = '\".$cliente['idcliente'].\"',\n                         total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $ventasxclientes['total']).\"'\n                     WHERE idventaxcliente = '\".$ventasxclientes['idventaxcliente'].\"'\");\n@@ -177,183 +176,5 @@\n {\n     consulta_sql(\"UPDATE servicios SET\n             idcliente = '\".$idcliente.\"'\n             WHERE idservicio = '\".$idservicio.\"'\");\n-}\n-<?php\n-\n-/* Esta funcion es propia de clientes por eso debe estar acá */\n-function verficar_email($mail)\n-{\n-    global $i18n;\n-    global $id;\n-    return verificar_mail_cliente($mail, $i18n[181], $i18n[182], $id);\n-}\n-\n-function comprobantes_habilitados_clientes($idcliente, $pago_a_cuenta = \"\", $devolucion_a_cuenta = \"\")\n-{\n-    $opciones = array();\n-    if (($pago_a_cuenta != \"\") && !$_SESSION['sistema_gratis'])\n-        $opciones[] = array('a' => $pago_a_cuenta, 'permiso' => 'ventaspagos_alta');\n-    if (($devolucion_a_cuenta != \"\") && !$_SESSION['sistema_gratis'])\n-        $opciones[] = array('a' => $devolucion_a_cuenta, 'permiso' => 'ventaspagos_alta');\n-    $resultado_sql = comprobantes_obtener_habilitados($idcliente);\n-    while ($tipoventa = array_sql($resultado_sql)) {\n-        $opciones[] = array('a' => $tipoventa['nombre'], 'permiso' => 'ventas_alta');\n-    }\n-\n-    return $opciones;\n-}\n-\n-function comprobantes_habilitados_validar($idcliente, $idtipoventa = 0)\n-{\n-    $resultado_sql = comprobantes_obtener_habilitados($idcliente,$idtipoventa);\n-    return contar_sql($resultado_sql);\n-}\n-\n-function comprobantes_habilitados_condicion($condicion_cliente, $excluir = false) // $condicion_cliente puede ser 0 para CF, 1 para RI o 2 para Monotributos\n-{\n-    $opciones = array();\n-    $resultado_sql = comprobantes_obtener_habilitados(false, false, $condicion_cliente, $excluir);\n-    while ($tipoventa = array_sql($resultado_sql)) {\n-        $opciones[] = array('a' => $tipoventa['nombre'], 'permiso' => 'ventas_alta');\n-    }\n-\n-    return $opciones;\n-}\n-\n-function comprobantes_obtener_habilitados($idcliente, $idtipoventa = false, $condicion_cliente = false, $excluir = false)\n-{\n-    $condicion_empresa = ($_SESSION['configuracion_idtipoiva'] == 1 ? true : false);\n-\n-    if ($idcliente) {\n-        $condicion_cliente = campo_sql(consulta_sql(\n-            \"SELECT idtipoiva FROM clientes WHERE idcliente = '$idcliente'\"), 0);\n-\n-    } else if ($condicion_cliente != 1)\n-        $condicion_cliente = 0; // Cualquiera que no sea Responsable Inscripto\n-\n-    if ($condicion_empresa && $condicion_cliente == 1)\n-        // Ambos RI\n-        $discrimina =(\"('A','R')\");\n-    elseif ($condicion_empresa && in_array($condicion_cliente, [2, 4, 5]))\n-        // Empresa RI y cliente Monotributo\n-        $discrimina =(\"('A','R')\");\n-    elseif ($condicion_empresa)\n-        // Empresa RI y cliente Consumidor Final o Exento\n-        $discrimina =(\"('B','R')\");\n-    else\n-        // Ninguno RI\n-        $discrimina =(\"('C','R')\");\n-\n-    if ($excluir == 'interno') {\n-        $sql_discrimina = \" tipofacturacion != 'interno' AND discrimina IN $discrimina \";\n-    } else {\n-        $sql_discrimina = \" (tipofacturacion = 'interno' OR discrimina IN $discrimina) \";\n-    }\n-\n-    return consulta_sql(\n-        \"SELECT nombre\n-        FROM categorias_ventas\n-        WHERE estado = '1'\n-            AND $sql_discrimina\"\n-            .($idtipoventa\n-                ? \"AND idtipoventa = '$idtipoventa'\"\n-                : \"\")\n-        .\" ORDER BY nombre\");\n-}\n-\n-function obtener_datos_cliente($idcliente)\n-{\n-    return array_sql(consulta_sql(\n-        \"SELECT clientes.idcliente, clientes.nombre AS cliente, clientes.idtipocliente,\n-            clientes.idlocalidad, clientes.idtipoiva, clientes.razonsocial, clientes.cuit, clientes.dni,\n-            clientes.domicilio, categorias_localidades.idlocalidad, categorias_localidades.nombre AS localidad, tablas_condiciones.nombre AS iva\n-        FROM clientes\n-            LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad\n-            LEFT JOIN tablas_condiciones ON clientes.idtipoiva = tablas_condiciones.idtipoiva\n-        WHERE idcliente = '$idcliente'\"));\n-}\n-\n-function obtener_datos_venta($idventa)\n-{\n-    return array_sql(consulta_sql(\n-        \"SELECT ventas.idventa, ventas.idcliente, ventas.idrelacion, ventas.tiporelacion, ventas.operacioninversa, ventas.total, ventas.muevesaldo,\n-            ventas.estado, ventas.idtipoventa, categorias_ventas.discrimina, ventaspagos.idventapago\n-        FROM ventas\n-            INNER JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n-            LEFT JOIN ventaspagos ON ventas.idventa = ventaspagos.idventa\n-        WHERE ventas.idventa = '$idventa'\"));\n-}\n-\n-function obtener_datos_ventas_desde_servicios($idservicio)\n-{\n-    return array_all_sql(consulta_sql(\n-        \"SELECT ventas.idventa, ventas.idcliente, ventas.idrelacion, ventas.tiporelacion, ventas.operacioninversa, ventas.total,\n-            ventas.estado, ventas.idtipoventa, categorias_ventas.discrimina, ventaspagos.idventapago\n-        FROM ventas\n-            INNER JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n-            LEFT JOIN ventaspagos ON ventas.idventa = ventaspagos.idventa\n-        WHERE ventas.idrelacion = '$idservicio'\n-        AND ventas.tiporelacion = 'servicio'\"));\n-}\n-\n-function cambiar_cliente_en_ventas($idventa, $cliente, $venta) {\n-\n-    if (!$idventa || !$venta['idventa']) {\n-        mostrar_error(\"No se ha especificado la venta a la que se le quiere cambiar el cliente.\");\n-        return false;\n-    }\n-\n-    if ($venta['estado'] == 'cerrado') {\n-        consulta_sql(\"UPDATE ventas SET\n-            idcliente = '\".$cliente['idcliente'].\"'\n-            WHERE idventa = '\".$idventa.\"'\");\n-    } else {\n-        consulta_sql(\"UPDATE ventas SET\n-            idcliente = '\".$cliente['idcliente'].\"',\n-            idtipoiva = '\".$cliente['idtipoiva'].\"',\n-            dni = '\".$cliente['dni'].\"',\n-            cuit = '\".$cliente['cuit'].\"',\n-            tipodoc = '\".$cliente['tipodoc'].\"',\n-            razonsocial = '\".escape_sql($cliente['razonsocial']).\"',\n-            domicilio = '\".escape_sql($cliente['domicilio']).\"',\n-            idlocalidad = '\".(!$cliente['idlocalidad'] ? campo_sql(consulta_sql(\"SELECT idlocalidad FROM configuraciones LIMIT 1\")) : $cliente['idlocalidad']).\"'\n-            WHERE idventa = '\".$idventa.\"'\n-        \");\n-    }\n-\n-    $idmoneda_venta = idmoneda('ventas', $idventa);\n-    $idmoneda_cliente_origen = idmoneda('clientes', $venta['idcliente']);\n-    $idmoneda_cliente_destino = idmoneda('clientes', $cliente['idcliente']);\n-\n-    consulta_sql(\"UPDATE ventasxclientes SET\n-            idcliente = '\".$cliente['idcliente'].\"'\n-            total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $venta['total']).\"'\n-        WHERE id = '\".$idventa.\"'\n-            AND idtipoventa > 0\");\n-\n-    $resultado_sql = consulta_sql(\"SELECT idventaxcliente, total FROM ventasxclientes WHERE id IN (SELECT idventapago FROM ventaspagos WHERE idventa = '$idventa') AND idtipoventa <= 0\");\n-    while ($ventasxclientes = array_sql($resultado_sql)) {\n-        consulta_sql(\"UPDATE ventasxclientes SET\n-                        idcliente = '\".$cliente['idcliente'].\"',\n-                        total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $ventasxclientes['total']).\"'\n-                    WHERE idventaxcliente = '\".$ventasxclientes['idventaxcliente'].\"'\");\n-    }\n-\n-    consulta_sql(\"UPDATE ventaspagos SET\n-            idcliente = '\".$cliente['idcliente'].\"'\n-        WHERE idventa = '\".$idventa.\"'\");\n-\n-    if ($venta['estado'] == 'cerrado' && $venta['muevesaldo']) {\n-        $diferencia = obtener_saldo('ventas', $idventa);\n-        actualizar_saldo('clientes', $venta['idcliente'], cotizacion($idmoneda_cliente_origen, $idmoneda_venta, - $diferencia));\n-        actualizar_saldo('clientes', $cliente['idcliente'], cotizacion($idmoneda_cliente_destino, $idmoneda_venta, $diferencia));\n-    }\n-}\n-\n-function cambiar_cliente_en_servicios($idservicio, $idcliente)\n-{\n-    consulta_sql(\"UPDATE servicios SET\n-            idcliente = '\".$idcliente.\"'\n-            WHERE idservicio = '\".$idservicio.\"'\");\n }\n\\ No newline at end of file\n"}], "date": 1745939184883, "name": "Commit-0", "content": "<?php\n\n/* Esta funcion es propia de clientes por eso debe estar acá */\nfunction verficar_email($mail)\n{\n    global $i18n;\n    global $id;\n    return verificar_mail_cliente($mail, $i18n[181], $i18n[182], $id);\n}\n\nfunction comprobantes_habilitados_clientes($idcliente, $pago_a_cuenta = \"\", $devolucion_a_cuenta = \"\")\n{\n    $opciones = array();\n    if (($pago_a_cuenta != \"\") && !$_SESSION['sistema_gratis'])\n        $opciones[] = array('a' => $pago_a_cuenta, 'permiso' => 'ventaspagos_alta');\n    if (($devolucion_a_cuenta != \"\") && !$_SESSION['sistema_gratis'])\n        $opciones[] = array('a' => $devolucion_a_cuenta, 'permiso' => 'ventaspagos_alta');\n    $resultado_sql = comprobantes_obtener_habilitados($idcliente);\n    while ($tipoventa = array_sql($resultado_sql)) {\n        $opciones[] = array('a' => $tipoventa['nombre'], 'permiso' => 'ventas_alta');\n    }\n\n    return $opciones;\n}\n\nfunction comprobantes_habilitados_validar($idcliente, $idtipoventa = 0)\n{\n    $resultado_sql = comprobantes_obtener_habilitados($idcliente,$idtipoventa);\n    return contar_sql($resultado_sql);\n}\n\nfunction comprobantes_habilitados_condicion($condicion_cliente, $excluir = false) // $condicion_cliente puede ser 0 para CF, 1 para RI o 2 para Monotributos\n{\n    $opciones = array();\n    $resultado_sql = comprobantes_obtener_habilitados(false, false, $condicion_cliente, $excluir);\n    while ($tipoventa = array_sql($resultado_sql)) {\n        $opciones[] = array('a' => $tipoventa['nombre'], 'permiso' => 'ventas_alta');\n    }\n\n    return $opciones;\n}\n\nfunction comprobantes_obtener_habilitados($idcliente, $idtipoventa = false, $condicion_cliente = false, $excluir = false)\n{\n    $condicion_empresa = ($_SESSION['configuracion_idtipoiva'] == 1 ? true : false);\n\n    if ($idcliente) {\n        $condicion_cliente = campo_sql(consulta_sql(\n            \"SELECT idtipoiva FROM clientes WHERE idcliente = '$idcliente'\"), 0);\n\n    } else if ($condicion_cliente != 1)\n        $condicion_cliente = 0; // Cualquiera que no sea Responsable Inscripto\n\n    if ($condicion_empresa && $condicion_cliente == 1)\n        // Ambos RI\n        $discrimina =(\"('A','R')\");\n    elseif ($condicion_empresa && in_array($condicion_cliente, [2, 4, 5]))\n        // Empresa RI y cliente Monotributo\n        $discrimina =(\"('A','R')\");\n    elseif ($condicion_empresa)\n        // Empresa RI y cliente Consumidor Final o Exento\n        $discrimina =(\"('B','R')\");\n    else\n        // Ninguno RI\n        $discrimina =(\"('C','R')\");\n\n    if ($excluir == 'interno') {\n        $sql_discrimina = \" tipofacturacion != 'interno' AND discrimina IN $discrimina \";\n    } else {\n        $sql_discrimina = \" (tipofacturacion = 'interno' OR discrimina IN $discrimina) \";\n    }\n\n    return consulta_sql(\n        \"SELECT nombre\n        FROM categorias_ventas\n        WHERE estado = '1'\n            AND $sql_discrimina\"\n            .($idtipoventa\n                ? \"AND idtipoventa = '$idtipoventa'\"\n                : \"\")\n        .\" ORDER BY nombre\");\n}\n\nfunction obtener_datos_cliente($idcliente)\n{\n    return array_sql(consulta_sql(\n        \"SELECT clientes.idcliente, clientes.nombre AS cliente, clientes.idtipocliente,\n            clientes.idlocalidad, clientes.idtipoiva, clientes.razonsocial, clientes.cuit, clientes.dni,\n            clientes.domicilio, categorias_localidades.idlocalidad, categorias_localidades.nombre AS localidad, tablas_condiciones.nombre AS iva\n        FROM clientes\n            LEFT JOIN categorias_localidades ON clientes.idlocalidad = categorias_localidades.idlocalidad\n            LEFT JOIN tablas_condiciones ON clientes.idtipoiva = tablas_condiciones.idtipoiva\n        WHERE idcliente = '$idcliente'\"));\n}\n\nfunction obtener_datos_venta($idventa)\n{\n    return array_sql(consulta_sql(\n        \"SELECT ventas.idventa, ventas.idcliente, ventas.idrelacion, ventas.tiporelacion, ventas.operacioninversa, ventas.total, ventas.muevesaldo,\n            ventas.estado, ventas.idtipoventa, categorias_ventas.discrimina, ventaspagos.idventapago\n        FROM ventas\n            INNER JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n            LEFT JOIN ventaspagos ON ventas.idventa = ventaspagos.idventa\n        WHERE ventas.idventa = '$idventa'\"));\n}\n\nfunction obtener_datos_ventas_desde_servicios($idservicio)\n{\n    return array_all_sql(consulta_sql(\n        \"SELECT ventas.idventa, ventas.idcliente, ventas.idrelacion, ventas.tiporelacion, ventas.operacioninversa, ventas.total,\n            ventas.estado, ventas.idtipoventa, categorias_ventas.discrimina, ventaspagos.idventapago\n        FROM ventas\n            INNER JOIN categorias_ventas ON ventas.idtipoventa = categorias_ventas.idtipoventa\n            LEFT JOIN ventaspagos ON ventas.idventa = ventaspagos.idventa\n        WHERE ventas.idrelacion = '$idservicio'\n        AND ventas.tiporelacion = 'servicio'\"));\n}\n\nfunction cambiar_cliente_en_ventas($idventa, $cliente, $venta) {\n\n    if (!$idventa || !$venta['idventa']) {\n        mostrar_error(\"No se ha especificado la venta a la que se le quiere cambiar el cliente.\");\n        return false;\n    }\n\n    if ($venta['estado'] == 'cerrado') {\n        consulta_sql(\"UPDATE ventas SET\n            idcliente = '\".$cliente['idcliente'].\"'\n            WHERE idventa = '\".$idventa.\"'\");\n    } else {\n        consulta_sql(\"UPDATE ventas SET\n            idcliente = '\".$cliente['idcliente'].\"',\n            idtipoiva = '\".$cliente['idtipoiva'].\"',\n            dni = '\".$cliente['dni'].\"',\n            cuit = '\".$cliente['cuit'].\"',\n            tipodoc = '\".$cliente['tipodoc'].\"',\n            razonsocial = '\".escape_sql($cliente['razonsocial']).\"',\n            domicilio = '\".escape_sql($cliente['domicilio']).\"',\n            idlocalidad = '\".(!$cliente['idlocalidad'] ? campo_sql(consulta_sql(\"SELECT idlocalidad FROM configuraciones LIMIT 1\")) : $cliente['idlocalidad']).\"'\n            WHERE idventa = '\".$idventa.\"'\n        \");\n    }\n\n    $idmoneda_venta = idmoneda('ventas', $idventa);\n    $idmoneda_cliente_origen = idmoneda('clientes', $venta['idcliente']);\n    $idmoneda_cliente_destino = idmoneda('clientes', $cliente['idcliente']);\n\n    consulta_sql(\"UPDATE ventasxclientes SET\n            idcliente = '\".$cliente['idcliente'].\"'\n        WHERE id = '\".$idventa.\"'\n            AND idtipoventa > 0\");\n\n    $resultado_sql = consulta_sql(\"SELECT idventaxcliente, total FROM ventasxclientes WHERE id IN (SELECT idventapago FROM ventaspagos WHERE idventa = '$idventa') AND idtipoventa <= 0\");\n    while ($ventasxclientes = array_sql($resultado_sql)) {\n        consulta_sql(\"UPDATE ventasxclientes SET\n                        idcliente = '\".$cliente['idcliente'].\"',\n                        total = '\".cotizacion($idmoneda_cliente_destino, $idmoneda_cliente_origen, $ventasxclientes['total']).\"'\n                    WHERE idventaxcliente = '\".$ventasxclientes['idventaxcliente'].\"'\");\n    }\n\n    consulta_sql(\"UPDATE ventaspagos SET\n            idcliente = '\".$cliente['idcliente'].\"'\n        WHERE idventa = '\".$idventa.\"'\");\n\n    if ($venta['estado'] == 'cerrado' && $venta['muevesaldo']) {\n        $diferencia = obtener_saldo('ventas', $idventa);\n        actualizar_saldo('clientes', $venta['idcliente'], cotizacion($idmoneda_cliente_origen, $idmoneda_venta, - $diferencia));\n        actualizar_saldo('clientes', $cliente['idcliente'], cotizacion($idmoneda_cliente_destino, $idmoneda_venta, $diferencia));\n    }\n}\n\nfunction cambiar_cliente_en_servicios($idservicio, $idcliente)\n{\n    consulta_sql(\"UPDATE servicios SET\n            idcliente = '\".$idcliente.\"'\n            WHERE idservicio = '\".$idservicio.\"'\");\n}"}]}