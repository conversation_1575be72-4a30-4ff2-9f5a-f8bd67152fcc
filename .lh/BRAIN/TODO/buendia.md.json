{"sourceFile": "BRAIN/TODO/buendia.md", "activeCommit": 0, "commits": [{"activePatchIndex": 14, "patches": [{"date": 1747309807506, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747397655483, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,8 @@\n # BUEN DÍA ☕ 😃\n \n *¿Cómo querés distribuir tu día?*\n-  - ELONGAR 🧘‍♂️\n+  - [ELONGAR 🧘‍♂️](../ROADS/NIRVANA/ELONGAR.md)\n   - [ENTRENAR 🏊‍♂️ 🏃 🏋️‍♂️](../ROADS/NIRVANA/ENTRENAR.md)\n \n *¿Cómo va a ser una semana exitosa?*\n   - TODO priorizando los @flow\n"}, {"date": 1747397727844, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,10 @@\n # BUEN DÍA ☕ 😃\n \n *¿Cómo querés distribuir tu día?*\n-  - [ELONGAR 🧘‍♂️](../ROADS/NIRVANA/ELONGAR.md)\n-  - [ENTRENAR 🏊‍♂️ 🏃 🏋️‍♂️](../ROADS/NIRVANA/ENTRENAR.md)\n+  - ELONGAR 🧘‍♂️\n+  - ENTRENAR 🏊‍♂️ 🏃 🏋️‍♂️\n+  - [ENTRENAR](../ROADS/NIRVANA/ENTRENAR.md)\n \n *¿Cómo va a ser una semana exitosa?*\n   - TODO priorizando los @flow\n   - Deploy SaaS\n"}, {"date": 1747397802621, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,9 +2,11 @@\n \n *¿Cómo querés distribuir tu día?*\n   - ELONGAR 🧘‍♂️\n   - ENTRENAR 🏊‍♂️ 🏃 🏋️‍♂️\n-  - [ENTRENAR](../ROADS/NIRVANA/ENTRENAR.md)\n+  - Trabajo\n+  - Descanso\n+  - Brain\n \n *¿Cómo va a ser una semana exitosa?*\n   - TODO priorizando los @flow\n   - Deploy SaaS\n"}, {"date": 1747397925461, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,19 +7,19 @@\n   - Descanso\n   - Brain\n \n *¿Cómo va a ser una semana exitosa?*\n-  - TODO priorizando los @flow\n-  - Deploy SaaS\n-  - Errores y mejoras en admin (para dejar listo y seguir con EVENTOS)\n+  - [x] TODO priorizando los @flow\n+  - [x] Deploy SaaS\n+  - [ ] Errores y mejoras en admin (para dejar listo y seguir con EVENTOS)\n \n *¿Qué podes compartir con Maty hoy?*\n-  - Ayudarlo con sus tiempos\n-  - Tapas de vinilos\n+  - [x] Ayudarlo con sus tiempos\n+  - [ ] Tapas de vinilos\n \n *¿Cuál va a ser tu PMF (Pay Me First: ejercicio, familia y estudio)?*\n-  - Volver a correr y calistenia\n-  - <PERSON><PERSON> con el nuevo TODO ordenando los @flow\n+  - [x] Volver a correr y calistenia\n+  - [x] Jugar con el nuevo TODO ordenando los @flow\n \n *Plan anti burn out*\n   - No más de 1 flow, 1 focus y 3 quicks por día\n   - CERO WhatsApp, reuniones, offline antes del almuerzo\n"}, {"date": 1747658850181, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,19 +7,17 @@\n   - Descanso\n   - Brain\n \n *¿Cómo va a ser una semana exitosa?*\n-  - [x] TODO priorizando los @flow\n-  - [x] Deploy SaaS\n-  - [ ] Errores y mejoras en admin (para dejar listo y seguir con EVENTOS)\n+- [ ] Cronometraje Añelo Perfecto\n+- [ ] Dar un buen paso en SaaS Lamda API\n+- [ ] Terminar Eventos\n \n *¿Qué podes compartir con Maty hoy?*\n-  - [x] Ayudarlo con sus tiempos\n-  - [ ] Tapas de vinilos\n+- [ ] Tapas de vinilos\n \n *¿Cuál va a ser tu PMF (Pay Me First: ejercicio, familia y estudio)?*\n-  - [x] Volver a correr y calistenia\n-  - [x] Jugar con el nuevo TODO ordenando los @flow\n+- [ ] Seguir entrenando y empezar a elongar\n \n *Plan anti burn out*\n   - No más de 1 flow, 1 focus y 3 quicks por día\n   - CERO WhatsApp, reuniones, offline antes del almuerzo\n"}, {"date": 1748274125563, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,11 +7,12 @@\n   - Descanso\n   - Brain\n \n *¿Cómo va a ser una semana exitosa?*\n-- [ ] Cronometraje Añelo Perfecto\n+- [x] Cronometraje Añelo Perfecto\n - [ ] Dar un buen paso en SaaS Lamda API\n-- [ ] Terminar Eventos\n+- [ ] Grabar vídeos de @crono\n+- [ ] Terminar Eventos y dejar ordenado los minors de @crono\n \n *¿Qué podes compartir con Maty hoy?*\n - [ ] Tapas de vinilos\n \n"}, {"date": 1748274150781, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,18 +7,17 @@\n   - Descanso\n   - Brain\n \n *¿Cómo va a ser una semana exitosa?*\n-- [x] Cronometraje Añelo Perfecto\n - [ ] Dar un buen paso en SaaS Lamda API\n - [ ] Grabar vídeos de @crono\n - [ ] Terminar Eventos y dejar ordenado los minors de @crono\n \n *¿Qué podes compartir con Maty hoy?*\n-- [ ] Tapas de vinilos\n+- [ ] Tapas de vinilos e impresiones\n \n *¿Cuál va a ser tu PMF (Pay Me First: ejercicio, familia y estudio)?*\n-- [ ] Seguir entrenando y empezar a elongar\n+- [ ] Priorizar mi espalda\n \n *Plan anti burn out*\n   - No más de 1 flow, 1 focus y 3 quicks por día\n   - CERO WhatsApp, reuniones, offline antes del almuerzo\n"}, {"date": 1748274178280, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,11 +15,11 @@\n *¿Qué podes compartir con Maty hoy?*\n - [ ] Tapas de vinilos e impresiones\n \n *¿Cuál va a ser tu PMF (Pay Me First: ejercicio, familia y estudio)?*\n-- [ ] Priorizar mi espalda\n+- [ ] Priorizar mi espalda. Cada día a las 11 me reservo un tiempo para mí espalda\n \n *Plan anti burn out*\n   - No más de 1 flow, 1 focus y 3 quicks por día\n   - CERO WhatsApp, reuniones, offline antes del almuerzo\n   - Entrenar\n\\ No newline at end of file\n-  - Just Keep Breathing\n+  - Just Keep Breathing\n"}, {"date": 1748275598153, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,16 +10,17 @@\n *¿Cómo va a ser una semana exitosa?*\n - [ ] Dar un buen paso en SaaS Lamda API\n - [ ] Grabar vídeos de @crono\n - [ ] Terminar Eventos y dejar ordenado los minors de @crono\n+- [ ] Empezar fuerte con Agente\n \n *¿Qué podes compartir con Maty hoy?*\n - [ ] Tapas de vinilos e impresiones\n \n *¿Cuál va a ser tu PMF (Pay Me First: ejercicio, familia y estudio)?*\n - [ ] Priorizar mi espalda. Cada día a las 11 me reservo un tiempo para mí espalda\n \n *Plan anti burn out*\n-  - No más de 1 flow, 1 focus y 3 quicks por día\n-  - CERO WhatsApp, reuniones, offline antes del almuerzo\n-  - Entrenar\n\\ No newline at end of file\n-  - Just Keep Breathing\n+- No más de 1 flow, 1 focus y 3 quicks por día\n+- CERO WhatsApp, reuniones, offline antes del almuerzo\n+- Entrenar\n+- Just Keep Breathing\n"}, {"date": 1748275633543, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,8 +19,8 @@\n *¿Cuál va a ser tu PMF (Pay Me First: ejercicio, familia y estudio)?*\n - [ ] Priorizar mi espalda. Cada día a las 11 me reservo un tiempo para mí espalda\n \n *Plan anti burn out*\n-- No más de 1 flow, 1 focus y 3 quicks por día\n+- No tengo más tareas diarios, ni weekly, sólo flujos para hacer cuando tengo ganas\n - CERO WhatsApp, reuniones, offline antes del almuerzo\n - Entrenar\n - Just Keep Breathing\n"}, {"date": 1748287786938, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,8 @@\n # BUEN DÍA ☕ 😃\n \n *¿Cómo querés distribuir tu día?*\n-  - ELONGAR 🧘‍♂️\n+  - ELONGAR 🤸‍♂️🧘‍♂️\n   - ENTRENAR 🏊‍♂️ 🏃 🏋️‍♂️\n   - Trabajo\n   - Descanso\n   - Brain\n"}, {"date": 1748608111638, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,13 +6,10 @@\n   - Trabajo\n   - Descanso\n   - Brain\n \n-*¿Cómo va a ser una semana exitosa?*\n+*¿Cómo va a ser una semana exitosa?* (Sólo 1 flow)\n - [ ] Dar un buen paso en SaaS Lamda API\n-- [ ] Grabar vídeos de @crono\n-- [ ] Terminar Eventos y dejar ordenado los minors de @crono\n-- [ ] Em<PERSON>zar fuerte con Agente\n \n *¿Qué podes compartir con Maty hoy?*\n - [ ] Tapas de vinilos e impresiones\n \n"}, {"date": 1749070198687, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,9 +13,9 @@\n *¿Qué podes compartir con Maty hoy?*\n - [ ] Tapas de vinilos e impresiones\n \n *¿Cuál va a ser tu PMF (Pay Me First: ejercicio, familia y estudio)?*\n-- [ ] Priorizar mi espalda. Cada día a las 11 me reservo un tiempo para mí espalda\n+- [ ] Priorizar mi espalda. Cada día me reservo un tiempo para mí espalda\n \n *Plan anti burn out*\n - No tengo más tareas diarios, ni weekly, sólo flujos para hacer cuando tengo ganas\n - CERO WhatsApp, reuniones, offline antes del almuerzo\n"}, {"date": 1750076010938, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,12 +7,12 @@\n   - Descanso\n   - Brain\n \n *¿Cómo va a ser una semana exitosa?* (Sólo 1 flow)\n-- [ ] Dar un buen paso en SaaS Lamda API\n+- [ ] Dar un buen paso en SaaS Lamda ARCA\n \n *¿Qué podes compartir con Maty hoy?*\n-- [ ] Tapas de vinilos e impresiones\n+- [ ] Tapas de vinilos y manejar\n \n *¿Cuál va a ser tu PMF (Pay Me First: ejercicio, familia y estudio)?*\n - [ ] Priorizar mi espalda. Cada día me reservo un tiempo para mí espalda\n \n"}], "date": 1747309807506, "name": "Commit-0", "content": "# BUEN DÍA ☕ 😃\n\n*¿Cómo querés distribuir tu día?*\n  - ELONGAR 🧘‍♂️\n  - [ENTRENAR 🏊‍♂️ 🏃 🏋️‍♂️](../ROADS/NIRVANA/ENTRENAR.md)\n\n*¿Cómo va a ser una semana exitosa?*\n  - TODO priorizando los @flow\n  - Deploy SaaS\n  - Errores y mejoras en admin (para dejar listo y seguir con EVENTOS)\n\n*¿Qué podes compartir con Maty hoy?*\n  - Ayudarlo con sus tiempos\n  - Tapas de vinilos\n\n*¿Cuál va a ser tu PMF (Pay Me First: ejercicio, familia y estudio)?*\n  - Volver a correr y calistenia\n  - Jugar con el nuevo TODO ordenando los @flow\n\n*Plan anti burn out*\n  - No más de 1 flow, 1 focus y 3 quicks por día\n  - CERO WhatsApp, reuniones, offline antes del almuerzo\n  - Entrenar\n  - Just Keep Breathing"}]}