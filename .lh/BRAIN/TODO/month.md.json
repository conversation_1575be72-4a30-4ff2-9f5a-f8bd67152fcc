{"sourceFile": "BRAIN/TODO/month.md", "activeCommit": 0, "commits": [{"activePatchIndex": 17, "patches": [{"date": 1747308147705, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747309058387, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,14 @@\n+# MONTH\n \n+## TTT\n+\n+\n+## TODO\n+\n @saas\n - [ ] Mandar archivos Uriel\n \n @crono\n - [ ] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n+\n+## CUENTAS\n"}, {"date": 1747309143191, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,5 +10,5 @@\n \n @crono\n - [ ] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n \n-## CUENTAS\n+## CUENTAS 💵\n"}, {"date": 1747309409786, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,29 @@\n+# MONTH\n+\n+## TTT\n+\n+\n+## TODO\n+\n+@saas\n+- [ ] Mandar archivos Uriel\n+\n+@crono\n+- [ ] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n+\n+## CUENTAS 💵\n+\n+- [ ] <PERSON><PERSON> ( https://oficinavirtual.camuzzigas.com.ar/home/<USER>\n+- [ ] Pagar Personal Pri\n+- [ ] Pagar Claro\n+- [ ] Pagar Calf\n+- [ ] Pagar Tarjeta Visa Santander (Hay que restar los impuestos de los dólares)\n+- [ ] Pagar alquiler\n+- [ ] Pagar Tarjeta AMEX\n+- [ ] Pagar a Gaby, Pri y Juli\n+- [ ] Pagar número Mo<PERSON>ar (2995 51-3063 / 2994 58-8493)\n+- [ ] Pagar autónomo<PERSON>, monotributo (mío y Juli) y IIGG Nqn\n+- [ ] Pagar Epas Trimestral ( http://www.epas.gov.ar/mi-cuenta/ )\n+- [ ] Pagar Cablevisión (está por débito)\n+- [ ] Pagar estudio Pri\n+\n"}, {"date": 1747310001254, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,17 +1,23 @@\n # MONTH\n \n ## TTT\n \n+- [ ] Revisar quieros y milestones\n+- [ ] <PERSON>gar sueldos y acomodar cuentas\n+- [ ] Cerrar cajas Crono y SV\n+- [ ] Revisar métricas SaaS, Crono, AIC, Personales\n \n+\n ## TODO\n \n @saas\n - [ ] Mandar archivos Uriel\n \n @crono\n - [ ] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n \n+\n ## CUENTAS 💵\n \n - [ ] <PERSON><PERSON> ( https://oficinavirtual.camuzzigas.com.ar/home/<USER>\n - [ ] Pagar Personal Pri\n"}, {"date": 1747311383780, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -32,18 +32,8 @@\n - [ ] Pagar Epas Trimestral ( http://www.epas.gov.ar/mi-cuenta/ )\n - [ ] Pagar Cablevisión (está por débito)\n - [ ] Pagar estudio Pri\n \n-# MONTH\n \n-## TTT\n+## MANTENIMIENTOS 🚗\n \n-\n-## TODO\n-\n-@saas\n-- [ ] Mandar archivos Uriel\n-\n-@crono\n-- [ ] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n-\n-## CUENTAS 💵\n+- PROXIMO Cambio de Aceite de caja y diferencial, aceite y filtros | 100.000 km\n"}, {"date": 1748275452931, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,8 +26,10 @@\n - [ ] Pagar Tarjeta Visa Santander (Hay que restar los impuestos de los dólares)\n - [ ] Pagar alquiler\n - [ ] Pagar Tarjeta AMEX\n - [ ] Pagar a Gaby, Pri y Juli\n+- [ ] Pagar mensualidad a Mati\n+- [ ] Pagar 2% dividendos SaaS\n - [ ] Pagar número Movistar (2995 51-3063 / 2994 58-8493)\n - [ ] Pagar autónomos, monotributo (mío y Juli) y IIGG Nqn\n - [ ] Pagar Epas Trimestral ( http://www.epas.gov.ar/mi-cuenta/ )\n - [ ] Pagar Cablevisión (está por débito)\n"}, {"date": 1748275478497, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,5 +37,7 @@\n \n \n ## MANTENIMIENTOS 🚗\n \n-- PROXIMO Cambio de Aceite de caja y diferencial, aceite y filtros | 100.000 km\n+- 87500: Cambio de aceite y filtros. (Reservá tu turno en BOXES por la App YPF. Presentale al lubriexperto este mail para aplicar el descuento con el código 9507.)\n+- 100000: Cambio de Aceite de caja y diferencial, aceite y filtros, alineación y balanceo. Cambio de correa de distribución y bomba de aguas.\n+\n"}, {"date": 1748275500056, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -38,6 +38,4 @@\n \n ## MANTENIMIENTOS 🚗\n \n - 87500: Cambio de aceite y filtros. (Reservá tu turno en BOXES por la App YPF. Presentale al lubriexperto este mail para aplicar el descuento con el código 9507.)\n-- 100000: Cambio de Aceite de caja y diferencial, aceite y filtros, alineación y balanceo. Cambio de correa de distribución y bomba de aguas.\n-\n"}, {"date": 1748885638460, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,41 @@\n+# MONTH\n+\n+## TTT\n+\n+- [ ] Revisar quieros y milestones\n+- [ ] Pagar sueldos y acomodar cuentas\n+- [ ] Cerrar cajas Crono y SV\n+- [ ] Revisar métricas SaaS, Crono, AIC, Personales\n+\n+\n+## TODO\n+\n+@saas\n+- [ ] Mandar archivos Uriel\n+\n+@crono\n+- [ ] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n+\n+\n+## CUENTAS 💵\n+\n+- [ ] <PERSON>gar <PERSON> ( https://oficinavirtual.camuzzigas.com.ar/home/<USER>\n+- [ ] Pagar Personal Pri\n+- [ ] Pagar Claro\n+- [ ] Pagar Calf\n+- [ ] Pagar Tarjeta Visa Santander (Hay que restar los impuestos de los dólares)\n+- [x] <PERSON><PERSON> alquiler\n+- [ ] Pagar Tarjeta AMEX\n+- [ ] Pagar a Gaby, Pri y Juli\n+- [ ] Pagar mensualidad a Mati\n+- [ ] Pagar 2% dividendos SaaS\n+- [ ] Pagar número Movistar (2995 51-3063 / 2994 58-8493)\n+- [ ] Pagar autónomos, monotributo (mío y Juli) y IIGG Nqn\n+- [ ] Pagar Epas Trimestral ( http://www.epas.gov.ar/mi-cuenta/ )\n+- [ ] Pagar Cablevisión (está por débito)\n+- [ ] Pagar estudio Pri\n+\n+\n+## MANTENIMIENTOS 🚗\n+\n+- 87500: Cambio de aceite y filtros. (Reservá tu turno en BOXES por la App YPF. Presentale al lubriexperto este mail para aplicar el descuento con el código 9507.)\n"}, {"date": 1748885681581, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -38,45 +38,4 @@\n \n ## MANTENIMIENTOS 🚗\n \n - 87500: Cambio de aceite y filtros. (Reservá tu turno en BOXES por la App YPF. Presentale al lubriexperto este mail para aplicar el descuento con el código 9507.)\n-# MONTH\n-\n-## TTT\n-\n-- [ ] Revisar quieros y milestones\n-- [ ] Pagar sueldos y acomodar cuentas\n-- [ ] Cerrar cajas Crono y SV\n-- [ ] Revisar métricas SaaS, Crono, AIC, Personales\n-\n-\n-## TODO\n-\n-@saas\n-- [ ] Mandar archivos Uriel\n-\n-@crono\n-- [ ] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n-\n-\n-## CUENTAS 💵\n-\n-- [ ] <PERSON><PERSON> ( https://oficinavirtual.camuzzigas.com.ar/home/<USER>\n-- [ ] Pagar Personal Pri\n-- [ ] Pagar <PERSON>\n-- [ ] Pagar <PERSON>f\n-- [ ] Pagar Tarjeta Visa Santander (Hay que restar los impuestos de los dólares)\n-- [ ] Pagar alquiler\n-- [ ] Pagar Tarjeta AMEX\n-- [ ] Pagar a Gaby, Pri y Juli\n-- [ ] Pagar mensualidad a Mati\n-- [ ] Pagar 2% dividendos SaaS\n-- [ ] Pagar número Movistar (2995 51-3063 / 2994 58-8493)\n-- [ ] Pagar autónomos, monotributo (mío y Juli) y IIGG Nqn\n-- [ ] Pagar Epas Trimestral ( http://www.epas.gov.ar/mi-cuenta/ )\n-- [ ] Pagar Cablevisión (está por débito)\n-- [ ] Pagar estudio Pri\n-\n-\n-## MANTENIMIENTOS 🚗\n-\n-- 87500: Cambio de aceite y filtros. (Reservá tu turno en BOXES por la App YPF. Presentale al lubriexperto este mail para aplicar el descuento con el código 9507.)\n"}, {"date": 1748885715903, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,9 +3,9 @@\n ## TTT\n \n - [ ] Revisar quieros y milestones\n - [ ] <PERSON>gar sueldo<PERSON> y acomodar cuentas\n-- [ ] Cerrar cajas Crono y SV\n+- [x] Cerrar cajas Crono y SV\n - [ ] Revisar métricas SaaS, Crono, AIC, Personales\n \n \n ## TODO\n"}, {"date": 1748958183424, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,8 +11,16 @@\n ## TODO\n \n @saas\n - [ ] Mandar archivos Uriel\n+    Tener en cuenta:\n+    - Mandar con <NAME_EMAIL>\n+    - Exportar desde Chrome\n+    - Sacar el memory_limit\n+    - Generar CSVs y guardarlos a XLSX\n+    Informes para mandar:\n+    - Informe de Comprobantes emitidos (con Usuario, Provincia, CUIT y Datos ML y Datos Extras Ventas)\n+    - Subdiario (sólo los impuestos de Misiones, Provincias, No Grabado, 10,5 y 21 de IVA)\n \n @crono\n - [ ] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n \n"}, {"date": 1748965025914, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,18 +1,18 @@\n # MONTH\n \n ## TTT\n \n-- [ ] Revisar quieros y milestones\n-- [ ] Pagar sueldos y acomodar cuentas\n+- [x] Revisar quieros y milestones\n+- [x] Pagar sueldos y acomodar cuentas\n - [x] Cerrar cajas Crono y SV\n - [ ] Revisar métricas SaaS, Crono, AIC, Personales\n \n \n ## TODO\n \n @saas\n-- [ ] Mandar archivos Uriel\n+- [x] Mandar archivos Uriel\n     Tener en cuenta:\n     - Mandar con <NAME_EMAIL>\n     - Exportar desde Chrome\n     - Sacar el memory_limit\n@@ -21,9 +21,9 @@\n     - Informe de Comprobantes emitidos (con Usuario, Provincia, CUIT y Datos ML y Datos Extras Ventas)\n     - Subdiario (sólo los impuestos de Misiones, Provincias, No Grabado, 10,5 y 21 de IVA)\n \n @crono\n-- [ ] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n+- [x] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n \n \n ## CUENTAS 💵\n \n@@ -33,9 +33,9 @@\n - [ ] Pagar Calf\n - [ ] Pagar Tarjeta Visa Santander (Hay que restar los impuestos de los dólares)\n - [x] Pagar alquiler\n - [ ] Pagar Tarjeta AMEX\n-- [ ] Pagar a Gaby, Pri y Juli\n+- [x] Pagar a Gaby, Pri y Juli\n - [ ] Pagar mensualidad a Mati\n - [ ] Pagar 2% dividendos SaaS\n - [ ] Pagar número Movistar (2995 51-3063 / 2994 58-8493)\n - [ ] Pagar autónomos, monotributo (mío y Juli) y IIGG Nqn\n"}, {"date": 1749855344728, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -30,12 +30,12 @@\n - [ ] <PERSON><PERSON> ( https://oficinavirtual.camuzzigas.com.ar/home/<USER>\n - [ ] Pagar Personal Pri\n - [ ] Pagar Claro\n - [ ] Pagar Calf\n-- [ ] Pagar Tarjeta Visa Santander (Hay que restar los impuestos de los dólares)\n - [x] Pagar alquiler\n-- [ ] Pagar Tarjeta AMEX\n - [x] Pagar a Gaby, Pri y Juli\n+- [x] Pagar Tarjeta Visa Santander (Hay que restar los impuestos de los dólares)\n+- [x] Pagar Tarjeta AMEX\n - [ ] Pagar mensualidad a Mati\n - [ ] Pagar 2% dividendos SaaS\n - [ ] Pagar número Movistar (2995 51-3063 / 2994 58-8493)\n - [ ] Pagar autónomo<PERSON>, monotributo (mío y Juli) y IIGG Nqn\n"}, {"date": 1749855354265, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -34,9 +34,9 @@\n - [x] <PERSON><PERSON> alquiler\n - [x] Pagar a Gaby, <PERSON>ri y Juli\n - [x] Pagar Tarjeta Visa Santander (Hay que restar los impuestos de los dólares)\n - [x] Pagar Tarjeta AMEX\n-- [ ] Pagar mensualidad a Mati\n+- [x] Pagar mensualidad a Mati\n - [ ] Pagar 2% dividendos SaaS\n - [ ] Pagar número Movistar (2995 51-3063 / 2994 58-8493)\n - [ ] Pagar autónomos, monotributo (mío y Juli) y IIGG Nqn\n - [ ] Pagar Epas Trimestral ( http://www.epas.gov.ar/mi-cuenta/ )\n"}, {"date": 1750097262513, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,23 +26,22 @@\n \n \n ## CUENTAS 💵\n \n-- [ ] <PERSON><PERSON> ( https://oficinavirtual.camuzzigas.com.ar/home/<USER>\n-- [ ] Pagar Personal Pri\n-- [ ] Pagar <PERSON>\n-- [ ] Pagar <PERSON>f\n+- [x] <PERSON><PERSON> ( https://oficinavirtual.camuzzigas.com.ar/home/<USER>\n+- [x] Pagar Personal Pri\n+- [x] Pagar Claro\n+- [x] Pagar <PERSON>f\n - [x] <PERSON>gar alquiler\n - [x] Pagar a Gaby, Pri y Juli\n - [x] Pagar Tarjeta Visa Santander (Hay que restar los impuestos de los dólares)\n - [x] Pagar Tarjeta AMEX\n - [x] Pagar mensualidad a Mati\n-- [ ] Pagar 2% dividendos SaaS\n-- [ ] <PERSON>gar nú<PERSON>o <PERSON> (2995 51-3063 / 2994 58-8493)\n-- [ ] Pagar autónomos, monotributo (mío y Juli) y IIGG Nqn\n-- [ ] Pagar Epas Trimestral ( http://www.epas.gov.ar/mi-cuenta/ )\n-- [ ] Pagar Cablevisión (está por débito)\n-- [ ] Pagar estudio Pri\n+- [x] Pagar 2% dividendos SaaS\n+- [x] Pagar número Movistar (2995 51-3063 / 2994 58-8493)\n+- [x] Pagar autónomos, monotributo (mío por débito y Juli por VEP) y IIGG Nqn (por débito)\n+- [x] Pagar Epas Trimestral ( http://www.epas.gov.ar/mi-cuenta/ )\n+- [x] Pagar Cablevisión (está por débito)\n \n \n ## MANTENIMIENTOS 🚗\n \n"}, {"date": 1750261114736, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,49 @@\n+# MONTH\n+\n+## TTT\n+\n+- [x] Revisar quieros y milestones\n+- [x] Pagar sueldos y acomodar cuentas\n+- [x] Cerrar cajas Crono y SV\n+- [ ] Revisar métricas SaaS, Crono, AIC, Personales\n+\n+\n+## TODO\n+\n+@saas\n+- [x] Mandar archivos Uriel\n+    Tener en cuenta:\n+    - Mandar con <NAME_EMAIL>\n+    - Exportar desde Chrome\n+    - Sacar el memory_limit\n+    - Generar CSVs y guardarlos a XLSX\n+    Informes para mandar:\n+    - Informe de Comprobantes emitidos (con Usuario, Provincia, CUIT y Datos ML y Datos Extras Ventas)\n+    - Subdiario (sólo los impuestos de Misiones, Provincias, No Grabado, 10,5 y 21 de IVA)\n+\n+@crono\n+- [x] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n+\n+\n+## CUENTAS 💵\n+\n+- [x] Pagar Camuzzi ( https://oficinavirtual.camuzzigas.com.ar/home/<USER>\n+- [x] Pagar Personal Pri\n+- [x] Pagar Claro\n+- [x] Pagar Calf\n+- [x] Pagar alquiler\n+- [x] Pagar a Gaby, Pri y Juli\n+- [x] Pagar Tarjeta Visa Santander (Hay que restar los impuestos de los dólares)\n+- [x] Pagar Tarjeta AMEX\n+- [x] Pagar mensualidad a Mati\n+- [ ] Pagar viaje egresados Mati\n+- [x] Pagar 2% dividendos SaaS\n+- [x] Pagar número Movistar (2995 51-3063 / 2994 58-8493)\n+- [x] Pagar autónomos, monotributo (mío por débito y Juli por VEP) y IIGG Nqn (por débito)\n+- [x] Pagar Epas Trimestral ( http://www.epas.gov.ar/mi-cuenta/ )\n+- [x] Pagar Cablevisión (está por débito)\n+\n+\n+## MANTENIMIENTOS 🚗\n+\n+- 87500: Cambio de aceite y filtros. (Reservá tu turno en BOXES por la App YPF. Presentale al lubriexperto este mail para aplicar el descuento con el código 9507.)\n"}], "date": 1747308147705, "name": "Commit-0", "content": "\n@saas\n- [ ] Mandar archivos Uriel\n\n@crono\n- [ ] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n"}]}