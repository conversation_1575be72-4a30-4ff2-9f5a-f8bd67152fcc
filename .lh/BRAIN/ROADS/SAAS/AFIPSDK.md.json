{"sourceFile": "BRAIN/ROADS/SAAS/AFIPSDK.md", "activeCommit": 0, "commits": [{"activePatchIndex": 7, "patches": [{"date": 1750365917742, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750367879464, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,43 @@\n+# AFIPSDK\n+\n+## PLAN GENERAL\n+\n+Necesito un archivo a parte para llevar todo este trabajo que es grande. A nivel general los pasos son:\n+\n+- [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n+- [ ] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n+- [ ] [Lambda ARCA](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n+- [ ] Deploy de ALFA a BETA con test manual de a poco\n+- [ ] Estudiar todo lo que está dando vuelta en Google Groups\n+  - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n+  - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n+  - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n+  - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n+- [ ] Actualizar Pyafipws con Python 2.x como plan B\n+- [ ] Deploy BETA a PROD\n+\n+\n+## Para pasar a otros issues o evaluar\n+\n+- [ ] Agregar una ventana para poder ejecutar el antiafip manualmente y presente el informe todo dentro del módulo SaaS, con el mensaje de AFIP completo y toda la info necesaria para saber que está pasando.\n+- [ ] Evaluar si conviene cambiar los procesos de generación de certificados\n+- Tener en cuenta que hoy da Error de Razón Social con Ñ\n+\n+\n+## Acentar\n+\n+Condiciones de IVA:\n+1   IVA Responsable Inscripto\n+2   IVA Responsable no Inscripto\n+3   IVA no Responsable\n+4   IVA Sujeto Exento\n+5   Consumidor Final\n+6   Responsable Monotributo\n+7   Sujeto no Categorizado\n+8   Proveedor del Exterior\n+9   Cliente del Exterior\n+10  IVA Liberado – Ley Nº 19.640\n+11  IVA Responsable Inscripto – Agente de Percepción\n+12  Pequeño Contribuyente Eventual\n+13  Monotributista Social\n+14  Pequeño Contribuyente Eventual Social\n"}, {"date": 1750367930233, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -40,4 +40,52 @@\n 11  IVA Responsable Inscripto – Agente de Percepción\n 12  Pequeño Contribuyente Eventual\n 13  Monotributista Social\n 14  Pequeño Contribuyente Eventual Social\n+\n+\n+## PROMPTS\n+\n+Necesito ayuda para configurar variables de entorno y manejo de ambientes en mi función Lambda PHP para AFIP. \n+\n+Tengo el código actual en /home/<USER>/www/saasargentina/services/lambda/arca/ que procesa facturas electrónicas usando AFIPSDK.\n+\n+Necesito:\n+1. Sacar datos hardcodeados a variables de entorno (URLs SQS, bucket S3, credenciales)\n+2. Configurar claramente entorno homologación vs producción\n+3. Variables específicas por ambiente (dev/alpha/beta/prod)\n+\n+El código actual tiene hardcodeados:\n+- Bucket S3: 'saasargentina-wsfe'\n+- Queue SQS: 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue'\n+- Credenciales AWS en ErrorHandler\n+\n+¿Puedes ayudarme a organizarlo correctamente?\n+\n+---\n+\n+Necesito implementar manejo de reintentos y mecanismo de pausa para mi Lambda de AFIP en PHP.\n+\n+Contexto: Tengo una función Lambda que procesa facturas enviándolas a AFIP, pero necesito:\n+\n+1. Reintento automático cuando la API de AFIP está caída (reintentar a los 15 min)\n+2. Dead letter queue para mensajes que fallan múltiples veces\n+3. Mecanismo para pausar el procesamiento manualmente (como alternativa al cronjob)\n+4. Timeout y circuit breaker para llamadas a AFIP\n+\n+La función actual está en /home/<USER>/www/saasargentina/services/lambda/arca/\n+\n+¿Cómo implementarías esto con SQS y Lambda?\n+\n+---\n+\n+Necesito implementar un sistema de logging específico para monitorear facturas electrónicas en mi Lambda de AFIP.\n+\n+Requisitos:\n+1. Log estructurado de facturas aprobadas/rechazadas/errores\n+2. Métricas separadas por empresa y tipo de error\n+3. Sistema para revisar manualmente facturas problemáticas\n+4. Alertas para fallos masivos\n+\n+El código actual está en /home/<USER>/www/saasargentina/services/lambda/arca/ y ya tiene un ErrorHandler básico.\n+\n+¿Cómo estructurarías un sistema de logging robusto para esto?\n"}, {"date": 1750367988208, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,8 +7,9 @@\n - [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n - [ ] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n - [ ] [Lambda ARCA](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n - [ ] Deploy de ALFA a BETA con test manual de a poco\n+- [ ] Documentar el Lambda\n - [ ] Estudiar todo lo que está dando vuelta en Google Groups\n   - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n   - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n@@ -44,9 +45,9 @@\n \n \n ## PROMPTS\n \n-Necesito ayuda para configurar variables de entorno y manejo de ambientes en mi función Lambda PHP para AFIP. \n+Necesito ayuda para configurar variables de entorno y manejo de ambientes en mi función Lambda PHP para AFIP.\n \n Tengo el código actual en /home/<USER>/www/saasargentina/services/lambda/arca/ que procesa facturas electrónicas usando AFIPSDK.\n \n Necesito:\n"}, {"date": 1750433765783, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,9 +5,10 @@\n Necesito un archivo a parte para llevar todo este trabajo que es grande. A nivel general los pasos son:\n \n - [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n - [ ] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n-- [ ] [Lambda ARCA](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n+- [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n+\n - [ ] Deploy de ALFA a BETA con test manual de a poco\n - [ ] Documentar el Lambda\n - [ ] Estudiar todo lo que está dando vuelta en Google Groups\n   - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n"}, {"date": 1750513977234, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,9 +8,9 @@\n - [ ] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n - [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n \n - [ ] Deploy de ALFA a BETA con test manual de a poco\n-- [ ] Documentar el Lambda\n+- [x] Documentar el Lambda\n - [ ] Estudiar todo lo que está dando vuelta en Google Groups\n   - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n   - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n@@ -46,26 +46,8 @@\n \n \n ## PROMPTS\n \n-Necesito ayuda para configurar variables de entorno y manejo de ambientes en mi función Lambda PHP para AFIP.\n-\n-Tengo el código actual en /home/<USER>/www/saasargentina/services/lambda/arca/ que procesa facturas electrónicas usando AFIPSDK.\n-\n-Necesito:\n-1. Sacar datos hardcodeados a variables de entorno (URLs SQS, bucket S3, credenciales)\n-2. Configurar claramente entorno homologación vs producción\n-3. Variables específicas por ambiente (dev/alpha/beta/prod)\n-\n-El código actual tiene hardcodeados:\n-- Bucket S3: 'saasargentina-wsfe'\n-- Queue SQS: 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue'\n-- Credenciales AWS en ErrorHandler\n-\n-¿Puedes ayudarme a organizarlo correctamente?\n-\n----\n-\n Necesito implementar manejo de reintentos y mecanismo de pausa para mi Lambda de AFIP en PHP.\n \n Contexto: Tengo una función Lambda que procesa facturas enviándolas a AFIP, pero necesito:\n \n"}, {"date": 1750517692890, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,33 +43,4 @@\n 12  Pequeño Contribuyente Eventual\n 13  Monotributista Social\n 14  Pequeño Contribuyente Eventual Social\n \n-\n-## PROMPTS\n-\n-Necesito implementar manejo de reintentos y mecanismo de pausa para mi Lambda de AFIP en PHP.\n-\n-Contexto: Tengo una función Lambda que procesa facturas enviándolas a AFIP, pero necesito:\n-\n-1. Reintento automático cuando la API de AFIP está caída (reintentar a los 15 min)\n-2. Dead letter queue para mensajes que fallan múltiples veces\n-3. Mecanismo para pausar el procesamiento manualmente (como alternativa al cronjob)\n-4. Timeout y circuit breaker para llamadas a AFIP\n-\n-La función actual está en /home/<USER>/www/saasargentina/services/lambda/arca/\n-\n-¿Cómo implementarías esto con SQS y Lambda?\n-\n----\n-\n-Necesito implementar un sistema de logging específico para monitorear facturas electrónicas en mi Lambda de AFIP.\n-\n-Requisitos:\n-1. Log estructurado de facturas aprobadas/rechazadas/errores\n-2. Métricas separadas por empresa y tipo de error\n-3. Sistema para revisar manualmente facturas problemáticas\n-4. Alertas para fallos masivos\n-\n-El código actual está en /home/<USER>/www/saasargentina/services/lambda/arca/ y ya tiene un ErrorHandler básico.\n-\n-¿Cómo estructurarías un sistema de logging robusto para esto?\n"}, {"date": 1750542819887, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,4 +43,33 @@\n 12  Pequeño Contribuyente Eventual\n 13  Monotributista Social\n 14  Pequeño Contribuyente Eventual Social\n \n+## PROMPTS\n+\n+Estoy migrando un proceso de aprobación de ventas que son facturas electrónicas de Argentina. Voy a darte todo el contexto y los pasos que quiero realizar. Te pido que analices todo lo que vamos a modificar, pero que vayamos de a un paso a la vez.\n+\n+En prompts anteriores ya ejecutamos los pasos 1, 2, 3, 4 y 13\n+\n+## Ten en cuenta los siguientes temas:\n+\n+- El proceso anterior utiliza una función en php que llama con la función `exec` a una librería que está en Python llamada pyafipws. Puedes ver la función `rece1` y las subfunciones que llama en el archivo #file:funciones_wsfe.php  NO MODIFIQUES NADA DE ESTE ARCHIVO. Para futura referencia más abajo de este prompt, lo voy a llamar \"RECE\"\n+- El proceso nuevo es una función de AWS Lambda que llama a una API con el SDK AFIPSDK. Puedes ver esta función en el archivo #file:afipsdk.php  SI MODIFICA ESTE ARCHIVO. Para futura referencia más abajo de este prompt lo voy a llamar \"LAMBDA\".\n+- La función `rece1` tiene 3 parámetros: `$id` que en el proceso nuevo es el `idventa` y otros 2 que no los vamos a necesitar migrar.\n+\n+## La función `rece1` tiene los siguientes pasos:\n+\n+1- Obtiene información de la venta. Esto hay que replicarlo en el método que ya existe `obtenerVenta` en LAMBDA, sólo modificando la consulta mysql.\n+2- Verifica si corresponde intentar emitir y si hay una factura del mismo tipo esperando CAE. Hay que agregar estas verificaciones en un nuevo método `debeAprobar` en LAMBDA que tenga estas 2 validaciones y si corresponde actualiza el registro y termina el proceso con el log correspondiente.\n+3- Descarga y bloquea el certificado con la función `bloquear_wsfe` de RECE y ya tenemos esto funcionando en `descargarCertificados` de LAMBDA, así que esto no hay que modificarlo.\n+4- Buscamos el número que dice ARCA que fue el último. Este proceso en RECE se hace con el método `rece1_ultimonumero` y hay que aplicarlo en un método de LAMBDA llamada `ultimoNumero` que se conecte con la API de AFIPSDK según la documentación que puedes ver en https://docs.afipsdk.com/siguientes-pasos/web-services/factura-electronica . En este mismo método, quiero aplicar también lo que se realiza en RECE `analizar_salida_ultimonumero` que pueden ser respuestas que vengan de AFIPSDK o no. También hay que agregar todas las verificaciones que tenemos en la función `rece1_verificar`. Hazlo simple.\n+5- Sigue actualizar número de venta. Esto es delicado, por lo que quiero que analices la funcion `actualizar_numero_venta` de RECE y que lo apliques en un nuevo método `actualizarNumero` pero con mucho cuidado de que funcione igual. Puedes revisar la consulta para ver si se puede mejorar su performance, pero es impresindible que esta función quede bien. Puedes meter ahí también el próximo paso que es verificar que la anterior esté ok\n+6- El próximo paso es Verifico que no tenga No Aplica si es RI y hay que replicarlo igual, este es fácil.\n+7- Hay un proceso que es `sin_control_numeracion` que su código es `return in_array($idempresa, [8905, 8980, 11597, 11854]);` . Este hay que migrarlo igual a un método `sinControlNumeracion` y aplicarlo en los mismos lugares.\n+8- Verifica y actualiza la fecha. Este proceso también hay que replicarlo en un método `actualizarFecha`\n+9- Luego tenemos el llamado a `generar_entrada_wsfe` que es el `prepararComprobante` en LAMBDA. Por el momento no toquemos este método.\n+10- Ahí si tenemos el llamado con el `exec` que ya está en el método `enviarFacturaAfip` que por ahora no lo toquemos.\n+11- La función `leer_salida_wsfe` de RECE es simplemente lo que devuelve `enviarFacturaAfip` así que no la necesitamos. Pero si la función `analizar_salida_wsfe` la tenemos que pasar con mucho cuidado de que funcione bien y quede bien registrada la devolución. Ahí hay un retorno de la función al array `$return` que quiero evitar, tiene que quedar más prolijo.\n+12- La función `desbloquear_wsfe` no tiene que pasarse porque ya limpiamos los certificados descargados y no hay que desbloquear nada.\n+13- Luego tenemos un par de líneas que analizan y actualizan la venta. Eso creo que puedes dejarlo bien prolijo en LAMBDA en el método `actualizarVentaExitosa`, que ya que estás cambia el nombre a `actualizarVenta` y que pueda actualizarse el estadocae a `rechazado` cuando corresponde. También hay que corregir los nombres de los campos que están mal y agregar `obscae` que es importante.\n+14- Por último quiero que se envíe el mail de error que está en ErrorHandler cuando la venta es rechazada o hay algún error.\n+\n"}], "date": 1750365917742, "name": "Commit-0", "content": ""}]}