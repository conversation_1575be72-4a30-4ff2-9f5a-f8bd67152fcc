{"sourceFile": "BRAIN/ROADS/CRONO/DEV Hardware.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1749403318602, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1749403318601, "name": "Commit-0", "content": "\n## CRONOMETRADOR\n\n@later\n- [ ] Oficina en Van\n- [ ] Armar totem con monitor y caja estanco (que se pueda usar para embudo de salida)\n- [ ] Batería en la valija del reader (ver que ya está en YouTube lo del yanki)\n- [ ] Dejar truss listo con uniones en caja\n- [ ] Agregar Acrílico y Led al totem\n- [ ] Armar o comprar valijas apilables para 100 chips recuperables\n- [ ] Sumar el reader Zebra\n- [ ] Comprar 4 antenas de 9db y armar una valija para llevarlas con cables y soportes\n- [ ] Imprimir lonas para el gazebo tipo media pared y para cerrar con trípodes ciertos sectores. Tiene que tener ojales para atarlo en varios lados\n- [ ] Comprar sillas más chicas para crono\n- [ ] Comprar reloj ALGE\n- [ ] Comprar fotocélula ALGE\n- [ ] Comprar carpa para el auto en Amazon\n- [ ] Hacerme el 270° awning\n\n---\n\n### Info varias\n- El usuario predeterminado de Zebra es **admin** y la contraseña **change** (no pude probarlo todavía)\n- El host predeterminado es FX9600F23B74\n- La IP de la placa del Rasberry es ***********\n- Adrenaline usa Classify https://www.youtube.com/watch?v=a1FclmzeGdA\n\n### Integraciones\n- Integración simple:\n\t- [ ] Configurar FPT y crear cron + queue para procesar los CSV subidos\n- Conectar Zebra directo\n\t- [ ] Probar conectar directo al reader (No pude, se podría resetear pero puede que no vuelva a funcionar con Macsha)\n\t- [ ] Buscar la contraseña del Zebra para ver si podemos ver y guardar la configuración (Probar la predeterminada y sino preguntar a Macsha)\n\t- [ ] Probar de cambiar el reader en el Macsha y ver si funciona\n\t- [ ] La IP del Zebra puede estar dentro del rango 192.168.2.x (una vez me pude conectar a ************ pero ahora está Macsha ahí)\n- Utilizar chips\n\t- [x] Probar de leer chips con equipos de Gaby (se me ocurre que pueden tener grabado algo los chips)\n- Otras pruebas\n\t- [x] Conectar el Raspberry al tele (Ver si de ahí se pueden ver las conexiones de red y por ende la IP de Zebra. NO se puede acceder)\n\t- [ ] Conectar las otras antenas\n\t- [ ] Conseguir un medidor de señal y jugar a ver si sirve\n\n---\n## PLACA DE TOQUE\n- [ ] Probar de leer desde un Raspberry Pi con el USB\n- [ ] Probar con alargue a una PC\n- [ ] Probar de conectar a una tablet\n\n---\n## KIOSKO\n- [ ] Programar una app y/o la misma app para que quede leyendo y mostrando el ticket digital\n\n\n---\n## RELOJ PARTIDA Y LLEGADA\n- Reloj en partida (ver tablet) y en llegada en la llegada para el mundial y para Chile\n- Tiene que poder mostrar el nombre del próximo que debería salir según listado de partida\n- Julio y Oscar de Santiago de Chile estaban buscando eso\n\n"}]}