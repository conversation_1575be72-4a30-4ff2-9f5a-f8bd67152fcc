{"sourceFile": "BRAIN/ROADS/CRONO/GROWTH.md", "activeCommit": 0, "commits": [{"activePatchIndex": 12, "patches": [{"date": 1724850139380, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1727094105513, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,10 @@\n \n TODO: Definir los milestones de MKT para el sitio web y las redes, para luego convertirlas en tareas a Todoist.\n - [ ] Agregar plan de newsletter, agregar bienvenida\n - [ ] Ahora que has vinculado tu cuenta de Google Analytics a Google Ads, no olvides usar las audiencias que Analytics ha preparado para ti para mostrar anuncios más relevantes. No es necesario implementar código adicional en tu sitio.\n+- [ ] Evaluar el mercado usando AI https://www.frederick.ai/\n+- [ ] Ver las sugerencias de herramientas de AI de la última parte de este vídeo: https://www.youtube.com/watch?v=XhgWBUBJtnE\n \n ## CONTENIDOS\n \n Tenemos un gran listado de IDEAS en [IDEAS MKT](./GROWTH%20Ideas%20MKT.md) para ir ordenando Y procesando. Las ideas que se van a convertir en los próximos contenidos las vamos cargando en el [TODO de CONTENIDOS](https://app.todoist.com/app/project/growth-2270118842).\n"}, {"date": 1741890968911, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,12 @@\n # 🥇 ROADS > CRONO > GROWTH BOARD\n -------------------------------------------------------------------------------\n \n+## VER AL ARMAR TODO AHORA\n+\n+- https://futurepedia.beehiiv.com/p/create-engaging-marketing-videos-on-a-budget\n+\n+\n ## MKT\n \n TODO: Definir los milestones de MKT para el sitio web y las redes, para luego convertirlas en tareas a Todoist.\n - [ ] Agregar plan de newsletter, agregar bienvenida\n"}, {"date": 1742656388653, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,42 +1,51 @@\n # 🥇 ROADS > CRONO > GROWTH BOARD\n -------------------------------------------------------------------------------\n \n-## VER AL ARMAR TODO AHORA\n+## GROWTH\n \n-- https://futurepedia.beehiiv.com/p/create-engaging-marketing-videos-on-a-budget\n+Siguiendo con el plan general 2024-2026, vamos a apuntar a las siguientes estrategias:\n \n+- Estrategia SEO/AEO: para eso vamos a mejorar la arquitectura de metadatos, generar los sitios para cada disciplina y cada hardware, y mejorar la calidad de los contenidos.\n+- Estrategia de deportes: para eso vamos a generar las páginas en nuestro sitio y los cambios en el sistema.\n+- Estrategia de contenido: creando un proceso (framework) explicado aquí abajo.\n \n-## MKT\n \n-TODO: Definir los milestones de MKT para el sitio web y las redes, para luego convertirlas en tareas a Todoist.\n-- [ ] Agregar plan de newsletter, agregar bienvenida\n-- [ ] Ahora que has vinculado tu cuenta de Google Analytics a Google Ads, no olvides usar las audiencias que Analytics ha preparado para ti para mostrar anuncios más relevantes. No es necesario implementar código adicional en tu sitio.\n-- [ ] Evaluar el mercado usando AI https://www.frederick.ai/\n-- [ ] Ver las sugerencias de herramientas de AI de la última parte de este vídeo: https://www.youtube.com/watch?v=XhgWBUBJtnE\n+## CONTENIDOS IDEAS\n \n-## CONTENIDOS\n+Las próximas ideas a trabajar van a estar en mi [Todoist](https://app.todoist.com/app/project/growth-2270118842).\n \n-Tenemos un gran listado de IDEAS en [IDEAS MKT](./GROWTH%20Ideas%20MKT.md) para ir ordenando Y procesando. Las ideas que se van a convertir en los próximos contenidos las vamos cargando en el [TODO de CONTENIDOS](https://app.todoist.com/app/project/growth-2270118842).\n+Para después tenemos un gran listado de IDEAS en [IDEAS MKT](./GROWTH%20Ideas%20MKT.md) para ir ordenando Y procesando.\n \n-Mensualmente se deben seleccionar entre 3 y 6 CONTENIDOS para generar. Cada una se debe pasar por el siguiente proceso:\n \n+## FRAMEWORK CONTENIDO\n+\n - Antes de generar las ideas, hay que analizar las metricas de los contenidos anteriores y ver que se puede mejorar o que es más efectivo.\n+\n+**Preparo el guíon**\n+\n - Hacer un resumen de la idea, el objetivo y el contenido ya pensado. Se puede utilizar un audio y pasarlo por alguna AI para limpiar la idea.\n - Generar el contenido con el prompt AI [CRONO_generar_contenido](/BRAIN/AI/CRONO_generar_contenido.md).\n+\n+**Grabo el vídeo**\n+\n - Armar set (Luces: ventana, spot Juli para fondo, foco lectura sobre cama y luz pieza / Usar mic / Trípode sobre cama / Usar doble cámara con las mismas resoluciones)\n-- Al grabar el vídeo tener en cuenta (Pasar en limpio):\n-  - MrBeast Strategi: https://www.youtube.com/watch?v=F-XZshE_3iA\n-  - Tengo bastantes Reels guardados en Instagram\n - Grabar vídeos, recortar con Capcut, subir a Drive y generar los enlaces para el editor.\n+\n+**Delego la publicación de contenido**\n+\n - Escribir una tarea para Prisci con las modificaciones en la WEB si son necesarias.\n-- Escribir una tarea configurar las métricas en Google Analytics y Search Console.\n - Escribir una tarea para Prisci si va a llevar publicidad o diseño en la web.\n-  - Si el contenido es sobre un deporte que podemos ampliar algún mercado, poner publicidad sólo ahí o más ahí que en el resto.\n - Escribir una orden de contenido (puede ser que requiera dividir el prompt AI), generar html y delegarlo por Whatsapp. Guardar el MD y HTML en CRONO.\n - Escribir newsletter invitando a leer el contenido.\n - Eliminar la tarea de CONTENIDOS y confiar en que Juli controla el proceso.\n \n \n-## VENTAS\n \n-TODO: Desde el plan de Ventas, definir los milestones y los procesos.\n+\n+- Al grabar el vídeo tener en cuenta (Pasar en limpio):\n+  - MrBeast Strategi: https://www.youtube.com/watch?v=F-XZshE_3iA\n+  - Tengo bastantes Reels guardados en Instagram\n+\n+- https://futurepedia.beehiiv.com/p/create-engaging-marketing-videos-on-a-budget\n+- [ ] Ver las sugerencias de herramientas de AI de la última parte de este vídeo: https://www.youtube.com/watch?v=XhgWBUBJtnE\n+\n"}, {"date": 1742656395553, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,15 +37,4 @@\n - Escribir una tarea para Prisci si va a llevar publicidad o diseño en la web.\n - Escribir una orden de contenido (puede ser que requiera dividir el prompt AI), generar html y delegarlo por Whatsapp. Guardar el MD y HTML en CRONO.\n - Escribir newsletter invitando a leer el contenido.\n - Eliminar la tarea de CONTENIDOS y confiar en que Juli controla el proceso.\n-\n-\n-\n-\n-- Al grabar el vídeo tener en cuenta (Pasar en limpio):\n-  - MrBeast Strategi: https://www.youtube.com/watch?v=F-XZshE_3iA\n-  - Tengo bastantes Reels guardados en Instagram\n-\n-- https://futurepedia.beehiiv.com/p/create-engaging-marketing-videos-on-a-budget\n-- [ ] Ver las sugerencias de herramientas de AI de la última parte de este vídeo: https://www.youtube.com/watch?v=XhgWBUBJtnE\n-\n"}, {"date": 1747318729861, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n-# 🥇 ROADS > CRONO > GROWTH BOARD\n+# 🏅 ROADS > CRONO > GROWTH BOARD\n -------------------------------------------------------------------------------\n \n ## GROWTH\n \n"}, {"date": 1747319985463, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,20 @@\n # 🏅 ROADS > CRONO > GROWTH BOARD\n--------------------------------------------------------------------------------\n \n+## TODO ORDENAR\n+\n+- [ ] Modificaciones en el sitio web\n+- [ ] Cambios en \"Home\"\n+- [ ] Cambiar el listado de deportes por los 9 deportes principales, supongo que lo mejor es usar 3 líneas y 3 columnas.\n+- [ ] Cambiar los nombres en los logos, siguiendo el listado de deportes, donde dice Gestión completa para ..., por ej. de \"Downhill\" por \"Ciclismo\"\n+- [ ] Intercambiar el orden entre los deportes, dejar como el listado de arriba: Ciclismo, Running, Rally, Motociclismo, Aguas Abiertas, etc.\n+- [ ] Agregar botones de los deportes con el texto: \"Más información de Ciclismo\" que lleve a la página de cada deporte\n+- [ ] Hay que crear un menú en el sitio, antes de Tecnologías, que sea Deportes y que tenga la lista de los 9 deportes principales, cada uno lleva a la página correspondiente. También hay que crear 9 páginas, una para cada deporte, supongo que conviene crear la y duplicarla.\n+- [ ] Video de Youtube Inscripciones @focus\n+- [ ] Vídeo de llegadas muy juntas en DH @mobile\n+- [ ] Video de invitación a ver el video Completo de Youtube\n+\n+\n ## GROWTH\n \n Siguiendo con el plan general 2024-2026, vamos a apuntar a las siguientes estrategias:\n \n"}, {"date": 1747320629850, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,9 @@\n # 🏅 ROADS > CRONO > GROWTH BOARD\n \n ## TODO ORDENAR\n \n+- [ ] Mover Apple a la LLC (https://mail.google.com/mail/u/0/#inbox/********************************)\n - [ ] Modificaciones en el sitio web\n - [ ] Cambios en \"Home\"\n - [ ] Cambiar el listado de deportes por los 9 deportes principales, supongo que lo mejor es usar 3 líneas y 3 columnas.\n - [ ] Cambiar los nombres en los logos, siguiendo el listado de deportes, donde dice Gestión completa para ..., por ej. de \"Downhill\" por \"Ciclismo\"\n@@ -11,26 +12,47 @@\n - [ ] Hay que crear un menú en el sitio, antes de Tecnologías, que sea Deportes y que tenga la lista de los 9 deportes principales, cada uno lleva a la página correspondiente. También hay que crear 9 páginas, una para cada deporte, supongo que conviene crear la y duplicarla.\n - [ ] Video de Youtube Inscripciones @focus\n - [ ] Vídeo de llegadas muy juntas en DH @mobile\n - [ ] Video de invitación a ver el video Completo de Youtube\n+- [ ] Reunión Andres Colombia y Preparar Contrato\n+- [ ] Manual de marca en sitio exclusivo\n+- [ ] Registrar la marca\n+- [ ] Imprimir fondo de podio para Crono\n \n-\n ## GROWTH\n \n Siguiendo con el plan general 2024-2026, vamos a apuntar a las siguientes estrategias:\n \n - Estrategia SEO/AEO: para eso vamos a mejorar la arquitectura de metadatos, generar los sitios para cada disciplina y cada hardware, y mejorar la calidad de los contenidos.\n - Estrategia de deportes: para eso vamos a generar las páginas en nuestro sitio y los cambios en el sistema.\n - Estrategia de contenido: creando un proceso (framework) explicado aquí abajo.\n \n+## MKT ORDENAR\n \n+- [ ] [Unlock SEO Potential with AI-Powered Keyword Research](https://neilpatel.com/blog/ai-keyword-research/?utm_medium=email&utm_source=convertkit-list&utm_campaign=us-mkt-campaigns-mql-content-blog-promotional-email&utm_content=blog-promote&utm_term=ck-november-9&cf-last-campaign-source=US%20-%20Mkt%20Campaigns%20-%20MQL%20-%20Content%20-%20Blog%20Promotional%20Email) @next\n+- [ ] Todas las herramientas de Google super configuradas y monitoreadas @next\n+- [ ] https://neilpatel.com/blog/google-analytics-4/\n+- [ ] [Accept the new Terms of Service to upgrade your Google Analytics for Firebase property - Analytics Help](https://support.google.com/analytics/answer/10960488)\n+- [ ] Configurar acceso a Pri también\n+- [ ] Para monitorear temas de SEO [Telescope - Usage-Based SEO Research Tool](https://withtelescope.com/)\n+- [ ] [Guía completa para evitar el contenido duplicado con enlaces canonicals - ▶️ Blog de Marketing Digital - Elabs Consulting](https://www.elabsconsulting.com/noticias/guia-completa-para-evitar-el-contenido-duplicado-con-enlaces-canonicals)\n+- [ ] Comparar como ChatGPT conoce a la empresa\n+- [ ] Más revisiones de SEO a la web y blog\n+- [ ] [Configuring “Schema Markup” in Rank Math » Rank Math](https://rankmath.com/kb/rich-snippets/)\n+- [ ] [AutoOptimize.ai - Automatically Optimize Your Site Conversion Rate](https://www.autooptimize.ai/?coupon=fbspecial)\n+- [ ] [12 Dead Simple Steps To Complete Your First SEO Audit for Free with Rank Math](https://rankmath.com/blog/seo-audit/)\n+- [ ] [Google now shows why it ranked a specific search result](https://searchengineland.com/google-now-shows-why-it-ranked-a-specific-search-result-350659?utm_source=pocket-app&utm_medium=share)\n+- [ ] [INP (Interaction to Next Paint): A Guide To Core Web Vital Changes](https://neilpatel.com/blog/inp/)\n+- [ ] Agregar los productos de Crono a Google Merchant Center\n+- [ ] Ofrecer software y RFID en ML\n+- [ ] Evaluar el mercado usando AI [Home - AI teammates for Your Startup](https://www.frederick.ai/)\n+\n+\n ## CONTENIDOS IDEAS\n \n-Las próximas ideas a trabajar van a estar en mi [Todoist](https://app.todoist.com/app/project/growth-2270118842).\n+Para después tenemos un gran listado de IDEAS en [CONTENIDO](./CONTENIDO.md) para ir ordenando Y procesando.\n \n-Para después tenemos un gran listado de IDEAS en [IDEAS MKT](./GROWTH%20Ideas%20MKT.md) para ir ordenando Y procesando.\n \n-\n ## FRAMEWORK CONTENIDO\n \n - Antes de generar las ideas, hay que analizar las metricas de los contenidos anteriores y ver que se puede mejorar o que es más efectivo.\n \n"}, {"date": 1749070110550, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,53 +1,32 @@\n # 🏅 ROADS > CRONO > GROWTH BOARD\n \n ## TODO ORDENAR\n \n+- [ ] Generar contenido de novedades y para que tenga Prisci, ya está en (CONTENIDO)[./CONTENIDO.md]\n+- [ ] Actualizar novedades y recuperar newsletter\n+\n+- [ ] Priscila modificaciones en el sitio web\n+    - Cambios en \"Home\"\n+    - Cambiar el listado de deportes por los 9 deportes principales, supongo que lo mejor es usar 3 líneas y 3 columnas.\n+    - Cambiar los nombres en los logos, siguiendo el listado de deportes, donde dice Gestión completa para ..., por ej. de \"Downhill\" por \"Ciclismo\"\n+    - Intercambiar el orden entre los deportes, dejar como el listado de arriba: Ciclismo, Running, Rally, Motociclismo, Aguas Abiertas, etc.\n+    - Agregar botones de los deportes con el texto: \"Más información de Ciclismo\" que lleve a la página de cada deporte\n+    - Hay que crear un menú en el sitio, antes de Tecnologías, que sea Deportes y que tenga la lista de los 9 deportes principales, cada uno lleva a la página correspondiente. También hay que crear 9 páginas, una para cada deporte, supongo que conviene crear la y duplicarla.\n+\n - [ ] Mover Apple a la LLC (https://mail.google.com/mail/u/0/#inbox/********************************)\n-- [ ] Modificaciones en el sitio web\n-- [ ] Cambios en \"Home\"\n-- [ ] Cambiar el listado de deportes por los 9 deportes principales, supongo que lo mejor es usar 3 líneas y 3 columnas.\n-- [ ] Cambiar los nombres en los logos, siguiendo el listado de deportes, donde dice Gestión completa para ..., por ej. de \"Downhill\" por \"Ciclismo\"\n-- [ ] Intercambiar el orden entre los deportes, dejar como el listado de arriba: Ciclismo, Running, Rally, Motociclismo, Aguas Abiertas, etc.\n-- [ ] Agregar botones de los deportes con el texto: \"Más información de Ciclismo\" que lleve a la página de cada deporte\n-- [ ] Hay que crear un menú en el sitio, antes de Tecnologías, que sea Deportes y que tenga la lista de los 9 deportes principales, cada uno lleva a la página correspondiente. También hay que crear 9 páginas, una para cada deporte, supongo que conviene crear la y duplicarla.\n-- [ ] Video de Youtube Inscripciones @focus\n-- [ ] Vídeo de llegadas muy juntas en DH @mobile\n-- [ ] Video de invitación a ver el video Completo de Youtube\n - [ ] Reunión Andres Colombia y Preparar Contrato\n - [ ] Manual de marca en sitio exclusivo\n - [ ] Registrar la marca\n-- [ ] Imprimir fondo de podio para Crono\n \n ## GROWTH\n \n Siguiendo con el plan general 2024-2026, vamos a apuntar a las siguientes estrategias:\n \n-- Estrategia SEO/AEO: para eso vamos a mejorar la arquitectura de metadatos, generar los sitios para cada disciplina y cada hardware, y mejorar la calidad de los contenidos.\n+- Estrategia SEO/AEO: para eso vamos a mejorar la arquitectura de metadatos, generar sector de eventos, generar sectir de cursos y sitios.\n - Estrategia de deportes: para eso vamos a generar las páginas en nuestro sitio y los cambios en el sistema.\n-- Estrategia de contenido: creando un proceso (framework) explicado aquí abajo.\n+- Estrategia de contenido: siguiendo el proceso (framework) de abajo y generando desde el listado de ideas.\n \n-## MKT ORDENAR\n-\n-- [ ] [Unlock SEO Potential with AI-Powered Keyword Research](https://neilpatel.com/blog/ai-keyword-research/?utm_medium=email&utm_source=convertkit-list&utm_campaign=us-mkt-campaigns-mql-content-blog-promotional-email&utm_content=blog-promote&utm_term=ck-november-9&cf-last-campaign-source=US%20-%20Mkt%20Campaigns%20-%20MQL%20-%20Content%20-%20Blog%20Promotional%20Email) @next\n-- [ ] Todas las herramientas de Google super configuradas y monitoreadas @next\n-- [ ] https://neilpatel.com/blog/google-analytics-4/\n-- [ ] [Accept the new Terms of Service to upgrade your Google Analytics for Firebase property - Analytics Help](https://support.google.com/analytics/answer/10960488)\n-- [ ] Configurar acceso a Pri también\n-- [ ] Para monitorear temas de SEO [Telescope - Usage-Based SEO Research Tool](https://withtelescope.com/)\n-- [ ] [Guía completa para evitar el contenido duplicado con enlaces canonicals - ▶️ Blog de Marketing Digital - Elabs Consulting](https://www.elabsconsulting.com/noticias/guia-completa-para-evitar-el-contenido-duplicado-con-enlaces-canonicals)\n-- [ ] Comparar como ChatGPT conoce a la empresa\n-- [ ] Más revisiones de SEO a la web y blog\n-- [ ] [Configuring “Schema Markup” in Rank Math » Rank Math](https://rankmath.com/kb/rich-snippets/)\n-- [ ] [AutoOptimize.ai - Automatically Optimize Your Site Conversion Rate](https://www.autooptimize.ai/?coupon=fbspecial)\n-- [ ] [12 Dead Simple Steps To Complete Your First SEO Audit for Free with Rank Math](https://rankmath.com/blog/seo-audit/)\n-- [ ] [Google now shows why it ranked a specific search result](https://searchengineland.com/google-now-shows-why-it-ranked-a-specific-search-result-350659?utm_source=pocket-app&utm_medium=share)\n-- [ ] [INP (Interaction to Next Paint): A Guide To Core Web Vital Changes](https://neilpatel.com/blog/inp/)\n-- [ ] Agregar los productos de Crono a Google Merchant Center\n-- [ ] Ofrecer software y RFID en ML\n-- [ ] Evaluar el mercado usando AI [Home - AI teammates for Your Startup](https://www.frederick.ai/)\n-\n-\n ## CONTENIDOS IDEAS\n \n Para después tenemos un gran listado de IDEAS en [CONTENIDO](./CONTENIDO.md) para ir ordenando Y procesando.\n \n@@ -72,4 +51,25 @@\n - Escribir una tarea para Prisci si va a llevar publicidad o diseño en la web.\n - Escribir una orden de contenido (puede ser que requiera dividir el prompt AI), generar html y delegarlo por Whatsapp. Guardar el MD y HTML en CRONO.\n - Escribir newsletter invitando a leer el contenido.\n - Eliminar la tarea de CONTENIDOS y confiar en que Juli controla el proceso.\n+\n+\n+## MKT ORDENAR\n+\n+- [ ] [Unlock SEO Potential with AI-Powered Keyword Research](https://neilpatel.com/blog/ai-keyword-research/?utm_medium=email&utm_source=convertkit-list&utm_campaign=us-mkt-campaigns-mql-content-blog-promotional-email&utm_content=blog-promote&utm_term=ck-november-9&cf-last-campaign-source=US%20-%20Mkt%20Campaigns%20-%20MQL%20-%20Content%20-%20Blog%20Promotional%20Email) @next\n+- [ ] Todas las herramientas de Google super configuradas y monitoreadas @next\n+- [ ] https://neilpatel.com/blog/google-analytics-4/\n+- [ ] [Accept the new Terms of Service to upgrade your Google Analytics for Firebase property - Analytics Help](https://support.google.com/analytics/answer/10960488)\n+- [ ] Configurar acceso a Pri también\n+- [ ] Para monitorear temas de SEO [Telescope - Usage-Based SEO Research Tool](https://withtelescope.com/)\n+- [ ] [Guía completa para evitar el contenido duplicado con enlaces canonicals - ▶️ Blog de Marketing Digital - Elabs Consulting](https://www.elabsconsulting.com/noticias/guia-completa-para-evitar-el-contenido-duplicado-con-enlaces-canonicals)\n+- [ ] Comparar como ChatGPT conoce a la empresa\n+- [ ] Más revisiones de SEO a la web y blog\n+- [ ] [Configuring “Schema Markup” in Rank Math » Rank Math](https://rankmath.com/kb/rich-snippets/)\n+- [ ] [AutoOptimize.ai - Automatically Optimize Your Site Conversion Rate](https://www.autooptimize.ai/?coupon=fbspecial)\n+- [ ] [12 Dead Simple Steps To Complete Your First SEO Audit for Free with Rank Math](https://rankmath.com/blog/seo-audit/)\n+- [ ] [Google now shows why it ranked a specific search result](https://searchengineland.com/google-now-shows-why-it-ranked-a-specific-search-result-350659?utm_source=pocket-app&utm_medium=share)\n+- [ ] [INP (Interaction to Next Paint): A Guide To Core Web Vital Changes](https://neilpatel.com/blog/inp/)\n+- [ ] Agregar los productos de Crono a Google Merchant Center\n+- [ ] Ofrecer software y RFID en ML\n+- [ ] Evaluar el mercado usando AI [Home - AI teammates for Your Startup](https://www.frederick.ai/)\n"}, {"date": 1749070131787, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,8 +17,10 @@\n - [ ] Reunión Andres Colombia y Preparar Contrato\n - [ ] Manual de marca en sitio exclusivo\n - [ ] Registrar la marca\n \n+---\n+\n ## GROWTH\n \n Siguiendo con el plan general 2024-2026, vamos a apuntar a las siguientes estrategias:\n \n"}, {"date": 1749135261070, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,77 @@\n+# 🏅 ROADS > CRONO > GROWTH BOARD\n+\n+## TODO ORDENAR\n+\n+- [ ] Generar contenido de novedades y para que tenga Prisci, ya está en (CONTENIDO)[./CONTENIDO.md]\n+- [ ] Actualizar novedades y recuperar newsletter\n+\n+- [ ] Priscila modificaciones en el sitio web\n+    - Cambios en \"Home\"\n+    - Cambiar el listado de deportes por los 9 deportes principales, supongo que lo mejor es usar 3 líneas y 3 columnas.\n+    - Cambiar los nombres en los logos, siguiendo el listado de deportes, donde dice Gestión completa para ..., por ej. de \"Downhill\" por \"Ciclismo\"\n+    - Intercambiar el orden entre los deportes, dejar como el listado de arriba: Ciclismo, Running, Rally, Motociclismo, Aguas Abiertas, etc.\n+    - Agregar botones de los deportes con el texto: \"Más información de Ciclismo\" que lleve a la página de cada deporte\n+    - Hay que crear un menú en el sitio, antes de Tecnologías, que sea Deportes y que tenga la lista de los 9 deportes principales, cada uno lleva a la página correspondiente. También hay que crear 9 páginas, una para cada deporte, supongo que conviene crear la y duplicarla.\n+\n+- [ ] Mover Apple a la LLC (https://mail.google.com/mail/u/0/#inbox/********************************)\n+- [ ] Reunión Andres Colombia y Preparar Contrato\n+- [ ] Manual de marca en sitio exclusivo\n+- [ ] Registrar la marca\n+\n+---\n+\n+## GROWTH\n+\n+Siguiendo con el plan general 2024-2026, vamos a apuntar a las siguientes estrategias:\n+\n+- *Estrategia SEO/AEO*: para eso vamos a mejorar la arquitectura de metadatos, generar sector de eventos, generar sectir de cursos y sitios.\n+- *Estrategia de deportes*: para eso vamos a generar las páginas en nuestro sitio y los cambios en el sistema.\n+- *Estrategia de contenido*: siguiendo el proceso (framework) de abajo y generando desde el listado de ideas.\n+\n+## CONTENIDOS IDEAS\n+\n+Para después tenemos un gran listado de IDEAS en [CONTENIDO](./CONTENIDO.md) para ir ordenando Y procesando.\n+\n+\n+## FRAMEWORK CONTENIDO\n+\n+- Antes de generar las ideas, hay que analizar las metricas de los contenidos anteriores y ver que se puede mejorar o que es más efectivo.\n+\n+**Preparo el guíon**\n+\n+- Hacer un resumen de la idea, el objetivo y el contenido ya pensado. Se puede utilizar un audio y pasarlo por alguna AI para limpiar la idea.\n+- Generar el contenido con el prompt AI [CRONO_generar_contenido](/BRAIN/AI/CRONO_generar_contenido.md).\n+\n+**Grabo el vídeo**\n+\n+- Armar set (Luces: ventana, spot Juli para fondo, foco lectura sobre cama y luz pieza / Usar mic / Trípode sobre cama / Usar doble cámara con las mismas resoluciones)\n+- Grabar vídeos, recortar con Capcut, subir a Drive y generar los enlaces para el editor.\n+\n+**Delego la publicación de contenido**\n+\n+- Escribir una tarea para Prisci con las modificaciones en la WEB si son necesarias.\n+- Escribir una tarea para Prisci si va a llevar publicidad o diseño en la web.\n+- Escribir una orden de contenido (puede ser que requiera dividir el prompt AI), generar html y delegarlo por Whatsapp. Guardar el MD y HTML en CRONO.\n+- Escribir newsletter invitando a leer el contenido.\n+- Eliminar la tarea de CONTENIDOS y confiar en que Juli controla el proceso.\n+\n+\n+## MKT ORDENAR\n+\n+- [ ] [Unlock SEO Potential with AI-Powered Keyword Research](https://neilpatel.com/blog/ai-keyword-research/?utm_medium=email&utm_source=convertkit-list&utm_campaign=us-mkt-campaigns-mql-content-blog-promotional-email&utm_content=blog-promote&utm_term=ck-november-9&cf-last-campaign-source=US%20-%20Mkt%20Campaigns%20-%20MQL%20-%20Content%20-%20Blog%20Promotional%20Email) @next\n+- [ ] Todas las herramientas de Google super configuradas y monitoreadas @next\n+- [ ] https://neilpatel.com/blog/google-analytics-4/\n+- [ ] [Accept the new Terms of Service to upgrade your Google Analytics for Firebase property - Analytics Help](https://support.google.com/analytics/answer/10960488)\n+- [ ] Configurar acceso a Pri también\n+- [ ] Para monitorear temas de SEO [Telescope - Usage-Based SEO Research Tool](https://withtelescope.com/)\n+- [ ] [Guía completa para evitar el contenido duplicado con enlaces canonicals - ▶️ Blog de Marketing Digital - Elabs Consulting](https://www.elabsconsulting.com/noticias/guia-completa-para-evitar-el-contenido-duplicado-con-enlaces-canonicals)\n+- [ ] Comparar como ChatGPT conoce a la empresa\n+- [ ] Más revisiones de SEO a la web y blog\n+- [ ] [Configuring “Schema Markup” in Rank Math » Rank Math](https://rankmath.com/kb/rich-snippets/)\n+- [ ] [AutoOptimize.ai - Automatically Optimize Your Site Conversion Rate](https://www.autooptimize.ai/?coupon=fbspecial)\n+- [ ] [12 Dead Simple Steps To Complete Your First SEO Audit for Free with Rank Math](https://rankmath.com/blog/seo-audit/)\n+- [ ] [Google now shows why it ranked a specific search result](https://searchengineland.com/google-now-shows-why-it-ranked-a-specific-search-result-350659?utm_source=pocket-app&utm_medium=share)\n+- [ ] [INP (Interaction to Next Paint): A Guide To Core Web Vital Changes](https://neilpatel.com/blog/inp/)\n+- [ ] Agregar los productos de Crono a Google Merchant Center\n+- [ ] Ofrecer software y RFID en ML\n+- [ ] Evaluar el mercado usando AI [Home - AI teammates for Your Startup](https://www.frederick.ai/)\n"}, {"date": 1749404010463, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,77 @@\n+# 🏅 ROADS > CRONO > GROWTH BOARD\n+\n+## TODO ORDENAR\n+\n+- [ ] Generar contenido de novedades y para que tenga Prisci, ya está en [CONTENIDO](./CONTENIDO.md)\n+- [ ] Actualizar novedades y recuperar newsletter\n+\n+- [ ] Priscila modificaciones en el sitio web\n+    - Cambios en \"Home\"\n+    - Cambiar el listado de deportes por los 9 deportes principales, supongo que lo mejor es usar 3 líneas y 3 columnas.\n+    - Cambiar los nombres en los logos, siguiendo el listado de deportes, donde dice Gestión completa para ..., por ej. de \"Downhill\" por \"Ciclismo\"\n+    - Intercambiar el orden entre los deportes, dejar como el listado de arriba: Ciclismo, Running, Rally, Motociclismo, Aguas Abiertas, etc.\n+    - Agregar botones de los deportes con el texto: \"Más información de Ciclismo\" que lleve a la página de cada deporte\n+    - Hay que crear un menú en el sitio, antes de Tecnologías, que sea Deportes y que tenga la lista de los 9 deportes principales, cada uno lleva a la página correspondiente. También hay que crear 9 páginas, una para cada deporte, supongo que conviene crear la y duplicarla.\n+\n+- [ ] Mover Apple a la LLC (https://mail.google.com/mail/u/0/#inbox/********************************)\n+- [ ] Reunión Andres Colombia y Preparar Contrato\n+- [ ] Manual de marca en sitio exclusivo\n+- [ ] Registrar la marca\n+\n+---\n+\n+## GROWTH\n+\n+Siguiendo con el plan general 2024-2026, vamos a apuntar a las siguientes estrategias:\n+\n+- *Estrategia SEO/AEO*: para eso vamos a mejorar la arquitectura de metadatos, generar sector de eventos, generar sectir de cursos y sitios.\n+- *Estrategia de deportes*: para eso vamos a generar las páginas en nuestro sitio y los cambios en el sistema.\n+- *Estrategia de contenido*: siguiendo el proceso (framework) de abajo y generando desde el listado de ideas.\n+\n+## CONTENIDOS IDEAS\n+\n+Para después tenemos un gran listado de IDEAS en [CONTENIDO](./CONTENIDO.md) para ir ordenando Y procesando.\n+\n+\n+## FRAMEWORK CONTENIDO\n+\n+- Antes de generar las ideas, hay que analizar las metricas de los contenidos anteriores y ver que se puede mejorar o que es más efectivo.\n+\n+**Preparo el guíon**\n+\n+- Hacer un resumen de la idea, el objetivo y el contenido ya pensado. Se puede utilizar un audio y pasarlo por alguna AI para limpiar la idea.\n+- Generar el contenido con el prompt AI [CRONO_generar_contenido](/BRAIN/AI/CRONO_generar_contenido.md).\n+\n+**Grabo el vídeo**\n+\n+- Armar set (Luces: ventana, spot Juli para fondo, foco lectura sobre cama y luz pieza / Usar mic / Trípode sobre cama / Usar doble cámara con las mismas resoluciones)\n+- Grabar vídeos, recortar con Capcut, subir a Drive y generar los enlaces para el editor.\n+\n+**Delego la publicación de contenido**\n+\n+- Escribir una tarea para Prisci con las modificaciones en la WEB si son necesarias.\n+- Escribir una tarea para Prisci si va a llevar publicidad o diseño en la web.\n+- Escribir una orden de contenido (puede ser que requiera dividir el prompt AI), generar html y delegarlo por Whatsapp. Guardar el MD y HTML en CRONO.\n+- Escribir newsletter invitando a leer el contenido.\n+- Eliminar la tarea de CONTENIDOS y confiar en que Juli controla el proceso.\n+\n+\n+## MKT ORDENAR\n+\n+- [ ] [Unlock SEO Potential with AI-Powered Keyword Research](https://neilpatel.com/blog/ai-keyword-research/?utm_medium=email&utm_source=convertkit-list&utm_campaign=us-mkt-campaigns-mql-content-blog-promotional-email&utm_content=blog-promote&utm_term=ck-november-9&cf-last-campaign-source=US%20-%20Mkt%20Campaigns%20-%20MQL%20-%20Content%20-%20Blog%20Promotional%20Email) @next\n+- [ ] Todas las herramientas de Google super configuradas y monitoreadas @next\n+- [ ] https://neilpatel.com/blog/google-analytics-4/\n+- [ ] [Accept the new Terms of Service to upgrade your Google Analytics for Firebase property - Analytics Help](https://support.google.com/analytics/answer/10960488)\n+- [ ] Configurar acceso a Pri también\n+- [ ] Para monitorear temas de SEO [Telescope - Usage-Based SEO Research Tool](https://withtelescope.com/)\n+- [ ] [Guía completa para evitar el contenido duplicado con enlaces canonicals - ▶️ Blog de Marketing Digital - Elabs Consulting](https://www.elabsconsulting.com/noticias/guia-completa-para-evitar-el-contenido-duplicado-con-enlaces-canonicals)\n+- [ ] Comparar como ChatGPT conoce a la empresa\n+- [ ] Más revisiones de SEO a la web y blog\n+- [ ] [Configuring “Schema Markup” in Rank Math » Rank Math](https://rankmath.com/kb/rich-snippets/)\n+- [ ] [AutoOptimize.ai - Automatically Optimize Your Site Conversion Rate](https://www.autooptimize.ai/?coupon=fbspecial)\n+- [ ] [12 Dead Simple Steps To Complete Your First SEO Audit for Free with Rank Math](https://rankmath.com/blog/seo-audit/)\n+- [ ] [Google now shows why it ranked a specific search result](https://searchengineland.com/google-now-shows-why-it-ranked-a-specific-search-result-350659?utm_source=pocket-app&utm_medium=share)\n+- [ ] [INP (Interaction to Next Paint): A Guide To Core Web Vital Changes](https://neilpatel.com/blog/inp/)\n+- [ ] Agregar los productos de Crono a Google Merchant Center\n+- [ ] Ofrecer software y RFID en ML\n+- [ ] Evaluar el mercado usando AI [Home - AI teammates for Your Startup](https://www.frederick.ai/)\n"}, {"date": 1750076716675, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,8 @@\n # 🏅 ROADS > CRONO > GROWTH BOARD\n \n ## TODO ORDENAR\n \n-- [ ] Generar contenido de novedades y para que tenga Prisci, ya está en [CONTENIDO](./CONTENIDO.md)\n-- [ ] Actualizar novedades y recuperar newsletter\n-\n - [ ] Priscila modificaciones en el sitio web\n     - Cambios en \"Home\"\n     - Cambiar el listado de deportes por los 9 deportes principales, supongo que lo mejor es usar 3 líneas y 3 columnas.\n     - Cambiar los nombres en los logos, siguiendo el listado de deportes, donde dice Gestión completa para ..., por ej. de \"Downhill\" por \"Ciclismo\"\n"}], "date": 1724850139380, "name": "Commit-0", "content": "# 🥇 ROADS > CRONO > GROWTH BOARD\n-------------------------------------------------------------------------------\n\n## MKT\n\nTODO: Definir los milestones de MKT para el sitio web y las redes, para luego convertirlas en tareas a Todoist.\n- [ ] Agregar plan de newsletter, agregar bienvenida\n- [ ] Ahora que has vinculado tu cuenta de Google Analytics a Google Ads, no olvides usar las audiencias que Analytics ha preparado para ti para mostrar anuncios más relevantes. No es necesario implementar código adicional en tu sitio.\n\n## CONTENIDOS\n\nTenemos un gran listado de IDEAS en [IDEAS MKT](./GROWTH%20Ideas%20MKT.md) para ir ordenando Y procesando. Las ideas que se van a convertir en los próximos contenidos las vamos cargando en el [TODO de CONTENIDOS](https://app.todoist.com/app/project/growth-2270118842).\n\nMensualmente se deben seleccionar entre 3 y 6 CONTENIDOS para generar. Cada una se debe pasar por el siguiente proceso:\n\n- Antes de generar las ideas, hay que analizar las metricas de los contenidos anteriores y ver que se puede mejorar o que es más efectivo.\n- Hacer un resumen de la idea, el objetivo y el contenido ya pensado. Se puede utilizar un audio y pasarlo por alguna AI para limpiar la idea.\n- Generar el contenido con el prompt AI [CRONO_generar_contenido](/BRAIN/AI/CRONO_generar_contenido.md).\n- Armar set (Luces: ventana, spot Juli para fondo, foco lectura sobre cama y luz pieza / Usar mic / Trípode sobre cama / Usar doble cámara con las mismas resoluciones)\n- Al grabar el vídeo tener en cuenta (Pasar en limpio):\n  - MrBeast Strategi: https://www.youtube.com/watch?v=F-XZshE_3iA\n  - Tengo bastantes Reels guardados en Instagram\n- Grabar vídeos, recortar con Capcut, subir a Drive y generar los enlaces para el editor.\n- Escribir una tarea para Prisci con las modificaciones en la WEB si son necesarias.\n- Escribir una tarea configurar las métricas en Google Analytics y Search Console.\n- Escribir una tarea para Prisci si va a llevar publicidad o diseño en la web.\n  - Si el contenido es sobre un deporte que podemos ampliar algún mercado, poner publicidad sólo ahí o más ahí que en el resto.\n- Escribir una orden de contenido (puede ser que requiera dividir el prompt AI), generar html y delegarlo por Whatsapp. Guardar el MD y HTML en CRONO.\n- Escribir newsletter invitando a leer el contenido.\n- Eliminar la tarea de CONTENIDOS y confiar en que Juli controla el proceso.\n\n\n## VENTAS\n\nTODO: Desde el plan de Ventas, definir los milestones y los procesos.\n"}]}